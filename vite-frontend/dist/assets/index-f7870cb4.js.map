{"version": 3, "file": "index-f7870cb4.js", "sources": ["../../node_modules/react-dom/client.js", "../../node_modules/lucide-react/dist/esm/shared/src/utils.js", "../../node_modules/lucide-react/dist/esm/defaultAttributes.js", "../../node_modules/lucide-react/dist/esm/Icon.js", "../../node_modules/lucide-react/dist/esm/createLucideIcon.js", "../../node_modules/lucide-react/dist/esm/icons/arrow-left.js", "../../node_modules/lucide-react/dist/esm/icons/menu.js", "../../node_modules/lucide-react/dist/esm/icons/minus.js", "../../node_modules/lucide-react/dist/esm/icons/plus.js", "../../node_modules/lucide-react/dist/esm/icons/search.js", "../../node_modules/lucide-react/dist/esm/icons/shopping-cart.js", "../../node_modules/lucide-react/dist/esm/icons/star.js", "../../node_modules/lucide-react/dist/esm/icons/x.js", "../../node_modules/zustand/esm/vanilla.mjs", "../../node_modules/zustand/esm/react.mjs", "../../node_modules/zustand/esm/middleware.mjs", "../../src/store/cartStore.ts", "../../node_modules/clsx/dist/clsx.m.js", "../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "../../src/utils/index.ts", "../../src/components/ui/Button.tsx", "../../src/components/SearchBar.tsx", "../../src/components/Header.tsx", "../../node_modules/axios/lib/helpers/bind.js", "../../node_modules/axios/lib/utils.js", "../../node_modules/axios/lib/core/AxiosError.js", "../../node_modules/axios/lib/helpers/null.js", "../../node_modules/axios/lib/helpers/toFormData.js", "../../node_modules/axios/lib/helpers/AxiosURLSearchParams.js", "../../node_modules/axios/lib/helpers/buildURL.js", "../../node_modules/axios/lib/core/InterceptorManager.js", "../../node_modules/axios/lib/defaults/transitional.js", "../../node_modules/axios/lib/platform/browser/classes/URLSearchParams.js", "../../node_modules/axios/lib/platform/browser/classes/FormData.js", "../../node_modules/axios/lib/platform/browser/classes/Blob.js", "../../node_modules/axios/lib/platform/browser/index.js", "../../node_modules/axios/lib/platform/common/utils.js", "../../node_modules/axios/lib/platform/index.js", "../../node_modules/axios/lib/helpers/toURLEncodedForm.js", "../../node_modules/axios/lib/helpers/formDataToJSON.js", "../../node_modules/axios/lib/defaults/index.js", "../../node_modules/axios/lib/helpers/parseHeaders.js", "../../node_modules/axios/lib/core/AxiosHeaders.js", "../../node_modules/axios/lib/core/transformData.js", "../../node_modules/axios/lib/cancel/isCancel.js", "../../node_modules/axios/lib/cancel/CanceledError.js", "../../node_modules/axios/lib/core/settle.js", "../../node_modules/axios/lib/helpers/parseProtocol.js", "../../node_modules/axios/lib/helpers/speedometer.js", "../../node_modules/axios/lib/helpers/throttle.js", "../../node_modules/axios/lib/helpers/progressEventReducer.js", "../../node_modules/axios/lib/helpers/isURLSameOrigin.js", "../../node_modules/axios/lib/helpers/cookies.js", "../../node_modules/axios/lib/helpers/isAbsoluteURL.js", "../../node_modules/axios/lib/helpers/combineURLs.js", "../../node_modules/axios/lib/core/buildFullPath.js", "../../node_modules/axios/lib/core/mergeConfig.js", "../../node_modules/axios/lib/helpers/resolveConfig.js", "../../node_modules/axios/lib/adapters/xhr.js", "../../node_modules/axios/lib/helpers/composeSignals.js", "../../node_modules/axios/lib/helpers/trackStream.js", "../../node_modules/axios/lib/adapters/fetch.js", "../../node_modules/axios/lib/adapters/adapters.js", "../../node_modules/axios/lib/core/dispatchRequest.js", "../../node_modules/axios/lib/env/data.js", "../../node_modules/axios/lib/helpers/validator.js", "../../node_modules/axios/lib/core/Axios.js", "../../node_modules/axios/lib/cancel/CancelToken.js", "../../node_modules/axios/lib/helpers/spread.js", "../../node_modules/axios/lib/helpers/isAxiosError.js", "../../node_modules/axios/lib/helpers/HttpStatusCode.js", "../../node_modules/axios/lib/axios.js", "../../src/services/api.ts", "../../src/hooks/useProducts.ts", "../../src/hooks/useCategories.ts", "../../src/components/ui/Card.tsx", "../../src/components/ProductCard.tsx", "../../src/components/ui/Loading.tsx", "../../src/components/ProductGrid.tsx", "../../src/pages/HomePage.tsx", "../../src/pages/ProductDetailPage.tsx", "../../src/App.tsx", "../../src/main.tsx"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m12 19-7-7 7-7\", key: \"1l729n\" }],\n  [\"path\", { d: \"M19 12H5\", key: \"x3x0zl\" }]\n];\nconst ArrowLeft = createLucideIcon(\"arrow-left\", __iconNode);\n\nexport { __iconNode, ArrowLeft as default };\n//# sourceMappingURL=arrow-left.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M4 12h16\", key: \"1lakjw\" }],\n  [\"path\", { d: \"M4 18h16\", key: \"19g7jn\" }],\n  [\"path\", { d: \"M4 6h16\", key: \"1o0s65\" }]\n];\nconst Menu = createLucideIcon(\"menu\", __iconNode);\n\nexport { __iconNode, Menu as default };\n//# sourceMappingURL=menu.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [[\"path\", { d: \"M5 12h14\", key: \"1ays0h\" }]];\nconst Minus = createLucideIcon(\"minus\", __iconNode);\n\nexport { __iconNode, Minus as default };\n//# sourceMappingURL=minus.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M5 12h14\", key: \"1ays0h\" }],\n  [\"path\", { d: \"M12 5v14\", key: \"s699le\" }]\n];\nconst Plus = createLucideIcon(\"plus\", __iconNode);\n\nexport { __iconNode, Plus as default };\n//# sourceMappingURL=plus.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m21 21-4.34-4.34\", key: \"14j7rj\" }],\n  [\"circle\", { cx: \"11\", cy: \"11\", r: \"8\", key: \"4ej97u\" }]\n];\nconst Search = createLucideIcon(\"search\", __iconNode);\n\nexport { __iconNode, Search as default };\n//# sourceMappingURL=search.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"8\", cy: \"21\", r: \"1\", key: \"jimo8o\" }],\n  [\"circle\", { cx: \"19\", cy: \"21\", r: \"1\", key: \"13723u\" }],\n  [\n    \"path\",\n    {\n      d: \"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12\",\n      key: \"9zh506\"\n    }\n  ]\n];\nconst ShoppingCart = createLucideIcon(\"shopping-cart\", __iconNode);\n\nexport { __iconNode, ShoppingCart as default };\n//# sourceMappingURL=shopping-cart.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\",\n      key: \"r04s7s\"\n    }\n  ]\n];\nconst Star = createLucideIcon(\"star\", __iconNode);\n\nexport { __iconNode, Star as default };\n//# sourceMappingURL=star.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M18 6 6 18\", key: \"1bl5f8\" }],\n  [\"path\", { d: \"m6 6 12 12\", key: \"d8bk6v\" }]\n];\nconst X = createLucideIcon(\"x\", __iconNode);\n\nexport { __iconNode, X as default };\n//# sourceMappingURL=x.js.map\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...args) => api.dispatch(...args), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst removeStoreFromTrackedConnections = (name, store) => {\n  if (store === void 0) return;\n  const connectionInfo = trackedConnections.get(name);\n  if (!connectionInfo) return;\n  delete connectionInfo.stores[store];\n  if (Object.keys(connectionInfo.stores).length === 0) {\n    trackedConnections.delete(name);\n  }\n};\nconst findCallerName = (stack) => {\n  var _a, _b;\n  if (!stack) return void 0;\n  const traceLines = stack.split(\"\\n\");\n  const apiSetStateLineIndex = traceLines.findIndex(\n    (traceLine) => traceLine.includes(\"api.setState\")\n  );\n  if (apiSetStateLineIndex < 0) return void 0;\n  const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || \"\";\n  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? {\n      type: anonymousActionType || findCallerName(new Error().stack) || \"anonymous\"\n    } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  api.devtools = {\n    cleanup: () => {\n      if (connection && typeof connection.unsubscribe === \"function\") {\n        connection.unsubscribe();\n      }\n      removeStoreFromTrackedConnections(options.name, store);\n    }\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...args) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && args[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...args);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, fn) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) fn(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nfunction combine(initialState, create) {\n  return (...args) => Object.assign({}, initialState, create(...args));\n}\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { Cart, CartItem, Product } from '@/types';\n\ninterface CartStore extends Cart {\n  // 操作方法\n  addItem: (product: Product, quantity?: number) => void;\n  removeItem: (productId: number) => void;\n  updateQuantity: (productId: number, quantity: number) => void;\n  clearCart: () => void;\n  getItemQuantity: (productId: number) => number;\n  isInCart: (productId: number) => boolean;\n}\n\n// 计算购物车总计\nconst calculateTotals = (items: CartItem[]) => {\n  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);\n  const totalPrice = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);\n  return { totalItems, totalPrice };\n};\n\nexport const useCartStore = create<CartStore>()(\n  persist(\n    (set, get) => ({\n      items: [],\n      totalItems: 0,\n      totalPrice: 0,\n\n      addItem: (product: Product, quantity: number = 1) => {\n        set((state) => {\n          const existingItem = state.items.find(item => item.id === product.id);\n          \n          let newItems: CartItem[];\n          if (existingItem) {\n            // 如果商品已存在，更新数量\n            newItems = state.items.map(item =>\n              item.id === product.id\n                ? { ...item, quantity: item.quantity + quantity }\n                : item\n            );\n          } else {\n            // 如果商品不存在，添加新项目\n            const newItem: CartItem = {\n              id: product.id,\n              product,\n              quantity,\n              selectedAt: new Date().toISOString(),\n            };\n            newItems = [...state.items, newItem];\n          }\n\n          const { totalItems, totalPrice } = calculateTotals(newItems);\n          return {\n            items: newItems,\n            totalItems,\n            totalPrice,\n          };\n        });\n      },\n\n      removeItem: (productId: number) => {\n        set((state) => {\n          const newItems = state.items.filter(item => item.id !== productId);\n          const { totalItems, totalPrice } = calculateTotals(newItems);\n          return {\n            items: newItems,\n            totalItems,\n            totalPrice,\n          };\n        });\n      },\n\n      updateQuantity: (productId: number, quantity: number) => {\n        if (quantity <= 0) {\n          get().removeItem(productId);\n          return;\n        }\n\n        set((state) => {\n          const newItems = state.items.map(item =>\n            item.id === productId\n              ? { ...item, quantity }\n              : item\n          );\n          const { totalItems, totalPrice } = calculateTotals(newItems);\n          return {\n            items: newItems,\n            totalItems,\n            totalPrice,\n          };\n        });\n      },\n\n      clearCart: () => {\n        set({\n          items: [],\n          totalItems: 0,\n          totalPrice: 0,\n        });\n      },\n\n      getItemQuantity: (productId: number) => {\n        const item = get().items.find(item => item.id === productId);\n        return item ? item.quantity : 0;\n      },\n\n      isInCart: (productId: number) => {\n        return get().items.some(item => item.id === productId);\n      },\n    }),\n    {\n      name: 'cart-storage', // localStorage key\n      partialize: (state) => ({\n        items: state.items,\n        totalItems: state.totalItems,\n        totalPrice: state.totalPrice,\n      }),\n    }\n  )\n);\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}export function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "const CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    classGroups\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  for (const classGroupId in classGroups) {\n    processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n  }\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = config => {\n  const {\n    prefix,\n    experimentalParseClassName\n  } = config;\n  /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */\n  let parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let parenDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0 && parenDepth === 0) {\n        if (currentCharacter === MODIFIER_SEPARATOR) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      } else if (currentCharacter === '(') {\n        parenDepth++;\n      } else if (currentCharacter === ')') {\n        parenDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n    const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (prefix) {\n    const fullPrefix = prefix + MODIFIER_SEPARATOR;\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n      isExternal: true,\n      modifiers: [],\n      hasImportantModifier: false,\n      baseClassName: className,\n      maybePostfixModifierPosition: undefined\n    };\n  }\n  if (experimentalParseClassName) {\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => experimentalParseClassName({\n      className,\n      parseClassName: parseClassNameOriginal\n    });\n  }\n  return parseClassName;\n};\nconst stripImportantModifier = baseClassName => {\n  if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(0, baseClassName.length - 1);\n  }\n  /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */\n  if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(1);\n  }\n  return baseClassName;\n};\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst createSortModifiers = config => {\n  const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map(modifier => [modifier, true]));\n  const sortModifiers = modifiers => {\n    if (modifiers.length <= 1) {\n      return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach(modifier => {\n      const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n      if (isPositionSensitive) {\n        sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n        unsortedModifiers = [];\n      } else {\n        unsortedModifiers.push(modifier);\n      }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n  };\n  return sortModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  sortModifiers: createSortModifiers(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds,\n    sortModifiers\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      isExternal,\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    if (isExternal) {\n      result = originalClassName + (result.length > 0 ? ' ' + result : result);\n      continue;\n    }\n    let hasPostfixModifier = !!maybePostfixModifierPosition;\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = value => fractionRegex.test(value);\nconst isNumber = value => !!value && !Number.isNaN(Number(value));\nconst isInteger = value => !!value && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst isAny = () => true;\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst isAnyNonArbitrary = value => !isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = value => getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = value => getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = value => getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = value => arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = value => getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = value => getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = value => getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = value => getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = value => getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = value => getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false) => {\n  const result = arbitraryVariableRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return shouldMatchNoLabel;\n  }\n  return false;\n};\n// Labels\nconst isLabelPosition = label => label === 'position' || label === 'percentage';\nconst isLabelImage = label => label === 'image' || label === 'url';\nconst isLabelSize = label => label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = label => label === 'length';\nconst isLabelNumber = label => label === 'number';\nconst isLabelFamilyName = label => label === 'family-name';\nconst isLabelShadow = label => label === 'shadow';\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isAnyNonArbitrary,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isArbitraryVariable,\n  isArbitraryVariableFamilyName,\n  isArbitraryVariableImage,\n  isArbitraryVariableLength,\n  isArbitraryVariablePosition,\n  isArbitraryVariableShadow,\n  isArbitraryVariableSize,\n  isFraction,\n  isInteger,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */\n  /***/\n  const themeColor = fromTheme('color');\n  const themeFont = fromTheme('font');\n  const themeText = fromTheme('text');\n  const themeFontWeight = fromTheme('font-weight');\n  const themeTracking = fromTheme('tracking');\n  const themeLeading = fromTheme('leading');\n  const themeBreakpoint = fromTheme('breakpoint');\n  const themeContainer = fromTheme('container');\n  const themeSpacing = fromTheme('spacing');\n  const themeRadius = fromTheme('radius');\n  const themeShadow = fromTheme('shadow');\n  const themeInsetShadow = fromTheme('inset-shadow');\n  const themeTextShadow = fromTheme('text-shadow');\n  const themeDropShadow = fromTheme('drop-shadow');\n  const themeBlur = fromTheme('blur');\n  const themePerspective = fromTheme('perspective');\n  const themeAspect = fromTheme('aspect');\n  const themeEase = fromTheme('ease');\n  const themeAnimate = fromTheme('animate');\n  /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */\n  /***/\n  const scaleBreak = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const scalePosition = () => ['center', 'top', 'bottom', 'left', 'right', 'top-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-top', 'top-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-top', 'bottom-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-bottom', 'bottom-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-bottom'];\n  const scalePositionWithArbitrary = () => [...scalePosition(), isArbitraryVariable, isArbitraryValue];\n  const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const scaleOverscroll = () => ['auto', 'contain', 'none'];\n  const scaleUnambiguousSpacing = () => [isArbitraryVariable, isArbitraryValue, themeSpacing];\n  const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()];\n  const scaleGridTemplateColsRows = () => [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartAndEnd = () => ['auto', {\n    span: ['full', isInteger, isArbitraryVariable, isArbitraryValue]\n  }, isInteger, isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartOrEnd = () => [isInteger, 'auto', isArbitraryVariable, isArbitraryValue];\n  const scaleGridAutoColsRows = () => ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue];\n  const scaleAlignPrimaryAxis = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch', 'baseline', 'center-safe', 'end-safe'];\n  const scaleAlignSecondaryAxis = () => ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'];\n  const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()];\n  const scaleSizing = () => [isFraction, 'auto', 'full', 'dvw', 'dvh', 'lvw', 'lvh', 'svw', 'svh', 'min', 'max', 'fit', ...scaleUnambiguousSpacing()];\n  const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue];\n  const scaleBgPosition = () => [...scalePosition(), isArbitraryVariablePosition, isArbitraryPosition, {\n    position: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleBgRepeat = () => ['no-repeat', {\n    repeat: ['', 'x', 'y', 'space', 'round']\n  }];\n  const scaleBgSize = () => ['auto', 'cover', 'contain', isArbitraryVariableSize, isArbitrarySize, {\n    size: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleGradientStopPosition = () => [isPercent, isArbitraryVariableLength, isArbitraryLength];\n  const scaleRadius = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', 'full', themeRadius, isArbitraryVariable, isArbitraryValue];\n  const scaleBorderWidth = () => ['', isNumber, isArbitraryVariableLength, isArbitraryLength];\n  const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'];\n  const scaleBlendMode = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const scaleMaskImagePosition = () => [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition];\n  const scaleBlur = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', themeBlur, isArbitraryVariable, isArbitraryValue];\n  const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()];\n  return {\n    cacheSize: 500,\n    theme: {\n      animate: ['spin', 'ping', 'pulse', 'bounce'],\n      aspect: ['video'],\n      blur: [isTshirtSize],\n      breakpoint: [isTshirtSize],\n      color: [isAny],\n      container: [isTshirtSize],\n      'drop-shadow': [isTshirtSize],\n      ease: ['in', 'out', 'in-out'],\n      font: [isAnyNonArbitrary],\n      'font-weight': ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],\n      'inset-shadow': [isTshirtSize],\n      leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n      perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n      radius: [isTshirtSize],\n      shadow: [isTshirtSize],\n      spacing: ['px', isNumber],\n      text: [isTshirtSize],\n      'text-shadow': [isTshirtSize],\n      tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest']\n    },\n    classGroups: {\n      // --------------\n      // --- Layout ---\n      // --------------\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', isFraction, isArbitraryValue, isArbitraryVariable, themeAspect]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': scaleBreak()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': scaleBreak()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: scalePositionWithArbitrary()\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: scaleOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': scaleOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': scaleOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': scaleOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: scaleInset()\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': scaleInset()\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': scaleInset()\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: scaleInset()\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: scaleInset()\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: scaleInset()\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: scaleInset()\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: scaleInset()\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: scaleInset()\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------------\n      // --- Flexbox and Grid ---\n      // ------------------------\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: [isFraction, 'full', 'auto', themeContainer, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['nowrap', 'wrap', 'wrap-reverse']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: [isInteger, 'first', 'last', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': scaleGridAutoColsRows()\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': scaleGridAutoColsRows()\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: [...scaleAlignPrimaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': [...scaleAlignSecondaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...scaleAlignPrimaryAxis()]\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: [...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', ...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': scaleAlignPrimaryAxis()\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': [...scaleAlignSecondaryAxis(), 'baseline']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: scaleMargin()\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: scaleMargin()\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: scaleMargin()\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: scaleMargin()\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: scaleMargin()\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: scaleMargin()\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: scaleMargin()\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: scaleMargin()\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: scaleMargin()\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x': [{\n        'space-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y': [{\n        'space-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // --------------\n      // --- Sizing ---\n      // --------------\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */\n      size: [{\n        size: scaleSizing()\n      }],\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: [themeContainer, 'screen', ...scaleSizing()]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [themeContainer, 'screen', /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [themeContainer, 'screen', 'none', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'prose', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        {\n          screen: [themeBreakpoint]\n        }, ...scaleSizing()]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: ['screen', 'lh', ...scaleSizing()]\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': ['screen', 'lh', 'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': ['screen', 'lh', ...scaleSizing()]\n      }],\n      // ------------------\n      // --- Typography ---\n      // ------------------\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */\n      'font-stretch': [{\n        'font-stretch': ['ultra-condensed', 'extra-condensed', 'condensed', 'semi-condensed', 'normal', 'semi-expanded', 'expanded', 'extra-expanded', 'ultra-expanded', isPercent, isArbitraryValue]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: [themeTracking, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: [/** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        themeLeading, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: scaleColor()\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: scaleColor()\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...scaleLineStyle(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: [isNumber, 'from-font', 'auto', isArbitraryVariable, isArbitraryLength]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: scaleColor()\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */\n      wrap: [{\n        wrap: ['break-word', 'anywhere', 'normal']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -------------------\n      // --- Backgrounds ---\n      // -------------------\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: scaleBgPosition()\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: scaleBgRepeat()\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: scaleBgSize()\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          linear: [{\n            to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n          }, isInteger, isArbitraryVariable, isArbitraryValue],\n          radial: ['', isArbitraryVariable, isArbitraryValue],\n          conic: [isInteger, isArbitraryVariable, isArbitraryValue]\n        }, isArbitraryVariableImage, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: scaleColor()\n      }],\n      // ---------------\n      // --- Borders ---\n      // ---------------\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: scaleRadius()\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': scaleRadius()\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': scaleRadius()\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': scaleRadius()\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': scaleRadius()\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': scaleRadius()\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': scaleRadius()\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': scaleRadius()\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': scaleRadius()\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': scaleRadius()\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: scaleBorderWidth()\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': scaleBorderWidth()\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x': [{\n        'divide-x': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y': [{\n        'divide-y': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */\n      'divide-style': [{\n        divide: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: scaleColor()\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': scaleColor()\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': scaleColor()\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': scaleColor()\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': scaleColor()\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': scaleColor()\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': scaleColor()\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': scaleColor()\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': scaleColor()\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: scaleColor()\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: [...scaleLineStyle(), 'none', 'hidden']\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: scaleColor()\n      }],\n      // ---------------\n      // --- Effects ---\n      // ---------------\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */\n      'shadow-color': [{\n        shadow: scaleColor()\n      }],\n      /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */\n      'inset-shadow': [{\n        'inset-shadow': ['none', themeInsetShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */\n      'inset-shadow-color': [{\n        'inset-shadow': scaleColor()\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */\n      'ring-w': [{\n        ring: scaleBorderWidth()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */\n      'ring-color': [{\n        ring: scaleColor()\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isNumber, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-color': [{\n        'ring-offset': scaleColor()\n      }],\n      /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */\n      'inset-ring-w': [{\n        'inset-ring': scaleBorderWidth()\n      }],\n      /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */\n      'inset-ring-color': [{\n        'inset-ring': scaleColor()\n      }],\n      /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */\n      'text-shadow': [{\n        'text-shadow': ['none', themeTextShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */\n      'text-shadow-color': [{\n        'text-shadow': scaleColor()\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': scaleBlendMode()\n      }],\n      /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */\n      'mask-clip': [{\n        'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }, 'mask-no-clip'],\n      /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */\n      'mask-composite': [{\n        mask: ['add', 'subtract', 'intersect', 'exclude']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image-linear-pos': [{\n        'mask-linear': [isNumber]\n      }],\n      'mask-image-linear-from-pos': [{\n        'mask-linear-from': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-to-pos': [{\n        'mask-linear-to': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-from-color': [{\n        'mask-linear-from': scaleColor()\n      }],\n      'mask-image-linear-to-color': [{\n        'mask-linear-to': scaleColor()\n      }],\n      'mask-image-t-from-pos': [{\n        'mask-t-from': scaleMaskImagePosition()\n      }],\n      'mask-image-t-to-pos': [{\n        'mask-t-to': scaleMaskImagePosition()\n      }],\n      'mask-image-t-from-color': [{\n        'mask-t-from': scaleColor()\n      }],\n      'mask-image-t-to-color': [{\n        'mask-t-to': scaleColor()\n      }],\n      'mask-image-r-from-pos': [{\n        'mask-r-from': scaleMaskImagePosition()\n      }],\n      'mask-image-r-to-pos': [{\n        'mask-r-to': scaleMaskImagePosition()\n      }],\n      'mask-image-r-from-color': [{\n        'mask-r-from': scaleColor()\n      }],\n      'mask-image-r-to-color': [{\n        'mask-r-to': scaleColor()\n      }],\n      'mask-image-b-from-pos': [{\n        'mask-b-from': scaleMaskImagePosition()\n      }],\n      'mask-image-b-to-pos': [{\n        'mask-b-to': scaleMaskImagePosition()\n      }],\n      'mask-image-b-from-color': [{\n        'mask-b-from': scaleColor()\n      }],\n      'mask-image-b-to-color': [{\n        'mask-b-to': scaleColor()\n      }],\n      'mask-image-l-from-pos': [{\n        'mask-l-from': scaleMaskImagePosition()\n      }],\n      'mask-image-l-to-pos': [{\n        'mask-l-to': scaleMaskImagePosition()\n      }],\n      'mask-image-l-from-color': [{\n        'mask-l-from': scaleColor()\n      }],\n      'mask-image-l-to-color': [{\n        'mask-l-to': scaleColor()\n      }],\n      'mask-image-x-from-pos': [{\n        'mask-x-from': scaleMaskImagePosition()\n      }],\n      'mask-image-x-to-pos': [{\n        'mask-x-to': scaleMaskImagePosition()\n      }],\n      'mask-image-x-from-color': [{\n        'mask-x-from': scaleColor()\n      }],\n      'mask-image-x-to-color': [{\n        'mask-x-to': scaleColor()\n      }],\n      'mask-image-y-from-pos': [{\n        'mask-y-from': scaleMaskImagePosition()\n      }],\n      'mask-image-y-to-pos': [{\n        'mask-y-to': scaleMaskImagePosition()\n      }],\n      'mask-image-y-from-color': [{\n        'mask-y-from': scaleColor()\n      }],\n      'mask-image-y-to-color': [{\n        'mask-y-to': scaleColor()\n      }],\n      'mask-image-radial': [{\n        'mask-radial': [isArbitraryVariable, isArbitraryValue]\n      }],\n      'mask-image-radial-from-pos': [{\n        'mask-radial-from': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-to-pos': [{\n        'mask-radial-to': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-from-color': [{\n        'mask-radial-from': scaleColor()\n      }],\n      'mask-image-radial-to-color': [{\n        'mask-radial-to': scaleColor()\n      }],\n      'mask-image-radial-shape': [{\n        'mask-radial': ['circle', 'ellipse']\n      }],\n      'mask-image-radial-size': [{\n        'mask-radial': [{\n          closest: ['side', 'corner'],\n          farthest: ['side', 'corner']\n        }]\n      }],\n      'mask-image-radial-pos': [{\n        'mask-radial-at': scalePosition()\n      }],\n      'mask-image-conic-pos': [{\n        'mask-conic': [isNumber]\n      }],\n      'mask-image-conic-from-pos': [{\n        'mask-conic-from': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-to-pos': [{\n        'mask-conic-to': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-from-color': [{\n        'mask-conic-from': scaleColor()\n      }],\n      'mask-image-conic-to-color': [{\n        'mask-conic-to': scaleColor()\n      }],\n      /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */\n      'mask-mode': [{\n        mask: ['alpha', 'luminance', 'match']\n      }],\n      /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */\n      'mask-origin': [{\n        'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }],\n      /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */\n      'mask-position': [{\n        mask: scaleBgPosition()\n      }],\n      /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */\n      'mask-repeat': [{\n        mask: scaleBgRepeat()\n      }],\n      /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */\n      'mask-size': [{\n        mask: scaleBgSize()\n      }],\n      /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */\n      'mask-type': [{\n        'mask-type': ['alpha', 'luminance']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image': [{\n        mask: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ---------------\n      // --- Filters ---\n      // ---------------\n      /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: scaleBlur()\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeDropShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */\n      'drop-shadow-color': [{\n        'drop-shadow': scaleColor()\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': scaleBlur()\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      // --------------\n      // --- Tables ---\n      // --------------\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // ---------------------------------\n      // --- Transitions and Animation ---\n      // ---------------------------------\n      /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['', 'all', 'colors', 'opacity', 'shadow', 'transform', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */\n      'transition-behavior': [{\n        transition: ['normal', 'discrete']\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------\n      // --- Transforms ---\n      // ------------------\n      /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */\n      backface: [{\n        backface: ['hidden', 'visible']\n      }],\n      /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */\n      perspective: [{\n        perspective: [themePerspective, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */\n      'perspective-origin': [{\n        'perspective-origin': scalePositionWithArbitrary()\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: scaleRotate()\n      }],\n      /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-x': [{\n        'rotate-x': scaleRotate()\n      }],\n      /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-y': [{\n        'rotate-y': scaleRotate()\n      }],\n      /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-z': [{\n        'rotate-z': scaleRotate()\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: scaleScale()\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': scaleScale()\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': scaleScale()\n      }],\n      /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-z': [{\n        'scale-z': scaleScale()\n      }],\n      /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-3d': ['scale-3d'],\n      /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */\n      skew: [{\n        skew: scaleSkew()\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': scaleSkew()\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': scaleSkew()\n      }],\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu']\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: scalePositionWithArbitrary()\n      }],\n      /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */\n      'transform-style': [{\n        transform: ['3d', 'flat']\n      }],\n      /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */\n      translate: [{\n        translate: scaleTranslate()\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': scaleTranslate()\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': scaleTranslate()\n      }],\n      /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-z': [{\n        'translate-z': scaleTranslate()\n      }],\n      /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-none': ['translate-none'],\n      // ---------------------\n      // --- Interactivity ---\n      // ---------------------\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: scaleColor()\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: scaleColor()\n      }],\n      /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */\n      'color-scheme': [{\n        scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */\n      'field-sizing': [{\n        'field-sizing': ['fixed', 'content']\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['auto', 'none']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', '', 'y', 'x']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -----------\n      // --- SVG ---\n      // -----------\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: ['none', ...scaleColor()]\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isNumber, isArbitraryVariableLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: ['none', ...scaleColor()]\n      }],\n      // ---------------------\n      // --- Accessibility ---\n      // ---------------------\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-x', 'border-w-y', 'border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-x', 'border-color-y', 'border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      translate: ['translate-x', 'translate-y', 'translate-none'],\n      'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    },\n    orderSensitiveModifiers: ['*', '**', 'after', 'backdrop', 'before', 'details-content', 'file', 'first-letter', 'first-line', 'marker', 'placeholder', 'selection']\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  overrideConfigProperties(baseConfig.theme, override.theme);\n  overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n  overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n  mergeConfigProperties(baseConfig.theme, extend.theme);\n  mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n  mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      mergeArrayProperties(baseObject, mergeObject, key);\n    }\n  }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key) => {\n  const mergeValue = mergeObject[key];\n  if (mergeValue !== undefined) {\n    baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\nexport { createTailwindMerge, extendTailwindMerge, fromTheme, getDefaultConfig, mergeConfigs, twJoin, twMerge, validators };\n//# sourceMappingURL=bundle-mjs.mjs.map\n", "import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\n// 合并Tailwind CSS类名\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// 格式化价格\nexport function formatPrice(price: number, currency: string = '¥'): string {\n  return `${currency}${price.toFixed(2)}`;\n}\n\n// 格式化数字（添加千分位分隔符）\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('zh-CN');\n}\n\n// 计算折扣百分比\nexport function calculateDiscount(originalPrice: number, currentPrice: number): number {\n  if (originalPrice <= currentPrice) return 0;\n  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);\n}\n\n// 格式化日期\nexport function formatDate(date: string | Date, format: 'short' | 'long' = 'short'): string {\n  const d = new Date(date);\n  const options: Intl.DateTimeFormatOptions = format === 'long' \n    ? { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' }\n    : { year: 'numeric', month: '2-digit', day: '2-digit' };\n  \n  return d.toLocaleDateString('zh-CN', options);\n}\n\n// 生成随机ID\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n// 防抖函数\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: number;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 节流函数\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// 深拷贝\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj;\n  if (obj instanceof Date) return new Date(obj.getTime()) as T;\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T;\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T;\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key]);\n      }\n    }\n    return clonedObj;\n  }\n  return obj;\n}\n\n// 检查是否为移动设备\nexport function isMobile(): boolean {\n  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n\n// 滚动到顶部\nexport function scrollToTop(smooth: boolean = true): void {\n  window.scrollTo({\n    top: 0,\n    behavior: smooth ? 'smooth' : 'auto'\n  });\n}\n\n// 滚动到元素\nexport function scrollToElement(elementId: string, smooth: boolean = true): void {\n  const element = document.getElementById(elementId);\n  if (element) {\n    element.scrollIntoView({\n      behavior: smooth ? 'smooth' : 'auto',\n      block: 'start'\n    });\n  }\n}\n\n// 获取图片占位符URL\nexport function getImagePlaceholder(width: number = 300, height: number = 300): string {\n  return `https://via.placeholder.com/${width}x${height}/f3f4f6/9ca3af?text=Loading`;\n}\n\n// 验证邮箱格式\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// 验证手机号格式（中国大陆）\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^1[3-9]\\d{9}$/;\n  return phoneRegex.test(phone);\n}\n\n// 截断文本\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\n// 生成评分星星数组\nexport function generateStars(rating: number, maxStars: number = 5): boolean[] {\n  return Array.from({ length: maxStars }, (_, index) => index < Math.floor(rating));\n}\n\n// 本地存储工具\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue || null;\n    } catch {\n      return defaultValue || null;\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error);\n    }\n  },\n  \n  remove: (key: string): void => {\n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Failed to remove from localStorage:', error);\n    }\n  },\n  \n  clear: (): void => {\n    try {\n      localStorage.clear();\n    } catch (error) {\n      console.error('Failed to clear localStorage:', error);\n    }\n  }\n};\n", "import React from 'react';\nimport { cn } from '@/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  disabled,\n  className,\n  children,\n  ...props\n}) => {\n  const baseClasses = 'btn';\n  const variantClasses: Record<string, string> = {\n    primary: 'btn-primary',\n    secondary: 'btn-secondary',\n    outline: 'btn-outline',\n    ghost: 'btn-ghost',\n    danger: 'btn-danger',\n  };\n  const sizeClasses: Record<string, string> = {\n    sm: 'btn-sm',\n    md: 'btn-md',\n    lg: 'btn-lg',\n  };\n\n  return (\n    <button\n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        sizeClasses[size],\n        loading && 'opacity-50 cursor-not-allowed',\n        className\n      )}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <div className=\"loading-spinner w-4 h-4 mr-2\" />\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n", "import React, { useState, useCallback } from 'react';\nimport { Search, X } from 'lucide-react';\nimport { debounce } from '@/utils';\nimport { SearchBarProps } from '@/types';\nimport Button from './ui/Button';\n\nconst SearchBar: React.FC<SearchBarProps> = ({\n  onSearch,\n  placeholder = \"搜索商品...\",\n  defaultValue = \"\",\n  className,\n}) => {\n  const [keyword, setKeyword] = useState(defaultValue);\n\n  // 防抖搜索\n  const debouncedSearch = useCallback(\n    debounce((value: string) => {\n      onSearch(value.trim());\n    }, 300),\n    [onSearch]\n  );\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setKeyword(value);\n    debouncedSearch(value);\n  };\n\n  const handleClear = () => {\n    setKeyword('');\n    onSearch('');\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSearch(keyword.trim());\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className={className}>\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <Search className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        \n        <input\n          type=\"text\"\n          value={keyword}\n          onChange={handleInputChange}\n          placeholder={placeholder}\n          className=\"input pl-10 pr-10 w-full\"\n        />\n        \n        {keyword && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleClear}\n              className=\"p-1 hover:bg-gray-100 rounded-full\"\n            >\n              <X className=\"h-4 w-4 text-gray-400\" />\n            </Button>\n          </div>\n        )}\n      </div>\n    </form>\n  );\n};\n\nexport default SearchBar;\n", "import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { ShoppingCart, Menu, X } from 'lucide-react';\nimport { useCartStore } from '@/store/cartStore';\nimport { formatPrice } from '@/utils';\nimport { HeaderProps } from '@/types';\nimport SearchBar from './SearchBar';\nimport Button from './ui/Button';\n\nconst Header: React.FC<HeaderProps> = ({ onSearch }) => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { totalItems, totalPrice } = useCartStore();\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b sticky top-0 z-40\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link to=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">🛍️</span>\n              </div>\n              <span className=\"font-bold text-xl text-gray-900 hidden sm:block\">\n                商品展示\n              </span>\n            </Link>\n          </div>\n\n          {/* 搜索框 - 桌面版 */}\n          <div className=\"hidden md:block flex-1 max-w-lg mx-8\">\n            <SearchBar onSearch={onSearch} />\n          </div>\n\n          {/* 右侧操作 */}\n          <div className=\"flex items-center space-x-4\">\n            {/* 购物车 */}\n            <Link to=\"/cart\" className=\"relative\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"p-2\">\n                <ShoppingCart className=\"w-6 h-6\" />\n                {totalItems > 0 && (\n                  <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                    {totalItems > 99 ? '99+' : totalItems}\n                  </span>\n                )}\n              </Button>\n            </Link>\n\n            {/* 移动端菜单按钮 */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"md:hidden p-2\"\n              onClick={toggleMobileMenu}\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* 移动端搜索框 */}\n        <div className=\"md:hidden pb-4\">\n          <SearchBar onSearch={onSearch} />\n        </div>\n      </div>\n\n      {/* 移动端菜单 */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden bg-white border-t\">\n          <div className=\"px-4 py-4 space-y-4\">\n            <Link\n              to=\"/\"\n              className=\"block text-gray-700 hover:text-primary-600 font-medium\"\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              首页\n            </Link>\n            <Link\n              to=\"/categories\"\n              className=\"block text-gray-700 hover:text-primary-600 font-medium\"\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              分类\n            </Link>\n            <Link\n              to=\"/cart\"\n              className=\"flex items-center justify-between text-gray-700 hover:text-primary-600 font-medium\"\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              <span>购物车</span>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-500\">\n                  {totalItems} 件商品\n                </span>\n                <span className=\"text-sm font-medium text-primary-600\">\n                  {formatPrice(totalPrice)}\n                </span>\n              </div>\n            </Link>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n", "'use strict';\n\nexport default function bind(fn, thisArg) {\n  return function wrap() {\n    return fn.apply(thisArg, arguments);\n  };\n}\n", "'use strict';\n\nimport bind from './helpers/bind.js';\n\n// utils is a library of generic helper functions non-specific to axios\n\nconst {toString} = Object.prototype;\nconst {getPrototypeOf} = Object;\nconst {iterator, toStringTag} = Symbol;\n\nconst kindOf = (cache => thing => {\n    const str = toString.call(thing);\n    return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n})(Object.create(null));\n\nconst kindOfTest = (type) => {\n  type = type.toLowerCase();\n  return (thing) => kindOf(thing) === type\n}\n\nconst typeOfTest = type => thing => typeof thing === type;\n\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n *\n * @returns {boolean} True if value is an Array, otherwise false\n */\nconst {isArray} = Array;\n\n/**\n * Determine if a value is undefined\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if the value is undefined, otherwise false\n */\nconst isUndefined = typeOfTest('undefined');\n\n/**\n * Determine if a value is a Buffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Buffer, otherwise false\n */\nfunction isBuffer(val) {\n  return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)\n    && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);\n}\n\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */\nconst isArrayBuffer = kindOfTest('ArrayBuffer');\n\n\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */\nfunction isArrayBufferView(val) {\n  let result;\n  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {\n    result = ArrayBuffer.isView(val);\n  } else {\n    result = (val) && (val.buffer) && (isArrayBuffer(val.buffer));\n  }\n  return result;\n}\n\n/**\n * Determine if a value is a String\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a String, otherwise false\n */\nconst isString = typeOfTest('string');\n\n/**\n * Determine if a value is a Function\n *\n * @param {*} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */\nconst isFunction = typeOfTest('function');\n\n/**\n * Determine if a value is a Number\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Number, otherwise false\n */\nconst isNumber = typeOfTest('number');\n\n/**\n * Determine if a value is an Object\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an Object, otherwise false\n */\nconst isObject = (thing) => thing !== null && typeof thing === 'object';\n\n/**\n * Determine if a value is a Boolean\n *\n * @param {*} thing The value to test\n * @returns {boolean} True if value is a Boolean, otherwise false\n */\nconst isBoolean = thing => thing === true || thing === false;\n\n/**\n * Determine if a value is a plain Object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a plain Object, otherwise false\n */\nconst isPlainObject = (val) => {\n  if (kindOf(val) !== 'object') {\n    return false;\n  }\n\n  const prototype = getPrototypeOf(val);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(toStringTag in val) && !(iterator in val);\n}\n\n/**\n * Determine if a value is a Date\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Date, otherwise false\n */\nconst isDate = kindOfTest('Date');\n\n/**\n * Determine if a value is a File\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nconst isFile = kindOfTest('File');\n\n/**\n * Determine if a value is a Blob\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Blob, otherwise false\n */\nconst isBlob = kindOfTest('Blob');\n\n/**\n * Determine if a value is a FileList\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */\nconst isFileList = kindOfTest('FileList');\n\n/**\n * Determine if a value is a Stream\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Stream, otherwise false\n */\nconst isStream = (val) => isObject(val) && isFunction(val.pipe);\n\n/**\n * Determine if a value is a FormData\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an FormData, otherwise false\n */\nconst isFormData = (thing) => {\n  let kind;\n  return thing && (\n    (typeof FormData === 'function' && thing instanceof FormData) || (\n      isFunction(thing.append) && (\n        (kind = kindOf(thing)) === 'formdata' ||\n        // detect form-data instance\n        (kind === 'object' && isFunction(thing.toString) && thing.toString() === '[object FormData]')\n      )\n    )\n  )\n}\n\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */\nconst isURLSearchParams = kindOfTest('URLSearchParams');\n\nconst [isReadableStream, isRequest, isResponse, isHeaders] = ['ReadableStream', 'Request', 'Response', 'Headers'].map(kindOfTest);\n\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n *\n * @returns {String} The String freed of excess whitespace\n */\nconst trim = (str) => str.trim ?\n  str.trim() : str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n *\n * @param {Boolean} [allOwnKeys = false]\n * @returns {any}\n */\nfunction forEach(obj, fn, {allOwnKeys = false} = {}) {\n  // Don't bother if no value provided\n  if (obj === null || typeof obj === 'undefined') {\n    return;\n  }\n\n  let i;\n  let l;\n\n  // Force an array if not already something iterable\n  if (typeof obj !== 'object') {\n    /*eslint no-param-reassign:0*/\n    obj = [obj];\n  }\n\n  if (isArray(obj)) {\n    // Iterate over array values\n    for (i = 0, l = obj.length; i < l; i++) {\n      fn.call(null, obj[i], i, obj);\n    }\n  } else {\n    // Iterate over object keys\n    const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);\n    const len = keys.length;\n    let key;\n\n    for (i = 0; i < len; i++) {\n      key = keys[i];\n      fn.call(null, obj[key], key, obj);\n    }\n  }\n}\n\nfunction findKey(obj, key) {\n  key = key.toLowerCase();\n  const keys = Object.keys(obj);\n  let i = keys.length;\n  let _key;\n  while (i-- > 0) {\n    _key = keys[i];\n    if (key === _key.toLowerCase()) {\n      return _key;\n    }\n  }\n  return null;\n}\n\nconst _global = (() => {\n  /*eslint no-undef:0*/\n  if (typeof globalThis !== \"undefined\") return globalThis;\n  return typeof self !== \"undefined\" ? self : (typeof window !== 'undefined' ? window : global)\n})();\n\nconst isContextDefined = (context) => !isUndefined(context) && context !== _global;\n\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n *\n * @returns {Object} Result of all merge properties\n */\nfunction merge(/* obj1, obj2, obj3, ... */) {\n  const {caseless} = isContextDefined(this) && this || {};\n  const result = {};\n  const assignValue = (val, key) => {\n    const targetKey = caseless && findKey(result, key) || key;\n    if (isPlainObject(result[targetKey]) && isPlainObject(val)) {\n      result[targetKey] = merge(result[targetKey], val);\n    } else if (isPlainObject(val)) {\n      result[targetKey] = merge({}, val);\n    } else if (isArray(val)) {\n      result[targetKey] = val.slice();\n    } else {\n      result[targetKey] = val;\n    }\n  }\n\n  for (let i = 0, l = arguments.length; i < l; i++) {\n    arguments[i] && forEach(arguments[i], assignValue);\n  }\n  return result;\n}\n\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n *\n * @param {Boolean} [allOwnKeys]\n * @returns {Object} The resulting value of object a\n */\nconst extend = (a, b, thisArg, {allOwnKeys}= {}) => {\n  forEach(b, (val, key) => {\n    if (thisArg && isFunction(val)) {\n      a[key] = bind(val, thisArg);\n    } else {\n      a[key] = val;\n    }\n  }, {allOwnKeys});\n  return a;\n}\n\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n *\n * @returns {string} content value without BOM\n */\nconst stripBOM = (content) => {\n  if (content.charCodeAt(0) === 0xFEFF) {\n    content = content.slice(1);\n  }\n  return content;\n}\n\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n *\n * @returns {void}\n */\nconst inherits = (constructor, superConstructor, props, descriptors) => {\n  constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n  constructor.prototype.constructor = constructor;\n  Object.defineProperty(constructor, 'super', {\n    value: superConstructor.prototype\n  });\n  props && Object.assign(constructor.prototype, props);\n}\n\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function|Boolean} [filter]\n * @param {Function} [propFilter]\n *\n * @returns {Object}\n */\nconst toFlatObject = (sourceObj, destObj, filter, propFilter) => {\n  let props;\n  let i;\n  let prop;\n  const merged = {};\n\n  destObj = destObj || {};\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  if (sourceObj == null) return destObj;\n\n  do {\n    props = Object.getOwnPropertyNames(sourceObj);\n    i = props.length;\n    while (i-- > 0) {\n      prop = props[i];\n      if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {\n        destObj[prop] = sourceObj[prop];\n        merged[prop] = true;\n      }\n    }\n    sourceObj = filter !== false && getPrototypeOf(sourceObj);\n  } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n\n  return destObj;\n}\n\n/**\n * Determines whether a string ends with the characters of a specified string\n *\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n *\n * @returns {boolean}\n */\nconst endsWith = (str, searchString, position) => {\n  str = String(str);\n  if (position === undefined || position > str.length) {\n    position = str.length;\n  }\n  position -= searchString.length;\n  const lastIndex = str.indexOf(searchString, position);\n  return lastIndex !== -1 && lastIndex === position;\n}\n\n\n/**\n * Returns new array from array like object or null if failed\n *\n * @param {*} [thing]\n *\n * @returns {?Array}\n */\nconst toArray = (thing) => {\n  if (!thing) return null;\n  if (isArray(thing)) return thing;\n  let i = thing.length;\n  if (!isNumber(i)) return null;\n  const arr = new Array(i);\n  while (i-- > 0) {\n    arr[i] = thing[i];\n  }\n  return arr;\n}\n\n/**\n * Checking if the Uint8Array exists and if it does, it returns a function that checks if the\n * thing passed in is an instance of Uint8Array\n *\n * @param {TypedArray}\n *\n * @returns {Array}\n */\n// eslint-disable-next-line func-names\nconst isTypedArray = (TypedArray => {\n  // eslint-disable-next-line func-names\n  return thing => {\n    return TypedArray && thing instanceof TypedArray;\n  };\n})(typeof Uint8Array !== 'undefined' && getPrototypeOf(Uint8Array));\n\n/**\n * For each entry in the object, call the function with the key and value.\n *\n * @param {Object<any, any>} obj - The object to iterate over.\n * @param {Function} fn - The function to call for each entry.\n *\n * @returns {void}\n */\nconst forEachEntry = (obj, fn) => {\n  const generator = obj && obj[iterator];\n\n  const _iterator = generator.call(obj);\n\n  let result;\n\n  while ((result = _iterator.next()) && !result.done) {\n    const pair = result.value;\n    fn.call(obj, pair[0], pair[1]);\n  }\n}\n\n/**\n * It takes a regular expression and a string, and returns an array of all the matches\n *\n * @param {string} regExp - The regular expression to match against.\n * @param {string} str - The string to search.\n *\n * @returns {Array<boolean>}\n */\nconst matchAll = (regExp, str) => {\n  let matches;\n  const arr = [];\n\n  while ((matches = regExp.exec(str)) !== null) {\n    arr.push(matches);\n  }\n\n  return arr;\n}\n\n/* Checking if the kindOfTest function returns true when passed an HTMLFormElement. */\nconst isHTMLForm = kindOfTest('HTMLFormElement');\n\nconst toCamelCase = str => {\n  return str.toLowerCase().replace(/[-_\\s]([a-z\\d])(\\w*)/g,\n    function replacer(m, p1, p2) {\n      return p1.toUpperCase() + p2;\n    }\n  );\n};\n\n/* Creating a function that will check if an object has a property. */\nconst hasOwnProperty = (({hasOwnProperty}) => (obj, prop) => hasOwnProperty.call(obj, prop))(Object.prototype);\n\n/**\n * Determine if a value is a RegExp object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a RegExp object, otherwise false\n */\nconst isRegExp = kindOfTest('RegExp');\n\nconst reduceDescriptors = (obj, reducer) => {\n  const descriptors = Object.getOwnPropertyDescriptors(obj);\n  const reducedDescriptors = {};\n\n  forEach(descriptors, (descriptor, name) => {\n    let ret;\n    if ((ret = reducer(descriptor, name, obj)) !== false) {\n      reducedDescriptors[name] = ret || descriptor;\n    }\n  });\n\n  Object.defineProperties(obj, reducedDescriptors);\n}\n\n/**\n * Makes all methods read-only\n * @param {Object} obj\n */\n\nconst freezeMethods = (obj) => {\n  reduceDescriptors(obj, (descriptor, name) => {\n    // skip restricted props in strict mode\n    if (isFunction(obj) && ['arguments', 'caller', 'callee'].indexOf(name) !== -1) {\n      return false;\n    }\n\n    const value = obj[name];\n\n    if (!isFunction(value)) return;\n\n    descriptor.enumerable = false;\n\n    if ('writable' in descriptor) {\n      descriptor.writable = false;\n      return;\n    }\n\n    if (!descriptor.set) {\n      descriptor.set = () => {\n        throw Error('Can not rewrite read-only method \\'' + name + '\\'');\n      };\n    }\n  });\n}\n\nconst toObjectSet = (arrayOrString, delimiter) => {\n  const obj = {};\n\n  const define = (arr) => {\n    arr.forEach(value => {\n      obj[value] = true;\n    });\n  }\n\n  isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));\n\n  return obj;\n}\n\nconst noop = () => {}\n\nconst toFiniteNumber = (value, defaultValue) => {\n  return value != null && Number.isFinite(value = +value) ? value : defaultValue;\n}\n\n/**\n * If the thing is a FormData object, return true, otherwise return false.\n *\n * @param {unknown} thing - The thing to check.\n *\n * @returns {boolean}\n */\nfunction isSpecCompliantForm(thing) {\n  return !!(thing && isFunction(thing.append) && thing[toStringTag] === 'FormData' && thing[iterator]);\n}\n\nconst toJSONObject = (obj) => {\n  const stack = new Array(10);\n\n  const visit = (source, i) => {\n\n    if (isObject(source)) {\n      if (stack.indexOf(source) >= 0) {\n        return;\n      }\n\n      if(!('toJSON' in source)) {\n        stack[i] = source;\n        const target = isArray(source) ? [] : {};\n\n        forEach(source, (value, key) => {\n          const reducedValue = visit(value, i + 1);\n          !isUndefined(reducedValue) && (target[key] = reducedValue);\n        });\n\n        stack[i] = undefined;\n\n        return target;\n      }\n    }\n\n    return source;\n  }\n\n  return visit(obj, 0);\n}\n\nconst isAsyncFn = kindOfTest('AsyncFunction');\n\nconst isThenable = (thing) =>\n  thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);\n\n// original code\n// https://github.com/DigitalBrainJS/AxiosPromise/blob/16deab13710ec09779922131f3fa5954320f83ab/lib/utils.js#L11-L34\n\nconst _setImmediate = ((setImmediateSupported, postMessageSupported) => {\n  if (setImmediateSupported) {\n    return setImmediate;\n  }\n\n  return postMessageSupported ? ((token, callbacks) => {\n    _global.addEventListener(\"message\", ({source, data}) => {\n      if (source === _global && data === token) {\n        callbacks.length && callbacks.shift()();\n      }\n    }, false);\n\n    return (cb) => {\n      callbacks.push(cb);\n      _global.postMessage(token, \"*\");\n    }\n  })(`axios@${Math.random()}`, []) : (cb) => setTimeout(cb);\n})(\n  typeof setImmediate === 'function',\n  isFunction(_global.postMessage)\n);\n\nconst asap = typeof queueMicrotask !== 'undefined' ?\n  queueMicrotask.bind(_global) : ( typeof process !== 'undefined' && process.nextTick || _setImmediate);\n\n// *********************\n\n\nconst isIterable = (thing) => thing != null && isFunction(thing[iterator]);\n\n\nexport default {\n  isArray,\n  isArrayBuffer,\n  isBuffer,\n  isFormData,\n  isArrayBufferView,\n  isString,\n  isNumber,\n  isBoolean,\n  isObject,\n  isPlainObject,\n  isReadableStream,\n  isRequest,\n  isResponse,\n  isHeaders,\n  isUndefined,\n  isDate,\n  isFile,\n  isBlob,\n  isRegExp,\n  isFunction,\n  isStream,\n  isURLSearchParams,\n  isTypedArray,\n  isFileList,\n  forEach,\n  merge,\n  extend,\n  trim,\n  stripBOM,\n  inherits,\n  toFlatObject,\n  kindOf,\n  kindOfTest,\n  endsWith,\n  toArray,\n  forEachEntry,\n  matchAll,\n  isHTMLForm,\n  hasOwnProperty,\n  hasOwnProp: hasOwnProperty, // an alias to avoid ESLint no-prototype-builtins detection\n  reduceDescriptors,\n  freezeMethods,\n  toObjectSet,\n  toCamelCase,\n  noop,\n  toFiniteNumber,\n  findKey,\n  global: _global,\n  isContextDefined,\n  isSpecCompliantForm,\n  toJSONObject,\n  isAsyncFn,\n  isThenable,\n  setImmediate: _setImmediate,\n  asap,\n  isIterable\n};\n", "'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n\n  if (Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    this.stack = (new Error()).stack;\n  }\n\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  if (response) {\n    this.response = response;\n    this.status = response.status ? response.status : null;\n  }\n}\n\nutils.inherits(AxiosError, Error, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: utils.toJSONObject(this.config),\n      code: this.code,\n      status: this.status\n    };\n  }\n});\n\nconst prototype = AxiosError.prototype;\nconst descriptors = {};\n\n[\n  'ERR_BAD_OPTION_VALUE',\n  'ERR_BAD_OPTION',\n  'ECONNABORTED',\n  'ETIMEDOUT',\n  'ERR_NETWORK',\n  'ERR_FR_TOO_MANY_REDIRECTS',\n  'ERR_DEPRECATED',\n  'ERR_BAD_RESPONSE',\n  'ERR_BAD_REQUEST',\n  'ERR_CANCELED',\n  'ERR_NOT_SUPPORT',\n  'ERR_INVALID_URL'\n// eslint-disable-next-line func-names\n].forEach(code => {\n  descriptors[code] = {value: code};\n});\n\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {value: true});\n\n// eslint-disable-next-line func-names\nAxiosError.from = (error, code, config, request, response, customProps) => {\n  const axiosError = Object.create(prototype);\n\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  }, prop => {\n    return prop !== 'isAxiosError';\n  });\n\n  AxiosError.call(axiosError, error.message, code, config, request, response);\n\n  axiosError.cause = error;\n\n  axiosError.name = error.name;\n\n  customProps && Object.assign(axiosError, customProps);\n\n  return axiosError;\n};\n\nexport default AxiosError;\n", "// eslint-disable-next-line strict\nexport default null;\n", "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\n// temporary hotfix to avoid circular references until AxiosURLSearchParams is refactored\nimport PlatformFormData from '../platform/node/classes/FormData.js';\n\n/**\n * Determines if the given thing is a array or js object.\n *\n * @param {string} thing - The object or array to be visited.\n *\n * @returns {boolean}\n */\nfunction isVisitable(thing) {\n  return utils.isPlainObject(thing) || utils.isArray(thing);\n}\n\n/**\n * It removes the brackets from the end of a string\n *\n * @param {string} key - The key of the parameter.\n *\n * @returns {string} the key without the brackets.\n */\nfunction removeBrackets(key) {\n  return utils.endsWith(key, '[]') ? key.slice(0, -2) : key;\n}\n\n/**\n * It takes a path, a key, and a boolean, and returns a string\n *\n * @param {string} path - The path to the current key.\n * @param {string} key - The key of the current object being iterated over.\n * @param {string} dots - If true, the key will be rendered with dots instead of brackets.\n *\n * @returns {string} The path to the current key.\n */\nfunction renderKey(path, key, dots) {\n  if (!path) return key;\n  return path.concat(key).map(function each(token, i) {\n    // eslint-disable-next-line no-param-reassign\n    token = removeBrackets(token);\n    return !dots && i ? '[' + token + ']' : token;\n  }).join(dots ? '.' : '');\n}\n\n/**\n * If the array is an array and none of its elements are visitable, then it's a flat array.\n *\n * @param {Array<any>} arr - The array to check\n *\n * @returns {boolean}\n */\nfunction isFlatArray(arr) {\n  return utils.isArray(arr) && !arr.some(isVisitable);\n}\n\nconst predicates = utils.toFlatObject(utils, {}, null, function filter(prop) {\n  return /^is[A-Z]/.test(prop);\n});\n\n/**\n * Convert a data object to FormData\n *\n * @param {Object} obj\n * @param {?Object} [formData]\n * @param {?Object} [options]\n * @param {Function} [options.visitor]\n * @param {Boolean} [options.metaTokens = true]\n * @param {Boolean} [options.dots = false]\n * @param {?Boolean} [options.indexes = false]\n *\n * @returns {Object}\n **/\n\n/**\n * It converts an object into a FormData object\n *\n * @param {Object<any, any>} obj - The object to convert to form data.\n * @param {string} formData - The FormData object to append to.\n * @param {Object<string, any>} options\n *\n * @returns\n */\nfunction toFormData(obj, formData, options) {\n  if (!utils.isObject(obj)) {\n    throw new TypeError('target must be an object');\n  }\n\n  // eslint-disable-next-line no-param-reassign\n  formData = formData || new (PlatformFormData || FormData)();\n\n  // eslint-disable-next-line no-param-reassign\n  options = utils.toFlatObject(options, {\n    metaTokens: true,\n    dots: false,\n    indexes: false\n  }, false, function defined(option, source) {\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    return !utils.isUndefined(source[option]);\n  });\n\n  const metaTokens = options.metaTokens;\n  // eslint-disable-next-line no-use-before-define\n  const visitor = options.visitor || defaultVisitor;\n  const dots = options.dots;\n  const indexes = options.indexes;\n  const _Blob = options.Blob || typeof Blob !== 'undefined' && Blob;\n  const useBlob = _Blob && utils.isSpecCompliantForm(formData);\n\n  if (!utils.isFunction(visitor)) {\n    throw new TypeError('visitor must be a function');\n  }\n\n  function convertValue(value) {\n    if (value === null) return '';\n\n    if (utils.isDate(value)) {\n      return value.toISOString();\n    }\n\n    if (utils.isBoolean(value)) {\n      return value.toString();\n    }\n\n    if (!useBlob && utils.isBlob(value)) {\n      throw new AxiosError('Blob is not supported. Use a Buffer instead.');\n    }\n\n    if (utils.isArrayBuffer(value) || utils.isTypedArray(value)) {\n      return useBlob && typeof Blob === 'function' ? new Blob([value]) : Buffer.from(value);\n    }\n\n    return value;\n  }\n\n  /**\n   * Default visitor.\n   *\n   * @param {*} value\n   * @param {String|Number} key\n   * @param {Array<String|Number>} path\n   * @this {FormData}\n   *\n   * @returns {boolean} return true to visit the each prop of the value recursively\n   */\n  function defaultVisitor(value, key, path) {\n    let arr = value;\n\n    if (value && !path && typeof value === 'object') {\n      if (utils.endsWith(key, '{}')) {\n        // eslint-disable-next-line no-param-reassign\n        key = metaTokens ? key : key.slice(0, -2);\n        // eslint-disable-next-line no-param-reassign\n        value = JSON.stringify(value);\n      } else if (\n        (utils.isArray(value) && isFlatArray(value)) ||\n        ((utils.isFileList(value) || utils.endsWith(key, '[]')) && (arr = utils.toArray(value))\n        )) {\n        // eslint-disable-next-line no-param-reassign\n        key = removeBrackets(key);\n\n        arr.forEach(function each(el, index) {\n          !(utils.isUndefined(el) || el === null) && formData.append(\n            // eslint-disable-next-line no-nested-ternary\n            indexes === true ? renderKey([key], index, dots) : (indexes === null ? key : key + '[]'),\n            convertValue(el)\n          );\n        });\n        return false;\n      }\n    }\n\n    if (isVisitable(value)) {\n      return true;\n    }\n\n    formData.append(renderKey(path, key, dots), convertValue(value));\n\n    return false;\n  }\n\n  const stack = [];\n\n  const exposedHelpers = Object.assign(predicates, {\n    defaultVisitor,\n    convertValue,\n    isVisitable\n  });\n\n  function build(value, path) {\n    if (utils.isUndefined(value)) return;\n\n    if (stack.indexOf(value) !== -1) {\n      throw Error('Circular reference detected in ' + path.join('.'));\n    }\n\n    stack.push(value);\n\n    utils.forEach(value, function each(el, key) {\n      const result = !(utils.isUndefined(el) || el === null) && visitor.call(\n        formData, el, utils.isString(key) ? key.trim() : key, path, exposedHelpers\n      );\n\n      if (result === true) {\n        build(el, path ? path.concat(key) : [key]);\n      }\n    });\n\n    stack.pop();\n  }\n\n  if (!utils.isObject(obj)) {\n    throw new TypeError('data must be an object');\n  }\n\n  build(obj);\n\n  return formData;\n}\n\nexport default toFormData;\n", "'use strict';\n\nimport toFormData from './toFormData.js';\n\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */\nfunction encode(str) {\n  const charMap = {\n    '!': '%21',\n    \"'\": '%27',\n    '(': '%28',\n    ')': '%29',\n    '~': '%7E',\n    '%20': '+',\n    '%00': '\\x00'\n  };\n  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n    return charMap[match];\n  });\n}\n\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */\nfunction AxiosURLSearchParams(params, options) {\n  this._pairs = [];\n\n  params && toFormData(params, this, options);\n}\n\nconst prototype = AxiosURLSearchParams.prototype;\n\nprototype.append = function append(name, value) {\n  this._pairs.push([name, value]);\n};\n\nprototype.toString = function toString(encoder) {\n  const _encode = encoder ? function(value) {\n    return encoder.call(this, value, encode);\n  } : encode;\n\n  return this._pairs.map(function each(pair) {\n    return _encode(pair[0]) + '=' + _encode(pair[1]);\n  }, '').join('&');\n};\n\nexport default AxiosURLSearchParams;\n", "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosURLSearchParams from '../helpers/AxiosURLSearchParams.js';\n\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?(object|Function)} options\n *\n * @returns {string} The formatted url\n */\nexport default function buildURL(url, params, options) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n  \n  const _encode = options && options.encode || encode;\n\n  if (utils.isFunction(options)) {\n    options = {\n      serialize: options\n    };\n  } \n\n  const serializeFn = options && options.serialize;\n\n  let serializedParams;\n\n  if (serializeFn) {\n    serializedParams = serializeFn(params, options);\n  } else {\n    serializedParams = utils.isURLSearchParams(params) ?\n      params.toString() :\n      new AxiosURLSearchParams(params, options).toString(_encode);\n  }\n\n  if (serializedParams) {\n    const hashmarkIndex = url.indexOf(\"#\");\n\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n}\n", "'use strict';\n\nimport utils from './../utils.js';\n\nclass InterceptorManager {\n  constructor() {\n    this.handlers = [];\n  }\n\n  /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */\n  use(fulfilled, rejected, options) {\n    this.handlers.push({\n      fulfilled,\n      rejected,\n      synchronous: options ? options.synchronous : false,\n      runWhen: options ? options.runWhen : null\n    });\n    return this.handlers.length - 1;\n  }\n\n  /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */\n  eject(id) {\n    if (this.handlers[id]) {\n      this.handlers[id] = null;\n    }\n  }\n\n  /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */\n  clear() {\n    if (this.handlers) {\n      this.handlers = [];\n    }\n  }\n\n  /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */\n  forEach(fn) {\n    utils.forEach(this.handlers, function forEachHandler(h) {\n      if (h !== null) {\n        fn(h);\n      }\n    });\n  }\n}\n\nexport default InterceptorManager;\n", "'use strict';\n\nexport default {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};\n", "'use strict';\n\nimport AxiosURLSearchParams from '../../../helpers/AxiosURLSearchParams.js';\nexport default typeof URLSearchParams !== 'undefined' ? URLSearchParams : AxiosURLSearchParams;\n", "'use strict';\n\nexport default typeof FormData !== 'undefined' ? FormData : null;\n", "'use strict'\n\nexport default typeof Blob !== 'undefined' ? Blob : null\n", "import URLSearchParams from './classes/URLSearchParams.js'\nimport FormData from './classes/FormData.js'\nimport Blob from './classes/Blob.js'\n\nexport default {\n  isBrowser: true,\n  classes: {\n    URLSearchParams,\n    FormData,\n    Blob\n  },\n  protocols: ['http', 'https', 'file', 'blob', 'url', 'data']\n};\n", "const hasBrowserEnv = typeof window !== 'undefined' && typeof document !== 'undefined';\n\nconst _navigator = typeof navigator === 'object' && navigator || undefined;\n\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */\nconst hasStandardBrowserEnv = hasBrowserEnv &&\n  (!_navigator || ['ReactNative', 'NativeScript', 'NS'].indexOf(_navigator.product) < 0);\n\n/**\n * Determine if we're running in a standard browser webWorker environment\n *\n * Although the `isStandardBrowserEnv` method indicates that\n * `allows axios to run in a web worker`, the WebWorker will still be\n * filtered out due to its judgment standard\n * `typeof window !== 'undefined' && typeof document !== 'undefined'`.\n * This leads to a problem when axios post `FormData` in webWorker\n */\nconst hasStandardBrowserWebWorkerEnv = (() => {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    // eslint-disable-next-line no-undef\n    self instanceof WorkerGlobalScope &&\n    typeof self.importScripts === 'function'\n  );\n})();\n\nconst origin = hasBrowserEnv && window.location.href || 'http://localhost';\n\nexport {\n  hasBrowserEnv,\n  hasStandardBrowserWebWorkerEnv,\n  hasStandardBrowserEnv,\n  _navigator as navigator,\n  origin\n}\n", "import platform from './node/index.js';\nimport * as utils from './common/utils.js';\n\nexport default {\n  ...utils,\n  ...platform\n}\n", "'use strict';\n\nimport utils from '../utils.js';\nimport toFormData from './toFormData.js';\nimport platform from '../platform/index.js';\n\nexport default function toURLEncodedForm(data, options) {\n  return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({\n    visitor: function(value, key, path, helpers) {\n      if (platform.isNode && utils.isBuffer(value)) {\n        this.append(key, value.toString('base64'));\n        return false;\n      }\n\n      return helpers.defaultVisitor.apply(this, arguments);\n    }\n  }, options));\n}\n", "'use strict';\n\nimport utils from '../utils.js';\n\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */\nfunction parsePropPath(name) {\n  // foo[x][y][z]\n  // foo.x.y.z\n  // foo-x-y-z\n  // foo x y z\n  return utils.matchAll(/\\w+|\\[(\\w*)]/g, name).map(match => {\n    return match[0] === '[]' ? '' : match[1] || match[0];\n  });\n}\n\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */\nfunction arrayToObject(arr) {\n  const obj = {};\n  const keys = Object.keys(arr);\n  let i;\n  const len = keys.length;\n  let key;\n  for (i = 0; i < len; i++) {\n    key = keys[i];\n    obj[key] = arr[key];\n  }\n  return obj;\n}\n\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */\nfunction formDataToJSON(formData) {\n  function buildPath(path, value, target, index) {\n    let name = path[index++];\n\n    if (name === '__proto__') return true;\n\n    const isNumericKey = Number.isFinite(+name);\n    const isLast = index >= path.length;\n    name = !name && utils.isArray(target) ? target.length : name;\n\n    if (isLast) {\n      if (utils.hasOwnProp(target, name)) {\n        target[name] = [target[name], value];\n      } else {\n        target[name] = value;\n      }\n\n      return !isNumericKey;\n    }\n\n    if (!target[name] || !utils.isObject(target[name])) {\n      target[name] = [];\n    }\n\n    const result = buildPath(path, value, target[name], index);\n\n    if (result && utils.isArray(target[name])) {\n      target[name] = arrayToObject(target[name]);\n    }\n\n    return !isNumericKey;\n  }\n\n  if (utils.isFormData(formData) && utils.isFunction(formData.entries)) {\n    const obj = {};\n\n    utils.forEachEntry(formData, (name, value) => {\n      buildPath(parsePropPath(name), value, obj, 0);\n    });\n\n    return obj;\n  }\n\n  return null;\n}\n\nexport default formDataToJSON;\n", "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosError from '../core/AxiosError.js';\nimport transitionalDefaults from './transitional.js';\nimport toFormData from '../helpers/toFormData.js';\nimport toURLEncodedForm from '../helpers/toURLEncodedForm.js';\nimport platform from '../platform/index.js';\nimport formDataToJSON from '../helpers/formDataToJSON.js';\n\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */\nfunction stringifySafely(rawValue, parser, encoder) {\n  if (utils.isString(rawValue)) {\n    try {\n      (parser || JSON.parse)(rawValue);\n      return utils.trim(rawValue);\n    } catch (e) {\n      if (e.name !== 'SyntaxError') {\n        throw e;\n      }\n    }\n  }\n\n  return (encoder || JSON.stringify)(rawValue);\n}\n\nconst defaults = {\n\n  transitional: transitionalDefaults,\n\n  adapter: ['xhr', 'http', 'fetch'],\n\n  transformRequest: [function transformRequest(data, headers) {\n    const contentType = headers.getContentType() || '';\n    const hasJSONContentType = contentType.indexOf('application/json') > -1;\n    const isObjectPayload = utils.isObject(data);\n\n    if (isObjectPayload && utils.isHTMLForm(data)) {\n      data = new FormData(data);\n    }\n\n    const isFormData = utils.isFormData(data);\n\n    if (isFormData) {\n      return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n    }\n\n    if (utils.isArrayBuffer(data) ||\n      utils.isBuffer(data) ||\n      utils.isStream(data) ||\n      utils.isFile(data) ||\n      utils.isBlob(data) ||\n      utils.isReadableStream(data)\n    ) {\n      return data;\n    }\n    if (utils.isArrayBufferView(data)) {\n      return data.buffer;\n    }\n    if (utils.isURLSearchParams(data)) {\n      headers.setContentType('application/x-www-form-urlencoded;charset=utf-8', false);\n      return data.toString();\n    }\n\n    let isFileList;\n\n    if (isObjectPayload) {\n      if (contentType.indexOf('application/x-www-form-urlencoded') > -1) {\n        return toURLEncodedForm(data, this.formSerializer).toString();\n      }\n\n      if ((isFileList = utils.isFileList(data)) || contentType.indexOf('multipart/form-data') > -1) {\n        const _FormData = this.env && this.env.FormData;\n\n        return toFormData(\n          isFileList ? {'files[]': data} : data,\n          _FormData && new _FormData(),\n          this.formSerializer\n        );\n      }\n    }\n\n    if (isObjectPayload || hasJSONContentType ) {\n      headers.setContentType('application/json', false);\n      return stringifySafely(data);\n    }\n\n    return data;\n  }],\n\n  transformResponse: [function transformResponse(data) {\n    const transitional = this.transitional || defaults.transitional;\n    const forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n    const JSONRequested = this.responseType === 'json';\n\n    if (utils.isResponse(data) || utils.isReadableStream(data)) {\n      return data;\n    }\n\n    if (data && utils.isString(data) && ((forcedJSONParsing && !this.responseType) || JSONRequested)) {\n      const silentJSONParsing = transitional && transitional.silentJSONParsing;\n      const strictJSONParsing = !silentJSONParsing && JSONRequested;\n\n      try {\n        return JSON.parse(data);\n      } catch (e) {\n        if (strictJSONParsing) {\n          if (e.name === 'SyntaxError') {\n            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n          }\n          throw e;\n        }\n      }\n    }\n\n    return data;\n  }],\n\n  /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */\n  timeout: 0,\n\n  xsrfCookieName: 'XSRF-TOKEN',\n  xsrfHeaderName: 'X-XSRF-TOKEN',\n\n  maxContentLength: -1,\n  maxBodyLength: -1,\n\n  env: {\n    FormData: platform.classes.FormData,\n    Blob: platform.classes.Blob\n  },\n\n  validateStatus: function validateStatus(status) {\n    return status >= 200 && status < 300;\n  },\n\n  headers: {\n    common: {\n      'Accept': 'application/json, text/plain, */*',\n      'Content-Type': undefined\n    }\n  }\n};\n\nutils.forEach(['delete', 'get', 'head', 'post', 'put', 'patch'], (method) => {\n  defaults.headers[method] = {};\n});\n\nexport default defaults;\n", "'use strict';\n\nimport utils from './../utils.js';\n\n// RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nconst ignoreDuplicateOf = utils.toObjectSet([\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n]);\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */\nexport default rawHeaders => {\n  const parsed = {};\n  let key;\n  let val;\n  let i;\n\n  rawHeaders && rawHeaders.split('\\n').forEach(function parser(line) {\n    i = line.indexOf(':');\n    key = line.substring(0, i).trim().toLowerCase();\n    val = line.substring(i + 1).trim();\n\n    if (!key || (parsed[key] && ignoreDuplicateOf[key])) {\n      return;\n    }\n\n    if (key === 'set-cookie') {\n      if (parsed[key]) {\n        parsed[key].push(val);\n      } else {\n        parsed[key] = [val];\n      }\n    } else {\n      parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n    }\n  });\n\n  return parsed;\n};\n", "'use strict';\n\nimport utils from '../utils.js';\nimport parseHeaders from '../helpers/parseHeaders.js';\n\nconst $internals = Symbol('internals');\n\nfunction normalizeHeader(header) {\n  return header && String(header).trim().toLowerCase();\n}\n\nfunction normalizeValue(value) {\n  if (value === false || value == null) {\n    return value;\n  }\n\n  return utils.isArray(value) ? value.map(normalizeValue) : String(value);\n}\n\nfunction parseTokens(str) {\n  const tokens = Object.create(null);\n  const tokensRE = /([^\\s,;=]+)\\s*(?:=\\s*([^,;]+))?/g;\n  let match;\n\n  while ((match = tokensRE.exec(str))) {\n    tokens[match[1]] = match[2];\n  }\n\n  return tokens;\n}\n\nconst isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());\n\nfunction matchHeaderValue(context, value, header, filter, isHeaderNameFilter) {\n  if (utils.isFunction(filter)) {\n    return filter.call(this, value, header);\n  }\n\n  if (isHeaderNameFilter) {\n    value = header;\n  }\n\n  if (!utils.isString(value)) return;\n\n  if (utils.isString(filter)) {\n    return value.indexOf(filter) !== -1;\n  }\n\n  if (utils.isRegExp(filter)) {\n    return filter.test(value);\n  }\n}\n\nfunction formatHeader(header) {\n  return header.trim()\n    .toLowerCase().replace(/([a-z\\d])(\\w*)/g, (w, char, str) => {\n      return char.toUpperCase() + str;\n    });\n}\n\nfunction buildAccessors(obj, header) {\n  const accessorName = utils.toCamelCase(' ' + header);\n\n  ['get', 'set', 'has'].forEach(methodName => {\n    Object.defineProperty(obj, methodName + accessorName, {\n      value: function(arg1, arg2, arg3) {\n        return this[methodName].call(this, header, arg1, arg2, arg3);\n      },\n      configurable: true\n    });\n  });\n}\n\nclass AxiosHeaders {\n  constructor(headers) {\n    headers && this.set(headers);\n  }\n\n  set(header, valueOrRewrite, rewrite) {\n    const self = this;\n\n    function setHeader(_value, _header, _rewrite) {\n      const lHeader = normalizeHeader(_header);\n\n      if (!lHeader) {\n        throw new Error('header name must be a non-empty string');\n      }\n\n      const key = utils.findKey(self, lHeader);\n\n      if(!key || self[key] === undefined || _rewrite === true || (_rewrite === undefined && self[key] !== false)) {\n        self[key || _header] = normalizeValue(_value);\n      }\n    }\n\n    const setHeaders = (headers, _rewrite) =>\n      utils.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));\n\n    if (utils.isPlainObject(header) || header instanceof this.constructor) {\n      setHeaders(header, valueOrRewrite)\n    } else if(utils.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {\n      setHeaders(parseHeaders(header), valueOrRewrite);\n    } else if (utils.isObject(header) && utils.isIterable(header)) {\n      let obj = {}, dest, key;\n      for (const entry of header) {\n        if (!utils.isArray(entry)) {\n          throw TypeError('Object iterator must return a key-value pair');\n        }\n\n        obj[key = entry[0]] = (dest = obj[key]) ?\n          (utils.isArray(dest) ? [...dest, entry[1]] : [dest, entry[1]]) : entry[1];\n      }\n\n      setHeaders(obj, valueOrRewrite)\n    } else {\n      header != null && setHeader(valueOrRewrite, header, rewrite);\n    }\n\n    return this;\n  }\n\n  get(header, parser) {\n    header = normalizeHeader(header);\n\n    if (header) {\n      const key = utils.findKey(this, header);\n\n      if (key) {\n        const value = this[key];\n\n        if (!parser) {\n          return value;\n        }\n\n        if (parser === true) {\n          return parseTokens(value);\n        }\n\n        if (utils.isFunction(parser)) {\n          return parser.call(this, value, key);\n        }\n\n        if (utils.isRegExp(parser)) {\n          return parser.exec(value);\n        }\n\n        throw new TypeError('parser must be boolean|regexp|function');\n      }\n    }\n  }\n\n  has(header, matcher) {\n    header = normalizeHeader(header);\n\n    if (header) {\n      const key = utils.findKey(this, header);\n\n      return !!(key && this[key] !== undefined && (!matcher || matchHeaderValue(this, this[key], key, matcher)));\n    }\n\n    return false;\n  }\n\n  delete(header, matcher) {\n    const self = this;\n    let deleted = false;\n\n    function deleteHeader(_header) {\n      _header = normalizeHeader(_header);\n\n      if (_header) {\n        const key = utils.findKey(self, _header);\n\n        if (key && (!matcher || matchHeaderValue(self, self[key], key, matcher))) {\n          delete self[key];\n\n          deleted = true;\n        }\n      }\n    }\n\n    if (utils.isArray(header)) {\n      header.forEach(deleteHeader);\n    } else {\n      deleteHeader(header);\n    }\n\n    return deleted;\n  }\n\n  clear(matcher) {\n    const keys = Object.keys(this);\n    let i = keys.length;\n    let deleted = false;\n\n    while (i--) {\n      const key = keys[i];\n      if(!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {\n        delete this[key];\n        deleted = true;\n      }\n    }\n\n    return deleted;\n  }\n\n  normalize(format) {\n    const self = this;\n    const headers = {};\n\n    utils.forEach(this, (value, header) => {\n      const key = utils.findKey(headers, header);\n\n      if (key) {\n        self[key] = normalizeValue(value);\n        delete self[header];\n        return;\n      }\n\n      const normalized = format ? formatHeader(header) : String(header).trim();\n\n      if (normalized !== header) {\n        delete self[header];\n      }\n\n      self[normalized] = normalizeValue(value);\n\n      headers[normalized] = true;\n    });\n\n    return this;\n  }\n\n  concat(...targets) {\n    return this.constructor.concat(this, ...targets);\n  }\n\n  toJSON(asStrings) {\n    const obj = Object.create(null);\n\n    utils.forEach(this, (value, header) => {\n      value != null && value !== false && (obj[header] = asStrings && utils.isArray(value) ? value.join(', ') : value);\n    });\n\n    return obj;\n  }\n\n  [Symbol.iterator]() {\n    return Object.entries(this.toJSON())[Symbol.iterator]();\n  }\n\n  toString() {\n    return Object.entries(this.toJSON()).map(([header, value]) => header + ': ' + value).join('\\n');\n  }\n\n  getSetCookie() {\n    return this.get(\"set-cookie\") || [];\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'AxiosHeaders';\n  }\n\n  static from(thing) {\n    return thing instanceof this ? thing : new this(thing);\n  }\n\n  static concat(first, ...targets) {\n    const computed = new this(first);\n\n    targets.forEach((target) => computed.set(target));\n\n    return computed;\n  }\n\n  static accessor(header) {\n    const internals = this[$internals] = (this[$internals] = {\n      accessors: {}\n    });\n\n    const accessors = internals.accessors;\n    const prototype = this.prototype;\n\n    function defineAccessor(_header) {\n      const lHeader = normalizeHeader(_header);\n\n      if (!accessors[lHeader]) {\n        buildAccessors(prototype, _header);\n        accessors[lHeader] = true;\n      }\n    }\n\n    utils.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);\n\n    return this;\n  }\n}\n\nAxiosHeaders.accessor(['Content-Type', 'Content-Length', 'Accept', 'Accept-Encoding', 'User-Agent', 'Authorization']);\n\n// reserved names hotfix\nutils.reduceDescriptors(AxiosHeaders.prototype, ({value}, key) => {\n  let mapped = key[0].toUpperCase() + key.slice(1); // map `set` => `Set`\n  return {\n    get: () => value,\n    set(headerValue) {\n      this[mapped] = headerValue;\n    }\n  }\n});\n\nutils.freezeMethods(AxiosHeaders);\n\nexport default AxiosHeaders;\n", "'use strict';\n\nimport utils from './../utils.js';\nimport defaults from '../defaults/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */\nexport default function transformData(fns, response) {\n  const config = this || defaults;\n  const context = response || config;\n  const headers = AxiosHeaders.from(context.headers);\n  let data = context.data;\n\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n  });\n\n  headers.normalize();\n\n  return data;\n}\n", "'use strict';\n\nexport default function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n}\n", "'use strict';\n\nimport AxiosError from '../core/AxiosError.js';\nimport utils from '../utils.js';\n\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */\nfunction CanceledError(message, config, request) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED, config, request);\n  this.name = 'CanceledError';\n}\n\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\n\nexport default CanceledError;\n", "'use strict';\n\nimport AxiosError from './AxiosError.js';\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */\nexport default function settle(resolve, reject, response) {\n  const validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError(\n      'Request failed with status code ' + response.status,\n      [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],\n      response.config,\n      response.request,\n      response\n    ));\n  }\n}\n", "'use strict';\n\nexport default function parseProtocol(url) {\n  const match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n}\n", "'use strict';\n\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */\nfunction speedometer(samplesCount, min) {\n  samplesCount = samplesCount || 10;\n  const bytes = new Array(samplesCount);\n  const timestamps = new Array(samplesCount);\n  let head = 0;\n  let tail = 0;\n  let firstSampleTS;\n\n  min = min !== undefined ? min : 1000;\n\n  return function push(chunkLength) {\n    const now = Date.now();\n\n    const startedAt = timestamps[tail];\n\n    if (!firstSampleTS) {\n      firstSampleTS = now;\n    }\n\n    bytes[head] = chunkLength;\n    timestamps[head] = now;\n\n    let i = tail;\n    let bytesCount = 0;\n\n    while (i !== head) {\n      bytesCount += bytes[i++];\n      i = i % samplesCount;\n    }\n\n    head = (head + 1) % samplesCount;\n\n    if (head === tail) {\n      tail = (tail + 1) % samplesCount;\n    }\n\n    if (now - firstSampleTS < min) {\n      return;\n    }\n\n    const passed = startedAt && now - startedAt;\n\n    return passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n  };\n}\n\nexport default speedometer;\n", "/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  let threshold = 1000 / freq;\n  let lastArgs;\n  let timer;\n\n  const invoke = (args, now = Date.now()) => {\n    timestamp = now;\n    lastArgs = null;\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n    fn.apply(null, args);\n  }\n\n  const throttled = (...args) => {\n    const now = Date.now();\n    const passed = now - timestamp;\n    if ( passed >= threshold) {\n      invoke(args, now);\n    } else {\n      lastArgs = args;\n      if (!timer) {\n        timer = setTimeout(() => {\n          timer = null;\n          invoke(lastArgs)\n        }, threshold - passed);\n      }\n    }\n  }\n\n  const flush = () => lastArgs && invoke(lastArgs);\n\n  return [throttled, flush];\n}\n\nexport default throttle;\n", "import speedometer from \"./speedometer.js\";\nimport throttle from \"./throttle.js\";\nimport utils from \"../utils.js\";\n\nexport const progressEventReducer = (listener, isDownloadStream, freq = 3) => {\n  let bytesNotified = 0;\n  const _speedometer = speedometer(50, 250);\n\n  return throttle(e => {\n    const loaded = e.loaded;\n    const total = e.lengthComputable ? e.total : undefined;\n    const progressBytes = loaded - bytesNotified;\n    const rate = _speedometer(progressBytes);\n    const inRange = loaded <= total;\n\n    bytesNotified = loaded;\n\n    const data = {\n      loaded,\n      total,\n      progress: total ? (loaded / total) : undefined,\n      bytes: progressBytes,\n      rate: rate ? rate : undefined,\n      estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n      event: e,\n      lengthComputable: total != null,\n      [isDownloadStream ? 'download' : 'upload']: true\n    };\n\n    listener(data);\n  }, freq);\n}\n\nexport const progressEventDecorator = (total, throttled) => {\n  const lengthComputable = total != null;\n\n  return [(loaded) => throttled[0]({\n    lengthComputable,\n    total,\n    loaded\n  }), throttled[1]];\n}\n\nexport const asyncDecorator = (fn) => (...args) => utils.asap(() => fn(...args));\n", "import platform from '../platform/index.js';\n\nexport default platform.hasStandardBrowserEnv ? ((origin, isMSIE) => (url) => {\n  url = new URL(url, platform.origin);\n\n  return (\n    origin.protocol === url.protocol &&\n    origin.host === url.host &&\n    (isMSIE || origin.port === url.port)\n  );\n})(\n  new URL(platform.origin),\n  platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)\n) : () => true;\n", "import utils from './../utils.js';\nimport platform from '../platform/index.js';\n\nexport default platform.hasStandardBrowserEnv ?\n\n  // Standard browser envs support document.cookie\n  {\n    write(name, value, expires, path, domain, secure) {\n      const cookie = [name + '=' + encodeURIComponent(value)];\n\n      utils.isNumber(expires) && cookie.push('expires=' + new Date(expires).toGMTString());\n\n      utils.isString(path) && cookie.push('path=' + path);\n\n      utils.isString(domain) && cookie.push('domain=' + domain);\n\n      secure === true && cookie.push('secure');\n\n      document.cookie = cookie.join('; ');\n    },\n\n    read(name) {\n      const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n      return (match ? decodeURIComponent(match[3]) : null);\n    },\n\n    remove(name) {\n      this.write(name, '', Date.now() - 86400000);\n    }\n  }\n\n  :\n\n  // Non-standard browser env (web workers, react-native) lack needed support.\n  {\n    write() {},\n    read() {\n      return null;\n    },\n    remove() {}\n  };\n\n", "'use strict';\n\n/**\n * Determines whether the specified URL is absolute\n *\n * @param {string} url The URL to test\n *\n * @returns {boolean} True if the specified URL is absolute, otherwise false\n */\nexport default function isAbsoluteURL(url) {\n  // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\n  // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\n  // by any combination of letters, digits, plus, period, or hyphen.\n  return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url);\n}\n", "'use strict';\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */\nexport default function combineURLs(baseURL, relativeURL) {\n  return relativeURL\n    ? baseURL.replace(/\\/?\\/$/, '') + '/' + relativeURL.replace(/^\\/+/, '')\n    : baseURL;\n}\n", "'use strict';\n\nimport isAbsoluteURL from '../helpers/isAbsoluteURL.js';\nimport combineURLs from '../helpers/combineURLs.js';\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */\nexport default function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {\n  let isRelativeUrl = !isAbsoluteURL(requestedURL);\n  if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n}\n", "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosHeaders from \"./AxiosHeaders.js\";\n\nconst headersToObject = (thing) => thing instanceof AxiosHeaders ? { ...thing } : thing;\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  const config = {};\n\n  function getMergedValue(target, source, prop, caseless) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge.call({caseless}, target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(a, b, prop , caseless) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(a, b, prop , caseless);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a, prop , caseless);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(a, b, prop) {\n    if (prop in config2) {\n      return getMergedValue(a, b);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  const mergeMap = {\n    url: valueFromConfig2,\n    method: valueFromConfig2,\n    data: valueFromConfig2,\n    baseURL: defaultToConfig2,\n    transformRequest: defaultToConfig2,\n    transformResponse: defaultToConfig2,\n    paramsSerializer: defaultToConfig2,\n    timeout: defaultToConfig2,\n    timeoutMessage: defaultToConfig2,\n    withCredentials: defaultToConfig2,\n    withXSRFToken: defaultToConfig2,\n    adapter: defaultToConfig2,\n    responseType: defaultToConfig2,\n    xsrfCookieName: defaultToConfig2,\n    xsrfHeaderName: defaultToConfig2,\n    onUploadProgress: defaultToConfig2,\n    onDownloadProgress: defaultToConfig2,\n    decompress: defaultToConfig2,\n    maxContentLength: defaultToConfig2,\n    maxBodyLength: defaultToConfig2,\n    beforeRedirect: defaultToConfig2,\n    transport: defaultToConfig2,\n    httpAgent: defaultToConfig2,\n    httpsAgent: defaultToConfig2,\n    cancelToken: defaultToConfig2,\n    socketPath: defaultToConfig2,\n    responseEncoding: defaultToConfig2,\n    validateStatus: mergeDirectKeys,\n    headers: (a, b , prop) => mergeDeepProperties(headersToObject(a), headersToObject(b),prop, true)\n  };\n\n  utils.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {\n    const merge = mergeMap[prop] || mergeDeepProperties;\n    const configValue = merge(config1[prop], config2[prop], prop);\n    (utils.isUndefined(configValue) && merge !== mergeDirectKeys) || (config[prop] = configValue);\n  });\n\n  return config;\n}\n", "import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport isURLSameOrigin from \"./isURLSameOrigin.js\";\nimport cookies from \"./cookies.js\";\nimport buildFullPath from \"../core/buildFullPath.js\";\nimport mergeConfig from \"../core/mergeConfig.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport buildURL from \"./buildURL.js\";\n\nexport default (config) => {\n  const newConfig = mergeConfig({}, config);\n\n  let {data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth} = newConfig;\n\n  newConfig.headers = headers = AxiosHeaders.from(headers);\n\n  newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);\n\n  // HTTP basic authentication\n  if (auth) {\n    headers.set('Authorization', 'Basic ' +\n      btoa((auth.username || '') + ':' + (auth.password ? unescape(encodeURIComponent(auth.password)) : ''))\n    );\n  }\n\n  let contentType;\n\n  if (utils.isFormData(data)) {\n    if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n      headers.setContentType(undefined); // Let the browser set it\n    } else if ((contentType = headers.getContentType()) !== false) {\n      // fix semicolon duplication issue for ReactNative FormData implementation\n      const [type, ...tokens] = contentType ? contentType.split(';').map(token => token.trim()).filter(Boolean) : [];\n      headers.setContentType([type || 'multipart/form-data', ...tokens].join('; '));\n    }\n  }\n\n  // Add xsrf header\n  // This is only done if running in a standard browser environment.\n  // Specifically not if we're in a web worker, or react-native.\n\n  if (platform.hasStandardBrowserEnv) {\n    withXSRFToken && utils.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n\n    if (withXSRFToken || (withXSRFToken !== false && isURLSameOrigin(newConfig.url))) {\n      // Add xsrf header\n      const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n\n      if (xsrfValue) {\n        headers.set(xsrfHeaderName, xsrfValue);\n      }\n    }\n  }\n\n  return newConfig;\n}\n\n", "import utils from './../utils.js';\nimport settle from './../core/settle.js';\nimport transitionalDefaults from '../defaults/transitional.js';\nimport AxiosError from '../core/AxiosError.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport parseProtocol from '../helpers/parseProtocol.js';\nimport platform from '../platform/index.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport {progressEventReducer} from '../helpers/progressEventReducer.js';\nimport resolveConfig from \"../helpers/resolveConfig.js\";\n\nconst isXHRAdapterSupported = typeof XMLHttpRequest !== 'undefined';\n\nexport default isXHRAdapterSupported && function (config) {\n  return new Promise(function dispatchXhrRequest(resolve, reject) {\n    const _config = resolveConfig(config);\n    let requestData = _config.data;\n    const requestHeaders = AxiosHeaders.from(_config.headers).normalize();\n    let {responseType, onUploadProgress, onDownloadProgress} = _config;\n    let onCanceled;\n    let uploadThrottled, downloadThrottled;\n    let flushUpload, flushDownload;\n\n    function done() {\n      flushUpload && flushUpload(); // flush events\n      flushDownload && flushDownload(); // flush events\n\n      _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);\n\n      _config.signal && _config.signal.removeEventListener('abort', onCanceled);\n    }\n\n    let request = new XMLHttpRequest();\n\n    request.open(_config.method.toUpperCase(), _config.url, true);\n\n    // Set the request timeout in MS\n    request.timeout = _config.timeout;\n\n    function onloadend() {\n      if (!request) {\n        return;\n      }\n      // Prepare the response\n      const responseHeaders = AxiosHeaders.from(\n        'getAllResponseHeaders' in request && request.getAllResponseHeaders()\n      );\n      const responseData = !responseType || responseType === 'text' || responseType === 'json' ?\n        request.responseText : request.response;\n      const response = {\n        data: responseData,\n        status: request.status,\n        statusText: request.statusText,\n        headers: responseHeaders,\n        config,\n        request\n      };\n\n      settle(function _resolve(value) {\n        resolve(value);\n        done();\n      }, function _reject(err) {\n        reject(err);\n        done();\n      }, response);\n\n      // Clean up request\n      request = null;\n    }\n\n    if ('onloadend' in request) {\n      // Use onloadend if available\n      request.onloadend = onloadend;\n    } else {\n      // Listen for ready state to emulate onloadend\n      request.onreadystatechange = function handleLoad() {\n        if (!request || request.readyState !== 4) {\n          return;\n        }\n\n        // The request errored out and we didn't get a response, this will be\n        // handled by onerror instead\n        // With one exception: request that using file: protocol, most browsers\n        // will return status as 0 even though it's a successful request\n        if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {\n          return;\n        }\n        // readystate handler is calling before onerror or ontimeout handlers,\n        // so we should call onloadend on the next 'tick'\n        setTimeout(onloadend);\n      };\n    }\n\n    // Handle browser request cancellation (as opposed to a manual cancellation)\n    request.onabort = function handleAbort() {\n      if (!request) {\n        return;\n      }\n\n      reject(new AxiosError('Request aborted', AxiosError.ECONNABORTED, config, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle low level network errors\n    request.onerror = function handleError() {\n      // Real errors are hidden from us by the browser\n      // onerror should only fire if it's a network error\n      reject(new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Handle timeout\n    request.ontimeout = function handleTimeout() {\n      let timeoutErrorMessage = _config.timeout ? 'timeout of ' + _config.timeout + 'ms exceeded' : 'timeout exceeded';\n      const transitional = _config.transitional || transitionalDefaults;\n      if (_config.timeoutErrorMessage) {\n        timeoutErrorMessage = _config.timeoutErrorMessage;\n      }\n      reject(new AxiosError(\n        timeoutErrorMessage,\n        transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,\n        config,\n        request));\n\n      // Clean up request\n      request = null;\n    };\n\n    // Remove Content-Type if data is undefined\n    requestData === undefined && requestHeaders.setContentType(null);\n\n    // Add headers to the request\n    if ('setRequestHeader' in request) {\n      utils.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {\n        request.setRequestHeader(key, val);\n      });\n    }\n\n    // Add withCredentials to request if needed\n    if (!utils.isUndefined(_config.withCredentials)) {\n      request.withCredentials = !!_config.withCredentials;\n    }\n\n    // Add responseType to request if needed\n    if (responseType && responseType !== 'json') {\n      request.responseType = _config.responseType;\n    }\n\n    // Handle progress if needed\n    if (onDownloadProgress) {\n      ([downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true));\n      request.addEventListener('progress', downloadThrottled);\n    }\n\n    // Not all browsers support upload events\n    if (onUploadProgress && request.upload) {\n      ([uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress));\n\n      request.upload.addEventListener('progress', uploadThrottled);\n\n      request.upload.addEventListener('loadend', flushUpload);\n    }\n\n    if (_config.cancelToken || _config.signal) {\n      // Handle cancellation\n      // eslint-disable-next-line func-names\n      onCanceled = cancel => {\n        if (!request) {\n          return;\n        }\n        reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);\n        request.abort();\n        request = null;\n      };\n\n      _config.cancelToken && _config.cancelToken.subscribe(onCanceled);\n      if (_config.signal) {\n        _config.signal.aborted ? onCanceled() : _config.signal.addEventListener('abort', onCanceled);\n      }\n    }\n\n    const protocol = parseProtocol(_config.url);\n\n    if (protocol && platform.protocols.indexOf(protocol) === -1) {\n      reject(new AxiosError('Unsupported protocol ' + protocol + ':', AxiosError.ERR_BAD_REQUEST, config));\n      return;\n    }\n\n\n    // Send the request\n    request.send(requestData || null);\n  });\n}\n", "import CanceledError from \"../cancel/CanceledError.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport utils from '../utils.js';\n\nconst composeSignals = (signals, timeout) => {\n  const {length} = (signals = signals ? signals.filter(Boolean) : []);\n\n  if (timeout || length) {\n    let controller = new AbortController();\n\n    let aborted;\n\n    const onabort = function (reason) {\n      if (!aborted) {\n        aborted = true;\n        unsubscribe();\n        const err = reason instanceof Error ? reason : this.reason;\n        controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n      }\n    }\n\n    let timer = timeout && setTimeout(() => {\n      timer = null;\n      onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT))\n    }, timeout)\n\n    const unsubscribe = () => {\n      if (signals) {\n        timer && clearTimeout(timer);\n        timer = null;\n        signals.forEach(signal => {\n          signal.unsubscribe ? signal.unsubscribe(onabort) : signal.removeEventListener('abort', onabort);\n        });\n        signals = null;\n      }\n    }\n\n    signals.forEach((signal) => signal.addEventListener('abort', onabort));\n\n    const {signal} = controller;\n\n    signal.unsubscribe = () => utils.asap(unsubscribe);\n\n    return signal;\n  }\n}\n\nexport default composeSignals;\n", "\nexport const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n\n  let pos = 0;\n  let end;\n\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n}\n\nexport const readBytes = async function* (iterable, chunkSize) {\n  for await (const chunk of readStream(iterable)) {\n    yield* streamChunk(chunk, chunkSize);\n  }\n}\n\nconst readStream = async function* (stream) {\n  if (stream[Symbol.asyncIterator]) {\n    yield* stream;\n    return;\n  }\n\n  const reader = stream.getReader();\n  try {\n    for (;;) {\n      const {done, value} = await reader.read();\n      if (done) {\n        break;\n      }\n      yield value;\n    }\n  } finally {\n    await reader.cancel();\n  }\n}\n\nexport const trackStream = (stream, chunkSize, onProgress, onFinish) => {\n  const iterator = readBytes(stream, chunkSize);\n\n  let bytes = 0;\n  let done;\n  let _onFinish = (e) => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  }\n\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {done, value} = await iterator.next();\n\n        if (done) {\n         _onFinish();\n          controller.close();\n          return;\n        }\n\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  })\n}\n", "import platform from \"../platform/index.js\";\nimport utils from \"../utils.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport composeSignals from \"../helpers/composeSignals.js\";\nimport {trackStream} from \"../helpers/trackStream.js\";\nimport AxiosHeaders from \"../core/AxiosHeaders.js\";\nimport {progressEventReducer, progressEventDecorator, asyncDecorator} from \"../helpers/progressEventReducer.js\";\nimport resolveConfig from \"../helpers/resolveConfig.js\";\nimport settle from \"../core/settle.js\";\n\nconst isFetchSupported = typeof fetch === 'function' && typeof Request === 'function' && typeof Response === 'function';\nconst isReadableStreamSupported = isFetchSupported && typeof ReadableStream === 'function';\n\n// used only inside the fetch adapter\nconst encodeText = isFetchSupported && (typeof TextEncoder === 'function' ?\n    ((encoder) => (str) => encoder.encode(str))(new TextEncoder()) :\n    async (str) => new Uint8Array(await new Response(str).arrayBuffer())\n);\n\nconst test = (fn, ...args) => {\n  try {\n    return !!fn(...args);\n  } catch (e) {\n    return false\n  }\n}\n\nconst supportsRequestStream = isReadableStreamSupported && test(() => {\n  let duplexAccessed = false;\n\n  const hasContentType = new Request(platform.origin, {\n    body: new ReadableStream(),\n    method: 'POST',\n    get duplex() {\n      duplexAccessed = true;\n      return 'half';\n    },\n  }).headers.has('Content-Type');\n\n  return duplexAccessed && !hasContentType;\n});\n\nconst DEFAULT_CHUNK_SIZE = 64 * 1024;\n\nconst supportsResponseStream = isReadableStreamSupported &&\n  test(() => utils.isReadableStream(new Response('').body));\n\n\nconst resolvers = {\n  stream: supportsResponseStream && ((res) => res.body)\n};\n\nisFetchSupported && (((res) => {\n  ['text', 'arrayBuffer', 'blob', 'formData', 'stream'].forEach(type => {\n    !resolvers[type] && (resolvers[type] = utils.isFunction(res[type]) ? (res) => res[type]() :\n      (_, config) => {\n        throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);\n      })\n  });\n})(new Response));\n\nconst getBodyLength = async (body) => {\n  if (body == null) {\n    return 0;\n  }\n\n  if(utils.isBlob(body)) {\n    return body.size;\n  }\n\n  if(utils.isSpecCompliantForm(body)) {\n    const _request = new Request(platform.origin, {\n      method: 'POST',\n      body,\n    });\n    return (await _request.arrayBuffer()).byteLength;\n  }\n\n  if(utils.isArrayBufferView(body) || utils.isArrayBuffer(body)) {\n    return body.byteLength;\n  }\n\n  if(utils.isURLSearchParams(body)) {\n    body = body + '';\n  }\n\n  if(utils.isString(body)) {\n    return (await encodeText(body)).byteLength;\n  }\n}\n\nconst resolveBodyLength = async (headers, body) => {\n  const length = utils.toFiniteNumber(headers.getContentLength());\n\n  return length == null ? getBodyLength(body) : length;\n}\n\nexport default isFetchSupported && (async (config) => {\n  let {\n    url,\n    method,\n    data,\n    signal,\n    cancelToken,\n    timeout,\n    onDownloadProgress,\n    onUploadProgress,\n    responseType,\n    headers,\n    withCredentials = 'same-origin',\n    fetchOptions\n  } = resolveConfig(config);\n\n  responseType = responseType ? (responseType + '').toLowerCase() : 'text';\n\n  let composedSignal = composeSignals([signal, cancelToken && cancelToken.toAbortSignal()], timeout);\n\n  let request;\n\n  const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {\n      composedSignal.unsubscribe();\n  });\n\n  let requestContentLength;\n\n  try {\n    if (\n      onUploadProgress && supportsRequestStream && method !== 'get' && method !== 'head' &&\n      (requestContentLength = await resolveBodyLength(headers, data)) !== 0\n    ) {\n      let _request = new Request(url, {\n        method: 'POST',\n        body: data,\n        duplex: \"half\"\n      });\n\n      let contentTypeHeader;\n\n      if (utils.isFormData(data) && (contentTypeHeader = _request.headers.get('content-type'))) {\n        headers.setContentType(contentTypeHeader)\n      }\n\n      if (_request.body) {\n        const [onProgress, flush] = progressEventDecorator(\n          requestContentLength,\n          progressEventReducer(asyncDecorator(onUploadProgress))\n        );\n\n        data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);\n      }\n    }\n\n    if (!utils.isString(withCredentials)) {\n      withCredentials = withCredentials ? 'include' : 'omit';\n    }\n\n    // Cloudflare Workers throws when credentials are defined\n    // see https://github.com/cloudflare/workerd/issues/902\n    const isCredentialsSupported = \"credentials\" in Request.prototype;\n    request = new Request(url, {\n      ...fetchOptions,\n      signal: composedSignal,\n      method: method.toUpperCase(),\n      headers: headers.normalize().toJSON(),\n      body: data,\n      duplex: \"half\",\n      credentials: isCredentialsSupported ? withCredentials : undefined\n    });\n\n    let response = await fetch(request, fetchOptions);\n\n    const isStreamResponse = supportsResponseStream && (responseType === 'stream' || responseType === 'response');\n\n    if (supportsResponseStream && (onDownloadProgress || (isStreamResponse && unsubscribe))) {\n      const options = {};\n\n      ['status', 'statusText', 'headers'].forEach(prop => {\n        options[prop] = response[prop];\n      });\n\n      const responseContentLength = utils.toFiniteNumber(response.headers.get('content-length'));\n\n      const [onProgress, flush] = onDownloadProgress && progressEventDecorator(\n        responseContentLength,\n        progressEventReducer(asyncDecorator(onDownloadProgress), true)\n      ) || [];\n\n      response = new Response(\n        trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {\n          flush && flush();\n          unsubscribe && unsubscribe();\n        }),\n        options\n      );\n    }\n\n    responseType = responseType || 'text';\n\n    let responseData = await resolvers[utils.findKey(resolvers, responseType) || 'text'](response, config);\n\n    !isStreamResponse && unsubscribe && unsubscribe();\n\n    return await new Promise((resolve, reject) => {\n      settle(resolve, reject, {\n        data: responseData,\n        headers: AxiosHeaders.from(response.headers),\n        status: response.status,\n        statusText: response.statusText,\n        config,\n        request\n      })\n    })\n  } catch (err) {\n    unsubscribe && unsubscribe();\n\n    if (err && err.name === 'TypeError' && /Load failed|fetch/i.test(err.message)) {\n      throw Object.assign(\n        new AxiosError('Network Error', AxiosError.ERR_NETWORK, config, request),\n        {\n          cause: err.cause || err\n        }\n      )\n    }\n\n    throw AxiosError.from(err, err && err.code, config, request);\n  }\n});\n\n\n", "import utils from '../utils.js';\nimport httpAdapter from './http.js';\nimport xhrAdapter from './xhr.js';\nimport fetchAdapter from './fetch.js';\nimport AxiosError from \"../core/AxiosError.js\";\n\nconst knownAdapters = {\n  http: httpAdapter,\n  xhr: xhrAdapter,\n  fetch: fetchAdapter\n}\n\nutils.forEach(knownAdapters, (fn, value) => {\n  if (fn) {\n    try {\n      Object.defineProperty(fn, 'name', {value});\n    } catch (e) {\n      // eslint-disable-next-line no-empty\n    }\n    Object.defineProperty(fn, 'adapterName', {value});\n  }\n});\n\nconst renderReason = (reason) => `- ${reason}`;\n\nconst isResolvedHandle = (adapter) => utils.isFunction(adapter) || adapter === null || adapter === false;\n\nexport default {\n  getAdapter: (adapters) => {\n    adapters = utils.isArray(adapters) ? adapters : [adapters];\n\n    const {length} = adapters;\n    let nameOrAdapter;\n    let adapter;\n\n    const rejectedReasons = {};\n\n    for (let i = 0; i < length; i++) {\n      nameOrAdapter = adapters[i];\n      let id;\n\n      adapter = nameOrAdapter;\n\n      if (!isResolvedHandle(nameOrAdapter)) {\n        adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n\n        if (adapter === undefined) {\n          throw new AxiosError(`Unknown adapter '${id}'`);\n        }\n      }\n\n      if (adapter) {\n        break;\n      }\n\n      rejectedReasons[id || '#' + i] = adapter;\n    }\n\n    if (!adapter) {\n\n      const reasons = Object.entries(rejectedReasons)\n        .map(([id, state]) => `adapter ${id} ` +\n          (state === false ? 'is not supported by the environment' : 'is not available in the build')\n        );\n\n      let s = length ?\n        (reasons.length > 1 ? 'since :\\n' + reasons.map(renderReason).join('\\n') : ' ' + renderReason(reasons[0])) :\n        'as no adapter specified';\n\n      throw new AxiosError(\n        `There is no suitable adapter to dispatch the request ` + s,\n        'ERR_NOT_SUPPORT'\n      );\n    }\n\n    return adapter;\n  },\n  adapters: knownAdapters\n}\n", "'use strict';\n\nimport transformData from './transformData.js';\nimport isCancel from '../cancel/isCancel.js';\nimport defaults from '../defaults/index.js';\nimport CanceledError from '../cancel/CanceledError.js';\nimport AxiosHeaders from '../core/AxiosHeaders.js';\nimport adapters from \"../adapters/adapters.js\";\n\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */\nfunction throwIfCancellationRequested(config) {\n  if (config.cancelToken) {\n    config.cancelToken.throwIfRequested();\n  }\n\n  if (config.signal && config.signal.aborted) {\n    throw new CanceledError(null, config);\n  }\n}\n\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */\nexport default function dispatchRequest(config) {\n  throwIfCancellationRequested(config);\n\n  config.headers = AxiosHeaders.from(config.headers);\n\n  // Transform request data\n  config.data = transformData.call(\n    config,\n    config.transformRequest\n  );\n\n  if (['post', 'put', 'patch'].indexOf(config.method) !== -1) {\n    config.headers.setContentType('application/x-www-form-urlencoded', false);\n  }\n\n  const adapter = adapters.getAdapter(config.adapter || defaults.adapter);\n\n  return adapter(config).then(function onAdapterResolution(response) {\n    throwIfCancellationRequested(config);\n\n    // Transform response data\n    response.data = transformData.call(\n      config,\n      config.transformResponse,\n      response\n    );\n\n    response.headers = AxiosHeaders.from(response.headers);\n\n    return response;\n  }, function onAdapterRejection(reason) {\n    if (!isCancel(reason)) {\n      throwIfCancellationRequested(config);\n\n      // Transform response data\n      if (reason && reason.response) {\n        reason.response.data = transformData.call(\n          config,\n          config.transformResponse,\n          reason.response\n        );\n        reason.response.headers = AxiosHeaders.from(reason.response.headers);\n      }\n    }\n\n    return Promise.reject(reason);\n  });\n}\n", "export const VERSION = \"1.10.0\";", "'use strict';\n\nimport {VERSION} from '../env/data.js';\nimport AxiosError from '../core/AxiosError.js';\n\nconst validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach((type, i) => {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\n\nconst deprecatedWarnings = {};\n\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return (value, opt, opts) => {\n    if (validator === false) {\n      throw new AxiosError(\n        formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')),\n        AxiosError.ERR_DEPRECATED\n      );\n    }\n\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(\n        formatMessage(\n          opt,\n          ' has been deprecated since v' + version + ' and will be removed in the near future'\n        )\n      );\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\nvalidators.spelling = function spelling(correctSpelling) {\n  return (value, opt) => {\n    // eslint-disable-next-line no-console\n    console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);\n    return true;\n  }\n};\n\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n  const keys = Object.keys(options);\n  let i = keys.length;\n  while (i-- > 0) {\n    const opt = keys[i];\n    const validator = schema[opt];\n    if (validator) {\n      const value = options[opt];\n      const result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\n\nexport default {\n  assertOptions,\n  validators\n};\n", "'use strict';\n\nimport utils from './../utils.js';\nimport buildURL from '../helpers/buildURL.js';\nimport InterceptorManager from './InterceptorManager.js';\nimport dispatchRequest from './dispatchRequest.js';\nimport mergeConfig from './mergeConfig.js';\nimport buildFullPath from './buildFullPath.js';\nimport validator from '../helpers/validator.js';\nimport AxiosHeaders from './AxiosHeaders.js';\n\nconst validators = validator.validators;\n\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n *\n * @return {Axios} A new instance of Axios\n */\nclass Axios {\n  constructor(instanceConfig) {\n    this.defaults = instanceConfig || {};\n    this.interceptors = {\n      request: new InterceptorManager(),\n      response: new InterceptorManager()\n    };\n  }\n\n  /**\n   * Dispatch a request\n   *\n   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)\n   * @param {?Object} config\n   *\n   * @returns {Promise} The Promise to be fulfilled\n   */\n  async request(configOrUrl, config) {\n    try {\n      return await this._request(configOrUrl, config);\n    } catch (err) {\n      if (err instanceof Error) {\n        let dummy = {};\n\n        Error.captureStackTrace ? Error.captureStackTrace(dummy) : (dummy = new Error());\n\n        // slice off the Error: ... line\n        const stack = dummy.stack ? dummy.stack.replace(/^.+\\n/, '') : '';\n        try {\n          if (!err.stack) {\n            err.stack = stack;\n            // match without the 2 top stack lines\n          } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\\n.+\\n/, ''))) {\n            err.stack += '\\n' + stack\n          }\n        } catch (e) {\n          // ignore the case where \"stack\" is an un-writable property\n        }\n      }\n\n      throw err;\n    }\n  }\n\n  _request(configOrUrl, config) {\n    /*eslint no-param-reassign:0*/\n    // Allow for axios('example/url'[, config]) a la fetch API\n    if (typeof configOrUrl === 'string') {\n      config = config || {};\n      config.url = configOrUrl;\n    } else {\n      config = configOrUrl || {};\n    }\n\n    config = mergeConfig(this.defaults, config);\n\n    const {transitional, paramsSerializer, headers} = config;\n\n    if (transitional !== undefined) {\n      validator.assertOptions(transitional, {\n        silentJSONParsing: validators.transitional(validators.boolean),\n        forcedJSONParsing: validators.transitional(validators.boolean),\n        clarifyTimeoutError: validators.transitional(validators.boolean)\n      }, false);\n    }\n\n    if (paramsSerializer != null) {\n      if (utils.isFunction(paramsSerializer)) {\n        config.paramsSerializer = {\n          serialize: paramsSerializer\n        }\n      } else {\n        validator.assertOptions(paramsSerializer, {\n          encode: validators.function,\n          serialize: validators.function\n        }, true);\n      }\n    }\n\n    // Set config.allowAbsoluteUrls\n    if (config.allowAbsoluteUrls !== undefined) {\n      // do nothing\n    } else if (this.defaults.allowAbsoluteUrls !== undefined) {\n      config.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls;\n    } else {\n      config.allowAbsoluteUrls = true;\n    }\n\n    validator.assertOptions(config, {\n      baseUrl: validators.spelling('baseURL'),\n      withXsrfToken: validators.spelling('withXSRFToken')\n    }, true);\n\n    // Set config.method\n    config.method = (config.method || this.defaults.method || 'get').toLowerCase();\n\n    // Flatten headers\n    let contextHeaders = headers && utils.merge(\n      headers.common,\n      headers[config.method]\n    );\n\n    headers && utils.forEach(\n      ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],\n      (method) => {\n        delete headers[method];\n      }\n    );\n\n    config.headers = AxiosHeaders.concat(contextHeaders, headers);\n\n    // filter out skipped interceptors\n    const requestInterceptorChain = [];\n    let synchronousRequestInterceptors = true;\n    this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n      if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {\n        return;\n      }\n\n      synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n\n      requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n    });\n\n    const responseInterceptorChain = [];\n    this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n      responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n    });\n\n    let promise;\n    let i = 0;\n    let len;\n\n    if (!synchronousRequestInterceptors) {\n      const chain = [dispatchRequest.bind(this), undefined];\n      chain.unshift.apply(chain, requestInterceptorChain);\n      chain.push.apply(chain, responseInterceptorChain);\n      len = chain.length;\n\n      promise = Promise.resolve(config);\n\n      while (i < len) {\n        promise = promise.then(chain[i++], chain[i++]);\n      }\n\n      return promise;\n    }\n\n    len = requestInterceptorChain.length;\n\n    let newConfig = config;\n\n    i = 0;\n\n    while (i < len) {\n      const onFulfilled = requestInterceptorChain[i++];\n      const onRejected = requestInterceptorChain[i++];\n      try {\n        newConfig = onFulfilled(newConfig);\n      } catch (error) {\n        onRejected.call(this, error);\n        break;\n      }\n    }\n\n    try {\n      promise = dispatchRequest.call(this, newConfig);\n    } catch (error) {\n      return Promise.reject(error);\n    }\n\n    i = 0;\n    len = responseInterceptorChain.length;\n\n    while (i < len) {\n      promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);\n    }\n\n    return promise;\n  }\n\n  getUri(config) {\n    config = mergeConfig(this.defaults, config);\n    const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);\n    return buildURL(fullPath, config.params, config.paramsSerializer);\n  }\n}\n\n// Provide aliases for supported request methods\nutils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {\n  /*eslint func-names:0*/\n  Axios.prototype[method] = function(url, config) {\n    return this.request(mergeConfig(config || {}, {\n      method,\n      url,\n      data: (config || {}).data\n    }));\n  };\n});\n\nutils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {\n  /*eslint func-names:0*/\n\n  function generateHTTPMethod(isForm) {\n    return function httpMethod(url, data, config) {\n      return this.request(mergeConfig(config || {}, {\n        method,\n        headers: isForm ? {\n          'Content-Type': 'multipart/form-data'\n        } : {},\n        url,\n        data\n      }));\n    };\n  }\n\n  Axios.prototype[method] = generateHTTPMethod();\n\n  Axios.prototype[method + 'Form'] = generateHTTPMethod(true);\n});\n\nexport default Axios;\n", "'use strict';\n\nimport CanceledError from './CanceledError.js';\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */\nclass CancelToken {\n  constructor(executor) {\n    if (typeof executor !== 'function') {\n      throw new TypeError('executor must be a function.');\n    }\n\n    let resolvePromise;\n\n    this.promise = new Promise(function promiseExecutor(resolve) {\n      resolvePromise = resolve;\n    });\n\n    const token = this;\n\n    // eslint-disable-next-line func-names\n    this.promise.then(cancel => {\n      if (!token._listeners) return;\n\n      let i = token._listeners.length;\n\n      while (i-- > 0) {\n        token._listeners[i](cancel);\n      }\n      token._listeners = null;\n    });\n\n    // eslint-disable-next-line func-names\n    this.promise.then = onfulfilled => {\n      let _resolve;\n      // eslint-disable-next-line func-names\n      const promise = new Promise(resolve => {\n        token.subscribe(resolve);\n        _resolve = resolve;\n      }).then(onfulfilled);\n\n      promise.cancel = function reject() {\n        token.unsubscribe(_resolve);\n      };\n\n      return promise;\n    };\n\n    executor(function cancel(message, config, request) {\n      if (token.reason) {\n        // Cancellation has already been requested\n        return;\n      }\n\n      token.reason = new CanceledError(message, config, request);\n      resolvePromise(token.reason);\n    });\n  }\n\n  /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */\n  throwIfRequested() {\n    if (this.reason) {\n      throw this.reason;\n    }\n  }\n\n  /**\n   * Subscribe to the cancel signal\n   */\n\n  subscribe(listener) {\n    if (this.reason) {\n      listener(this.reason);\n      return;\n    }\n\n    if (this._listeners) {\n      this._listeners.push(listener);\n    } else {\n      this._listeners = [listener];\n    }\n  }\n\n  /**\n   * Unsubscribe from the cancel signal\n   */\n\n  unsubscribe(listener) {\n    if (!this._listeners) {\n      return;\n    }\n    const index = this._listeners.indexOf(listener);\n    if (index !== -1) {\n      this._listeners.splice(index, 1);\n    }\n  }\n\n  toAbortSignal() {\n    const controller = new AbortController();\n\n    const abort = (err) => {\n      controller.abort(err);\n    };\n\n    this.subscribe(abort);\n\n    controller.signal.unsubscribe = () => this.unsubscribe(abort);\n\n    return controller.signal;\n  }\n\n  /**\n   * Returns an object that contains a new `CancelToken` and a function that, when called,\n   * cancels the `CancelToken`.\n   */\n  static source() {\n    let cancel;\n    const token = new CancelToken(function executor(c) {\n      cancel = c;\n    });\n    return {\n      token,\n      cancel\n    };\n  }\n}\n\nexport default CancelToken;\n", "'use strict';\n\n/**\n * Syntactic sugar for invoking a function and expanding an array for arguments.\n *\n * Common use case would be to use `Function.prototype.apply`.\n *\n *  ```js\n *  function f(x, y, z) {}\n *  var args = [1, 2, 3];\n *  f.apply(null, args);\n *  ```\n *\n * With `spread` this example can be re-written.\n *\n *  ```js\n *  spread(function(x, y, z) {})([1, 2, 3]);\n *  ```\n *\n * @param {Function} callback\n *\n * @returns {Function}\n */\nexport default function spread(callback) {\n  return function wrap(arr) {\n    return callback.apply(null, arr);\n  };\n}\n", "'use strict';\n\nimport utils from './../utils.js';\n\n/**\n * Determines whether the payload is an error thrown by <PERSON>xios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\nexport default function isAxiosError(payload) {\n  return utils.isObject(payload) && (payload.isAxiosError === true);\n}\n", "const HttpStatusCode = {\n  Continue: 100,\n  SwitchingProtocols: 101,\n  Processing: 102,\n  EarlyHints: 103,\n  Ok: 200,\n  Created: 201,\n  Accepted: 202,\n  NonAuthoritativeInformation: 203,\n  NoContent: 204,\n  ResetContent: 205,\n  PartialContent: 206,\n  MultiStatus: 207,\n  AlreadyReported: 208,\n  ImUsed: 226,\n  MultipleChoices: 300,\n  MovedPermanently: 301,\n  Found: 302,\n  SeeOther: 303,\n  NotModified: 304,\n  UseProxy: 305,\n  Unused: 306,\n  TemporaryRedirect: 307,\n  PermanentRedirect: 308,\n  BadRequest: 400,\n  Unauthorized: 401,\n  PaymentRequired: 402,\n  Forbidden: 403,\n  NotFound: 404,\n  MethodNotAllowed: 405,\n  NotAcceptable: 406,\n  ProxyAuthenticationRequired: 407,\n  RequestTimeout: 408,\n  Conflict: 409,\n  Gone: 410,\n  LengthRequired: 411,\n  PreconditionFailed: 412,\n  PayloadTooLarge: 413,\n  UriTooLong: 414,\n  UnsupportedMediaType: 415,\n  RangeNotSatisfiable: 416,\n  ExpectationFailed: 417,\n  ImATeapot: 418,\n  MisdirectedRequest: 421,\n  UnprocessableEntity: 422,\n  Locked: 423,\n  FailedDependency: 424,\n  TooEarly: 425,\n  UpgradeRequired: 426,\n  PreconditionRequired: 428,\n  TooManyRequests: 429,\n  RequestHeaderFieldsTooLarge: 431,\n  UnavailableForLegalReasons: 451,\n  InternalServerError: 500,\n  NotImplemented: 501,\n  BadGateway: 502,\n  ServiceUnavailable: 503,\n  GatewayTimeout: 504,\n  HttpVersionNotSupported: 505,\n  VariantAlsoNegotiates: 506,\n  InsufficientStorage: 507,\n  LoopDetected: 508,\n  NotExtended: 510,\n  NetworkAuthenticationRequired: 511,\n};\n\nObject.entries(HttpStatusCode).forEach(([key, value]) => {\n  HttpStatusCode[value] = key;\n});\n\nexport default HttpStatusCode;\n", "'use strict';\n\nimport utils from './utils.js';\nimport bind from './helpers/bind.js';\nimport Axios from './core/Axios.js';\nimport mergeConfig from './core/mergeConfig.js';\nimport defaults from './defaults/index.js';\nimport formDataToJSON from './helpers/formDataToJSON.js';\nimport CanceledError from './cancel/CanceledError.js';\nimport CancelToken from './cancel/CancelToken.js';\nimport isCancel from './cancel/isCancel.js';\nimport {VERSION} from './env/data.js';\nimport toFormData from './helpers/toFormData.js';\nimport AxiosError from './core/AxiosError.js';\nimport spread from './helpers/spread.js';\nimport isAxiosError from './helpers/isAxiosError.js';\nimport AxiosHeaders from \"./core/AxiosHeaders.js\";\nimport adapters from './adapters/adapters.js';\nimport HttpStatusCode from './helpers/HttpStatusCode.js';\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  const context = new Axios(defaultConfig);\n  const instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context, {allOwnKeys: true});\n\n  // Copy context to instance\n  utils.extend(instance, context, null, {allOwnKeys: true});\n\n  // Factory for creating new instances\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n\n  return instance;\n}\n\n// Create the default instance to be exported\nconst axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Expose Cancel & CancelToken\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData;\n\n// Expose AxiosError class\naxios.AxiosError = AxiosError;\n\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\n\naxios.spread = spread;\n\n// Expose isAxiosError\naxios.isAxiosError = isAxiosError;\n\n// Expose mergeConfig\naxios.mergeConfig = mergeConfig;\n\naxios.AxiosHeaders = AxiosHeaders;\n\naxios.formToJSON = thing => formDataToJSON(utils.isHTMLForm(thing) ? new FormData(thing) : thing);\n\naxios.getAdapter = adapters.getAdapter;\n\naxios.HttpStatusCode = HttpStatusCode;\n\naxios.default = axios;\n\n// this module should only have a default export\nexport default axios\n", "import axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { PaginatedResponse, Product, Category, Review, ProductSearchParams } from '@/types';\n\n// 创建axios实例\nconst api: AxiosInstance = axios.create({\n  baseURL: '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 可以在这里添加认证token等\n    console.log('API Request:', config.method?.toUpperCase(), config.url);\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response: AxiosResponse) => {\n    console.log('API Response:', response.status, response.config.url);\n    return response;\n  },\n  (error) => {\n    console.error('API Error:', error.response?.status, error.response?.data);\n    return Promise.reject(error);\n  }\n);\n\n// 商品API\nexport const productApi = {\n  // 获取商品列表\n  getProducts: async (params?: ProductSearchParams): Promise<PaginatedResponse<Product>> => {\n    const response = await api.get('/products', { params });\n    return response.data;\n  },\n\n  // 获取商品详情\n  getProduct: async (id: number): Promise<Product> => {\n    const response = await api.get(`/products/${id}`);\n    return response.data;\n  },\n\n  // 搜索商品\n  searchProducts: async (keyword: string, params?: ProductSearchParams): Promise<PaginatedResponse<Product>> => {\n    const response = await api.get('/products/search', { \n      params: { keyword, ...params } \n    });\n    return response.data;\n  },\n\n  // 获取热门商品\n  getPopularProducts: async (limit: number = 10): Promise<Product[]> => {\n    const response = await api.get('/products/popular', { params: { limit } });\n    return response.data;\n  },\n\n  // 获取推荐商品\n  getRecommendedProducts: async (productId?: number, limit: number = 10): Promise<Product[]> => {\n    const response = await api.get('/products/recommended', { \n      params: { productId, limit } \n    });\n    return response.data;\n  },\n};\n\n// 分类API\nexport const categoryApi = {\n  // 获取所有分类\n  getCategories: async (): Promise<Category[]> => {\n    const response = await api.get('/products/categories');\n    const categoryNames: string[] = response.data;\n\n    // 将字符串数组转换为Category对象数组\n    return categoryNames.map((name, index) => ({\n      id: index + 1,\n      name,\n      slug: name.toLowerCase().replace(/\\s+/g, '-'),\n      description: `${name}相关商品`,\n    }));\n  },\n\n  // 获取分类详情\n  getCategory: async (id: number): Promise<Category> => {\n    const categories = await categoryApi.getCategories();\n    const category = categories.find(cat => cat.id === id);\n    if (!category) {\n      throw new Error(`Category with id ${id} not found`);\n    }\n    return category;\n  },\n\n  // 获取分类下的商品\n  getCategoryProducts: async (categoryId: number, params?: ProductSearchParams): Promise<PaginatedResponse<Product>> => {\n    const categories = await categoryApi.getCategories();\n    const category = categories.find(cat => cat.id === categoryId);\n    if (!category) {\n      throw new Error(`Category with id ${categoryId} not found`);\n    }\n\n    const response = await api.get(`/products/category/${encodeURIComponent(category.name)}`, { params });\n    return response.data;\n  },\n};\n\n// 评价API\nexport const reviewApi = {\n  // 获取商品评价\n  getProductReviews: async (productId: number, page: number = 0, size: number = 10): Promise<PaginatedResponse<Review>> => {\n    const response = await api.get(`/products/${productId}/reviews`, { \n      params: { page, size } \n    });\n    return response.data;\n  },\n\n  // 添加评价\n  addReview: async (productId: number, review: Partial<Review>): Promise<Review> => {\n    const response = await api.post(`/products/${productId}/reviews`, review);\n    return response.data;\n  },\n};\n\n// 统计API\nexport const statsApi = {\n  // 获取商品统计信息\n  getProductStats: async (productId: number) => {\n    const response = await api.get(`/products/${productId}/stats`);\n    return response.data;\n  },\n};\n\nexport default api;\n", "import { useQuery, useInfiniteQuery } from '@tanstack/react-query';\nimport { productApi } from '@/services/api';\nimport { ProductSearchParams } from '@/types';\n\n// 获取商品列表\nexport const useProducts = (params?: ProductSearchParams) => {\n  return useQuery({\n    queryKey: ['products', params],\n    queryFn: () => productApi.getProducts(params),\n    staleTime: 5 * 60 * 1000, // 5分钟\n    gcTime: 10 * 60 * 1000, // 10分钟\n  });\n};\n\n// 获取商品详情\nexport const useProduct = (id: number) => {\n  return useQuery({\n    queryKey: ['product', id],\n    queryFn: () => productApi.getProduct(id),\n    enabled: !!id,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\n// 搜索商品\nexport const useSearchProducts = (keyword: string, params?: ProductSearchParams) => {\n  return useQuery({\n    queryKey: ['searchProducts', keyword, params],\n    queryFn: () => productApi.searchProducts(keyword, params),\n    enabled: !!keyword,\n    staleTime: 2 * 60 * 1000, // 搜索结果缓存时间较短\n  });\n};\n\n// 无限滚动获取商品\nexport const useInfiniteProducts = (params?: ProductSearchParams) => {\n  return useInfiniteQuery({\n    queryKey: ['infiniteProducts', params],\n    queryFn: ({ pageParam = 0 }) => \n      productApi.getProducts({ ...params, page: pageParam }),\n    getNextPageParam: (lastPage) => {\n      if (lastPage.last) return undefined;\n      return lastPage.number + 1;\n    },\n    initialPageParam: 0,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\n// 获取热门商品\nexport const usePopularProducts = (limit: number = 10) => {\n  return useQuery({\n    queryKey: ['popularProducts', limit],\n    queryFn: () => productApi.getPopularProducts(limit),\n    staleTime: 10 * 60 * 1000, // 热门商品缓存时间较长\n  });\n};\n\n// 获取推荐商品\nexport const useRecommendedProducts = (productId?: number, limit: number = 10) => {\n  return useQuery({\n    queryKey: ['recommendedProducts', productId, limit],\n    queryFn: () => productApi.getRecommendedProducts(productId, limit),\n    enabled: !!productId,\n    staleTime: 10 * 60 * 1000,\n  });\n};\n", "import { useQuery } from '@tanstack/react-query';\nimport { categoryApi } from '@/services/api';\nimport { ProductSearchParams } from '@/types';\n\n// 获取所有分类\nexport const useCategories = () => {\n  return useQuery({\n    queryKey: ['categories'],\n    queryFn: categoryApi.getCategories,\n    staleTime: 30 * 60 * 1000, // 分类数据缓存30分钟\n    gcTime: 60 * 60 * 1000, // 1小时\n  });\n};\n\n// 获取分类详情\nexport const useCategory = (id: number) => {\n  return useQuery({\n    queryKey: ['category', id],\n    queryFn: () => categoryApi.getCategory(id),\n    enabled: !!id,\n    staleTime: 30 * 60 * 1000,\n  });\n};\n\n// 获取分类下的商品\nexport const useCategoryProducts = (categoryId: number, params?: ProductSearchParams) => {\n  return useQuery({\n    queryKey: ['categoryProducts', categoryId, params],\n    queryFn: () => categoryApi.getCategoryProducts(categoryId, params),\n    enabled: !!categoryId,\n    staleTime: 5 * 60 * 1000,\n  });\n};\n", "import React from 'react';\nimport { cn } from '@/utils';\nimport { BaseComponentProps } from '@/types';\n\ninterface CardProps extends BaseComponentProps {\n  hover?: boolean;\n  onClick?: () => void;\n}\n\ninterface CardComponent extends React.FC<CardProps> {\n  Header: React.FC<BaseComponentProps>;\n  Content: React.FC<BaseComponentProps>;\n  Footer: React.FC<BaseComponentProps>;\n}\n\nconst Card = ({\n  children,\n  className,\n  hover = false,\n  onClick\n}: CardProps) => {\n  return (\n    <div\n      className={cn(\n        'card',\n        hover && 'hover:shadow-md transition-shadow duration-200',\n        onClick && 'cursor-pointer',\n        className\n      )}\n      onClick={onClick}\n    >\n      {children}\n    </div>\n  );\n};\n\nconst CardHeader: React.FC<BaseComponentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('card-header', className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardContent: React.FC<BaseComponentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('card-content', className)}>\n      {children}\n    </div>\n  );\n};\n\nconst CardFooter: React.FC<BaseComponentProps> = ({ children, className }) => {\n  return (\n    <div className={cn('card-footer', className)}>\n      {children}\n    </div>\n  );\n};\n\n// Attach sub-components\nconst CardWithSubComponents = Card as CardComponent;\nCardWithSubComponents.Header = CardHeader;\nCardWithSubComponents.Content = CardContent;\nCardWithSubComponents.Footer = CardFooter;\n\nexport default CardWithSubComponents;\n", "import React from 'react';\nimport { Star, ShoppingCart } from 'lucide-react';\nimport { ProductCardProps } from '@/types';\nimport { formatPrice, calculateDiscount, generateStars } from '@/utils';\nimport { useCartStore } from '@/store/cartStore';\nimport Button from './ui/Button';\nimport Card from './ui/Card';\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product, onClick }) => {\n  const { addItem, isInCart, getItemQuantity } = useCartStore();\n  \n  const discount = product.originalPrice \n    ? calculateDiscount(product.originalPrice, product.price)\n    : 0;\n  \n  const stars = generateStars(product.rating);\n  const inCart = isInCart(product.id);\n  const quantity = getItemQuantity(product.id);\n\n  const handleAddToCart = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    addItem(product);\n  };\n\n  const handleCardClick = () => {\n    onClick?.(product);\n  };\n\n  return (\n    <Card className=\"product-card\" hover>\n      <div onClick={handleCardClick}>\n        {/* 商品图片 */}\n        <div className=\"relative overflow-hidden rounded-t-lg\">\n          <img\n            src={product.imageUrl}\n            alt={product.name}\n            className=\"w-full h-48 object-cover transition-transform duration-300 hover:scale-105\"\n            loading=\"lazy\"\n          />\n          \n          {/* 折扣标签 */}\n          {discount > 0 && (\n            <div className=\"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium\">\n              -{discount}%\n            </div>\n          )}\n          \n          {/* 库存状态 */}\n          {product.stock === 0 && (\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n              <span className=\"text-white font-medium\">缺货</span>\n            </div>\n          )}\n        </div>\n\n        {/* 商品信息 */}\n        <Card.Content className=\"p-4\">\n          {/* 商品名称 */}\n          <h3 className=\"font-medium text-gray-900 mb-2 line-clamp-2 hover:text-primary-600 transition-colors\">\n            {product.name}\n          </h3>\n          \n          {/* 品牌 */}\n          <p className=\"text-sm text-gray-500 mb-2\">{product.brand}</p>\n          \n          {/* 评分 */}\n          <div className=\"flex items-center mb-3\">\n            <div className=\"rating-stars\">\n              {stars.map((filled, index) => (\n                <Star\n                  key={index}\n                  className={`w-4 h-4 ${filled ? 'star-filled' : 'star-empty'}`}\n                  fill=\"currentColor\"\n                />\n              ))}\n            </div>\n            <span className=\"text-sm text-gray-500 ml-2\">\n              ({product.reviewCount})\n            </span>\n          </div>\n          \n          {/* 价格 */}\n          <div className=\"flex items-center justify-between mb-3\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"price-current\">\n                {formatPrice(product.price)}\n              </span>\n              {product.originalPrice && product.originalPrice > product.price && (\n                <span className=\"price-original\">\n                  {formatPrice(product.originalPrice)}\n                </span>\n              )}\n            </div>\n          </div>\n        </Card.Content>\n      </div>\n\n      {/* 操作按钮 */}\n      <Card.Footer className=\"p-4 pt-0\">\n        <Button\n          onClick={handleAddToCart}\n          disabled={product.stock === 0}\n          className=\"w-full\"\n          variant={inCart ? \"secondary\" : \"primary\"}\n        >\n          <ShoppingCart className=\"w-4 h-4 mr-2\" />\n          {product.stock === 0 \n            ? '缺货' \n            : inCart \n              ? `已添加 (${quantity})` \n              : '加入购物车'\n          }\n        </Button>\n      </Card.Footer>\n    </Card>\n  );\n};\n\nexport default ProductCard;\n", "import React from 'react';\nimport { cn } from '@/utils';\nimport { LoadingProps } from '@/types';\n\nconst Loading: React.FC<LoadingProps> = ({ size = 'md', className }) => {\n  const sizeClasses: Record<string, string> = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n  };\n\n  return (\n    <div className={cn('loading-spinner', sizeClasses[size], className)} />\n  );\n};\n\n// 页面级加载组件\nexport const PageLoading: React.FC = () => {\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px]\">\n      <div className=\"text-center\">\n        <Loading size=\"lg\" />\n        <p className=\"mt-4 text-gray-600\">加载中...</p>\n      </div>\n    </div>\n  );\n};\n\n// 骨架屏组件\nexport const Skeleton: React.FC<{ className?: string }> = ({ className }) => {\n  return <div className={cn('skeleton', className)} />;\n};\n\n// 商品卡片骨架屏\nexport const ProductCardSkeleton: React.FC = () => {\n  return (\n    <div className=\"card\">\n      <Skeleton className=\"h-48 w-full rounded-t-lg\" />\n      <div className=\"p-4 space-y-3\">\n        <Skeleton className=\"h-4 w-3/4\" />\n        <Skeleton className=\"h-4 w-1/2\" />\n        <div className=\"flex justify-between items-center\">\n          <Skeleton className=\"h-6 w-20\" />\n          <Skeleton className=\"h-8 w-16\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Loading;\n", "import React from 'react';\nimport { ProductGridProps } from '@/types';\nimport ProductCard from './ProductCard';\nimport { ProductCardSkeleton } from './ui/Loading';\n\nconst ProductGrid: React.FC<ProductGridProps> = ({\n  products,\n  loading = false,\n  onProductClick,\n}) => {\n  if (loading) {\n    return (\n      <div className=\"product-grid\">\n        {Array.from({ length: 12 }).map((_, index) => (\n          <ProductCardSkeleton key={index} />\n        ))}\n      </div>\n    );\n  }\n\n  if (products.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-400 text-6xl mb-4\">📦</div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">暂无商品</h3>\n        <p className=\"text-gray-500\">请尝试调整筛选条件或搜索关键词</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"product-grid\">\n      {products.map((product) => (\n        <ProductCard\n          key={product.id}\n          product={product}\n          onClick={onProductClick}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default ProductGrid;\n", "import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useProducts, usePopularProducts } from '@/hooks/useProducts';\nimport { useCategories } from '@/hooks/useCategories';\nimport { Product, ProductSearchParams } from '@/types';\nimport ProductGrid from '@/components/ProductGrid';\nimport { PageLoading } from '@/components/ui/Loading';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\n\nconst HomePage: React.FC = () => {\n  const navigate = useNavigate();\n  const [searchParams] = useState<ProductSearchParams>({\n    page: 0,\n    size: 20,\n    sortBy: 'createdAt',\n    sortOrder: 'desc',\n  });\n\n  const { data: productsData, isLoading: productsLoading } = useProducts(searchParams);\n  const { data: popularProducts, isLoading: popularLoading } = usePopularProducts(8);\n  const { data: categories, isLoading: categoriesLoading } = useCategories();\n\n  const handleProductClick = (product: Product) => {\n    navigate(`/products/${product.id}`);\n  };\n\n  const handleCategoryClick = (categoryId: number) => {\n    navigate(`/categories/${categoryId}`);\n  };\n\n  if (productsLoading && popularLoading && categoriesLoading) {\n    return <PageLoading />;\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 英雄区域 */}\n      <section className=\"gradient-bg text-white py-16 rounded-lg\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\n            现代化商品展示\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n            发现优质商品，享受购物乐趣\n          </p>\n          <Button\n            size=\"lg\"\n            variant=\"secondary\"\n            onClick={() => navigate('/products')}\n            className=\"text-primary-600\"\n          >\n            开始购物\n          </Button>\n        </div>\n      </section>\n\n      {/* 热门分类 */}\n      {categories && categories.length > 0 && (\n        <section>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">热门分类</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n            {categories.slice(0, 6).map((category) => (\n              <Card\n                key={category.id}\n                hover\n                className=\"cursor-pointer text-center p-4\"\n                onClick={() => handleCategoryClick(category.id)}\n              >\n                <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                  <span className=\"text-2xl\">📦</span>\n                </div>\n                <h3 className=\"font-medium text-gray-900\">{category.name}</h3>\n              </Card>\n            ))}\n          </div>\n        </section>\n      )}\n\n      {/* 热门商品 */}\n      {popularProducts && popularProducts.length > 0 && (\n        <section>\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">热门商品</h2>\n            <Button\n              variant=\"outline\"\n              onClick={() => navigate('/products?sort=popular')}\n            >\n              查看更多\n            </Button>\n          </div>\n          <ProductGrid\n            products={popularProducts}\n            loading={popularLoading}\n            onProductClick={handleProductClick}\n          />\n        </section>\n      )}\n\n      {/* 最新商品 */}\n      <section>\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">最新商品</h2>\n          <Button\n            variant=\"outline\"\n            onClick={() => navigate('/products')}\n          >\n            查看全部\n          </Button>\n        </div>\n        <ProductGrid\n          products={productsData?.content || []}\n          loading={productsLoading}\n          onProductClick={handleProductClick}\n        />\n      </section>\n\n      {/* 特色功能 */}\n      <section className=\"bg-gray-50 rounded-lg p-8\">\n        <h2 className=\"text-2xl font-bold text-gray-900 text-center mb-8\">\n          为什么选择我们\n        </h2>\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-3xl\">🚀</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">极速加载</h3>\n            <p className=\"text-gray-600\">\n              基于现代化技术栈，提供极速的页面加载体验\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-3xl\">📱</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">响应式设计</h3>\n            <p className=\"text-gray-600\">\n              完美适配各种设备，随时随地享受购物乐趣\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-3xl\">🔒</span>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">安全可靠</h3>\n            <p className=\"text-gray-600\">\n              企业级安全保障，让您的购物更加安心\n            </p>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default HomePage;\n", "import React, { useState } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Star, ShoppingCart, ArrowLeft, Plus, Minus } from 'lucide-react';\nimport { useProduct, useRecommendedProducts } from '@/hooks/useProducts';\nimport { useCartStore } from '@/store/cartStore';\nimport { formatPrice, calculateDiscount, generateStars } from '@/utils';\nimport { PageLoading } from '@/components/ui/Loading';\nimport Button from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\nimport ProductGrid from '@/components/ProductGrid';\n\nconst ProductDetailPage: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const productId = parseInt(id || '0');\n  \n  const [quantity, setQuantity] = useState(1);\n  const [selectedImage, setSelectedImage] = useState(0);\n\n  const { data: product, isLoading, error } = useProduct(productId);\n  const { data: recommendedProducts } = useRecommendedProducts(productId, 8);\n  const { addItem, isInCart, getItemQuantity } = useCartStore();\n\n  if (isLoading) return <PageLoading />;\n  \n  if (error || !product) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-400 text-6xl mb-4\">😕</div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">商品未找到</h2>\n        <p className=\"text-gray-600 mb-6\">抱歉，您查找的商品不存在或已下架</p>\n        <Button onClick={() => navigate('/')}>返回首页</Button>\n      </div>\n    );\n  }\n\n  const discount = product.originalPrice \n    ? calculateDiscount(product.originalPrice, product.price)\n    : 0;\n  \n  const stars = generateStars(product.rating);\n  const inCart = isInCart(product.id);\n  const cartQuantity = getItemQuantity(product.id);\n  const images = product.images || [product.imageUrl];\n\n  const handleAddToCart = () => {\n    addItem(product, quantity);\n    setQuantity(1);\n  };\n\n  const handleQuantityChange = (delta: number) => {\n    const newQuantity = Math.max(1, Math.min(product.stock, quantity + delta));\n    setQuantity(newQuantity);\n  };\n\n  const handleProductClick = (clickedProduct: any) => {\n    navigate(`/products/${clickedProduct.id}`);\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 返回按钮 */}\n      <Button\n        variant=\"ghost\"\n        onClick={() => navigate(-1)}\n        className=\"flex items-center\"\n      >\n        <ArrowLeft className=\"w-4 h-4 mr-2\" />\n        返回\n      </Button>\n\n      {/* 商品详情 */}\n      <div className=\"grid lg:grid-cols-2 gap-8\">\n        {/* 商品图片 */}\n        <div className=\"space-y-4\">\n          <div className=\"aspect-square overflow-hidden rounded-lg bg-gray-100\">\n            <img\n              src={images[selectedImage]}\n              alt={product.name}\n              className=\"w-full h-full object-cover\"\n            />\n          </div>\n          \n          {images.length > 1 && (\n            <div className=\"flex space-x-2 overflow-x-auto\">\n              {images.map((image, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSelectedImage(index)}\n                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${\n                    selectedImage === index ? 'border-primary-500' : 'border-gray-200'\n                  }`}\n                >\n                  <img\n                    src={image}\n                    alt={`${product.name} ${index + 1}`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* 商品信息 */}\n        <div className=\"space-y-6\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n              {product.name}\n            </h1>\n            <p className=\"text-lg text-gray-600\">{product.brand}</p>\n          </div>\n\n          {/* 评分 */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"rating-stars\">\n              {stars.map((filled, index) => (\n                <Star\n                  key={index}\n                  className={`w-5 h-5 ${filled ? 'star-filled' : 'star-empty'}`}\n                  fill=\"currentColor\"\n                />\n              ))}\n            </div>\n            <span className=\"text-gray-600\">\n              {product.rating} ({product.reviewCount} 评价)\n            </span>\n          </div>\n\n          {/* 价格 */}\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-3xl font-bold text-red-600\">\n                {formatPrice(product.price)}\n              </span>\n              {product.originalPrice && product.originalPrice > product.price && (\n                <>\n                  <span className=\"text-xl text-gray-500 line-through\">\n                    {formatPrice(product.originalPrice)}\n                  </span>\n                  <span className=\"bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium\">\n                    省 {formatPrice(product.originalPrice - product.price)}\n                  </span>\n                </>\n              )}\n            </div>\n            {discount > 0 && (\n              <p className=\"text-green-600 font-medium\">\n                限时优惠 {discount}% OFF\n              </p>\n            )}\n          </div>\n\n          {/* 库存状态 */}\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-gray-700\">库存:</span>\n            <span className={`font-medium ${\n              product.stock > 10 ? 'text-green-600' : \n              product.stock > 0 ? 'text-yellow-600' : 'text-red-600'\n            }`}>\n              {product.stock > 0 ? `${product.stock} 件` : '缺货'}\n            </span>\n          </div>\n\n          {/* 数量选择 */}\n          {product.stock > 0 && (\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-gray-700\">数量:</span>\n              <div className=\"flex items-center border border-gray-300 rounded-lg\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => handleQuantityChange(-1)}\n                  disabled={quantity <= 1}\n                  className=\"px-3 py-2\"\n                >\n                  <Minus className=\"w-4 h-4\" />\n                </Button>\n                <span className=\"px-4 py-2 min-w-[3rem] text-center\">\n                  {quantity}\n                </span>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => handleQuantityChange(1)}\n                  disabled={quantity >= product.stock}\n                  className=\"px-3 py-2\"\n                >\n                  <Plus className=\"w-4 h-4\" />\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {/* 操作按钮 */}\n          <div className=\"flex space-x-4\">\n            <Button\n              onClick={handleAddToCart}\n              disabled={product.stock === 0}\n              className=\"flex-1\"\n              size=\"lg\"\n            >\n              <ShoppingCart className=\"w-5 h-5 mr-2\" />\n              {product.stock === 0 ? '缺货' : '加入购物车'}\n            </Button>\n            {inCart && (\n              <div className=\"flex items-center text-sm text-gray-600\">\n                购物车中已有 {cartQuantity} 件\n              </div>\n            )}\n          </div>\n\n          {/* 商品描述 */}\n          <Card>\n            <Card.Header>\n              <h3 className=\"text-lg font-semibold\">商品描述</h3>\n            </Card.Header>\n            <Card.Content>\n              <p className=\"text-gray-700 leading-relaxed\">\n                {product.description}\n              </p>\n            </Card.Content>\n          </Card>\n\n          {/* 规格参数 */}\n          {product.specifications && (\n            <Card>\n              <Card.Header>\n                <h3 className=\"text-lg font-semibold\">规格参数</h3>\n              </Card.Header>\n              <Card.Content>\n                <div className=\"space-y-2\">\n                  {Object.entries(product.specifications).map(([key, value]) => (\n                    <div key={key} className=\"flex justify-between py-2 border-b border-gray-100 last:border-b-0\">\n                      <span className=\"text-gray-600\">{key}</span>\n                      <span className=\"font-medium\">{value}</span>\n                    </div>\n                  ))}\n                </div>\n              </Card.Content>\n            </Card>\n          )}\n        </div>\n      </div>\n\n      {/* 推荐商品 */}\n      {recommendedProducts && recommendedProducts.length > 0 && (\n        <section>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">相关推荐</h2>\n          <ProductGrid\n            products={recommendedProducts}\n            onProductClick={handleProductClick}\n          />\n        </section>\n      )}\n    </div>\n  );\n};\n\nexport default ProductDetailPage;\n", "import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport Header from './components/Header';\nimport HomePage from './pages/HomePage';\nimport ProductDetailPage from './pages/ProductDetailPage';\n\n// 创建 React Query 客户端\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5分钟\n    },\n  },\n});\n\nconst App: React.FC = () => {\n  const handleSearch = (keyword: string) => {\n    // 这里可以添加搜索逻辑，比如导航到搜索页面\n    console.log('搜索关键词:', keyword);\n  };\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <Router>\n        <div className=\"min-h-screen bg-gray-50\">\n          <Header onSearch={handleSearch} />\n          \n          <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <Routes>\n              <Route path=\"/\" element={<HomePage />} />\n              <Route path=\"/products/:id\" element={<ProductDetailPage />} />\n              <Route path=\"*\" element={<NotFoundPage />} />\n            </Routes>\n          </main>\n          \n          <Footer />\n        </div>\n      </Router>\n    </QueryClientProvider>\n  );\n};\n\n// 404页面组件\nconst NotFoundPage: React.FC = () => {\n  return (\n    <div className=\"text-center py-12\">\n      <div className=\"text-gray-400 text-6xl mb-4\">🔍</div>\n      <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">页面未找到</h1>\n      <p className=\"text-gray-600 mb-6\">抱歉，您访问的页面不存在</p>\n      <a\n        href=\"/\"\n        className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors\"\n      >\n        返回首页\n      </a>\n    </div>\n  );\n};\n\n// 页脚组件\nconst Footer: React.FC = () => {\n  return (\n    <footer className=\"bg-white border-t mt-16\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid md:grid-cols-4 gap-8\">\n          <div>\n            <h3 className=\"font-semibold text-gray-900 mb-4\">关于我们</h3>\n            <p className=\"text-gray-600 text-sm\">\n              现代化商品展示系统，基于React和SpringBoot构建的高性能电商展示应用。\n            </p>\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 mb-4\">快速链接</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li><a href=\"/\" className=\"text-gray-600 hover:text-primary-600\">首页</a></li>\n              <li><a href=\"/products\" className=\"text-gray-600 hover:text-primary-600\">商品</a></li>\n              <li><a href=\"/categories\" className=\"text-gray-600 hover:text-primary-600\">分类</a></li>\n              <li><a href=\"/cart\" className=\"text-gray-600 hover:text-primary-600\">购物车</a></li>\n            </ul>\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 mb-4\">技术栈</h3>\n            <ul className=\"space-y-2 text-sm text-gray-600\">\n              <li>React 18 + TypeScript</li>\n              <li>Vite + Tailwind CSS</li>\n              <li>React Query + Zustand</li>\n              <li>SpringBoot + MySQL</li>\n            </ul>\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 mb-4\">联系我们</h3>\n            <ul className=\"space-y-2 text-sm text-gray-600\">\n              <li>邮箱: <EMAIL></li>\n              <li>电话: +86 123-4567-8900</li>\n              <li>地址: 北京市朝阳区</li>\n            </ul>\n          </div>\n        </div>\n        <div className=\"border-t mt-8 pt-8 text-center text-sm text-gray-600\">\n          <p>&copy; 2024 现代化商品展示系统. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default App;\n", "import React from 'react'\nimport { createRoot } from 'react-dom/client'\nimport App from './App'\nimport './index.css'\n\ncreateRoot(document.getElementById('root')!).render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>,\n)\n"], "names": ["m", "require$$0", "createRoot", "toKebabCase", "string", "toCamelCase", "match", "p1", "p2", "toPascalCase", "camelCase", "mergeClasses", "classes", "className", "index", "array", "hasA11yProp", "props", "prop", "defaultAttributes", "Icon", "forwardRef", "color", "size", "strokeWidth", "absoluteStrokeWidth", "children", "iconNode", "rest", "ref", "createElement", "tag", "attrs", "createLucideIcon", "iconName", "Component", "__iconNode", "ArrowLeft", "<PERSON><PERSON>", "Minus", "Plus", "Search", "ShoppingCart", "Star", "X", "createStoreImpl", "createState", "state", "listeners", "setState", "partial", "replace", "nextState", "previousState", "listener", "getState", "api", "initialState", "createStore", "identity", "arg", "useStore", "selector", "slice", "React", "createImpl", "useBoundStore", "create", "createJSONStorage", "getStorage", "options", "storage", "name", "_a", "parse", "str2", "str", "newValue", "toThenable", "fn", "input", "result", "onFulfilled", "_onRejected", "e", "_onFulfilled", "onRejected", "persistImpl", "config", "baseOptions", "set", "get", "persistedState", "currentState", "hasHydrated", "hydrationListeners", "finishHydrationListeners", "args", "setItem", "savedSetState", "config<PERSON><PERSON><PERSON>", "stateFromStorage", "hydrate", "_b", "cb", "_a2", "postRehydrationCallback", "deserializedStorageValue", "migration", "migrationResult", "migrated", "migratedState", "newOptions", "persist", "calculateTotals", "items", "totalItems", "sum", "item", "totalPrice", "useCartStore", "product", "quantity", "existingItem", "newItems", "newItem", "productId", "r", "f", "clsx", "CLASS_PART_SEPARATOR", "createClassGroupUtils", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "classParts", "getGroupRecursive", "getGroupIdForArbitraryProperty", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "classGroupFromNextClassPart", "classRest", "validator", "arbitraryPropertyRegex", "arbitraryPropertyClassName", "property", "theme", "classGroups", "processClassesRecursively", "classGroup", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "key", "path", "currentClassPartObject", "pathPart", "func", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "value", "IMPORTANT_MODIFIER", "MODIFIER_SEPARATOR", "MODIFIER_SEPARATOR_LENGTH", "createParseClassName", "prefix", "experimentalParseClassName", "parseClassName", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "currentCharacter", "baseClassNameWithImportantModifier", "baseClassName", "stripImportantModifier", "hasImportantModifier", "maybePostfixModifierPosition", "fullPrefix", "parseClassNameOriginal", "createSortModifiers", "orderSensitiveModifiers", "modifier", "sortedModifiers", "unsortedModifiers", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "getClassGroupId", "getConflictingClassGroupIds", "sortModifiers", "classGroupsInConflict", "classNames", "originalClassName", "isExternal", "variantModifier", "modifierId", "classId", "conflictGroups", "i", "group", "twJoin", "argument", "resolvedValue", "toValue", "mix", "k", "createTailwindMerge", "createConfigFirst", "createConfigRest", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "fromTheme", "themeGetter", "arbitraryValueRegex", "arbitraryVariableRegex", "fractionRegex", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "isFraction", "isNumber", "isInteger", "isPercent", "isTshirtSize", "isAny", "is<PERSON>engthOnly", "isNever", "is<PERSON><PERSON>ow", "isImage", "isAnyNonArbitrary", "isArbitraryValue", "isArbitraryVariable", "isArbitrarySize", "getIsArbitraryValue", "isLabelSize", "isArbitraryLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArbitraryNumber", "isLabelNumber", "isArbitraryPosition", "isLabelPosition", "isArbitraryImage", "isLabelImage", "isArbitraryShadow", "isLabel<PERSON><PERSON>ow", "isArbitraryVariableLength", "getIsArbitraryVariable", "isArbitraryVariableFamilyName", "isLabelFamilyName", "isArbitraryVariablePosition", "isArbitraryVariableSize", "isArbitraryVariableImage", "isArbitraryVariableShadow", "test<PERSON><PERSON><PERSON>", "testValue", "shouldMatchNoLabel", "label", "getDefaultConfig", "themeColor", "themeFont", "themeText", "themeFontWeight", "themeTracking", "themeLeading", "themeBreakpoint", "themeContainer", "themeSpacing", "themeRadius", "themeShadow", "themeInsetShadow", "themeTextShadow", "themeDropShadow", "themeBlur", "themePerspective", "themeAspect", "themeEase", "themeAnimate", "scaleBreak", "scalePosition", "scalePositionWithArbitrary", "scaleOverflow", "scaleOverscroll", "scaleUnambiguousSpacing", "scaleInset", "scaleGridTemplateColsRows", "scaleGridColRowStartAndEnd", "scaleGridColRowStartOrEnd", "scaleGridAutoColsRows", "scaleAlignPrimaryAxis", "scaleAlignSecondaryAxis", "scaleMargin", "scaleSizing", "scaleColor", "scaleBgPosition", "scaleBgRepeat", "scaleBgSize", "scaleGradientStopPosition", "scaleRadius", "scaleBorderWidth", "scaleLineStyle", "scaleBlendMode", "scaleMaskImagePosition", "scaleBlur", "scaleRotate", "scaleScale", "scaleSkew", "scaleTranslate", "twMerge", "cn", "inputs", "formatPrice", "price", "currency", "calculateDiscount", "originalPrice", "currentPrice", "debounce", "wait", "timeout", "generateStars", "rating", "maxStars", "_", "<PERSON><PERSON>", "variant", "loading", "disabled", "baseClasses", "variantClasses", "sizeClasses", "jsxs", "jsx", "SearchBar", "onSearch", "placeholder", "defaultValue", "keyword", "setKeyword", "useState", "debouncedSearch", "useCallback", "Header", "isMobileMenuOpen", "setIsMobileMenuOpen", "toggleMobileMenu", "Link", "bind", "thisArg", "toString", "getPrototypeOf", "iterator", "toStringTag", "kindOf", "thing", "kindOfTest", "type", "typeOfTest", "isArray", "isUndefined", "<PERSON><PERSON><PERSON><PERSON>", "val", "isFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isArrayBuffer<PERSON>iew", "isString", "isObject", "isBoolean", "isPlainObject", "prototype", "isDate", "isFile", "isBlob", "isFileList", "isStream", "isFormData", "kind", "isURLSearchParams", "isReadableStream", "isRequest", "isResponse", "isHeaders", "trim", "for<PERSON>ach", "obj", "allOwnKeys", "l", "keys", "len", "<PERSON><PERSON><PERSON>", "_key", "_global", "isContextDefined", "context", "merge", "caseless", "assignValue", "<PERSON><PERSON><PERSON>", "extend", "a", "b", "stripBOM", "content", "inherits", "constructor", "superConstructor", "descriptors", "toFlatObject", "sourceObj", "destObj", "filter", "propFilter", "merged", "endsWith", "searchString", "position", "lastIndex", "toArray", "arr", "isTypedArray", "TypedArray", "forEachEntry", "_iterator", "pair", "matchAll", "regExp", "matches", "isHTMLForm", "hasOwnProperty", "isRegExp", "reduceDescriptors", "reducer", "reducedDescriptors", "descriptor", "ret", "freezeMethods", "toObjectSet", "arrayOrString", "delimiter", "define", "noop", "toFiniteNumber", "isSpecCompliantForm", "toJSONObject", "stack", "visit", "source", "target", "reducedValue", "isAsyncFn", "isThenable", "_setImmediate", "setImmediateSupported", "postMessageSupported", "token", "callbacks", "data", "asap", "isIterable", "utils$1", "AxiosError", "message", "code", "request", "response", "utils", "error", "customProps", "axiosError", "httpAdapter", "isVisitable", "removeBrackets", "<PERSON><PERSON><PERSON>", "dots", "isFlatArray", "predicates", "toFormData", "formData", "option", "metaTokens", "visitor", "defaultVisitor", "indexes", "useBlob", "convertValue", "el", "exposedHelpers", "build", "encode", "charMap", "AxiosURLSearchParams", "params", "encoder", "_encode", "buildURL", "url", "serializeFn", "serializedParams", "hashmarkIndex", "InterceptorManager", "fulfilled", "rejected", "id", "h", "InterceptorManager$1", "transitionalD<PERSON>ault<PERSON>", "URLSearchParams$1", "FormData$1", "Blob$1", "platform$1", "URLSearchParams", "FormData", "Blob", "hasBrowserEnv", "_navigator", "hasStandardBrowserEnv", "hasStandardBrowserWebWorkerEnv", "origin", "platform", "toURLEncodedForm", "helpers", "parsePropPath", "arrayToObject", "formDataToJSON", "buildPath", "isNumericKey", "isLast", "stringifySafely", "rawValue", "parser", "defaults", "headers", "contentType", "hasJSONContentType", "isObjectPayload", "_FormData", "transitional", "forcedJSONParsing", "JSONRequested", "strictJSONParsing", "status", "method", "defaults$1", "ignoreDuplicateOf", "parseHeaders", "rawHeaders", "parsed", "line", "$internals", "normalizeHeader", "header", "normalizeValue", "parseTokens", "tokens", "tokensRE", "isValidHeaderName", "matchHeaderValue", "isHeaderNameFilter", "formatHeader", "w", "char", "buildAccessors", "accessorName", "methodName", "arg1", "arg2", "arg3", "AxiosHeaders", "valueOrRewrite", "rewrite", "self", "<PERSON><PERSON><PERSON><PERSON>", "_value", "_header", "_rewrite", "<PERSON><PERSON><PERSON><PERSON>", "setHeaders", "dest", "entry", "matcher", "deleted", "deleteHeader", "format", "normalized", "targets", "asStrings", "first", "computed", "accessors", "defineAccessor", "mapped", "headerValue", "AxiosHeaders$1", "transformData", "fns", "isCancel", "CanceledError", "settle", "resolve", "reject", "validateStatus", "parseProtocol", "speedometer", "samplesCount", "min", "bytes", "timestamps", "head", "tail", "firstSampleTS", "chunkLength", "now", "startedAt", "bytesCount", "passed", "throttle", "freq", "timestamp", "threshold", "lastArgs", "timer", "invoke", "progressEventReducer", "isDownloadStream", "bytesNotified", "_speedometer", "loaded", "total", "progressBytes", "rate", "inRange", "progressEventDecorator", "throttled", "lengthComputable", "asyncDecorator", "isURLSameOrigin", "isMSIE", "cookies", "expires", "domain", "secure", "cookie", "isAbsoluteURL", "combineURLs", "baseURL", "relativeURL", "buildFullPath", "requestedURL", "allowAbsoluteUrls", "isRelativeUrl", "headersToObject", "mergeConfig", "config1", "config2", "getMergedValue", "mergeDeepProperties", "valueFromConfig2", "defaultToConfig2", "mergeDirectKeys", "mergeMap", "config<PERSON><PERSON><PERSON>", "resolveConfig", "newConfig", "withXSRFToken", "xsrfHeaderName", "xsrfCookieName", "auth", "xsrfValue", "isXHRAdapterSupported", "xhrAdapter", "_config", "requestData", "requestHeaders", "responseType", "onUploadProgress", "onDownloadProgress", "onCanceled", "uploadThrottled", "downloadThrottled", "flushUpload", "flushDownload", "done", "onloadend", "responseHeaders", "err", "timeoutErrorMessage", "cancel", "protocol", "composeSignals", "signals", "length", "controller", "aborted", "<PERSON>ab<PERSON>", "reason", "unsubscribe", "signal", "composeSignals$1", "streamChunk", "chunk", "chunkSize", "pos", "end", "readBytes", "iterable", "readStream", "stream", "reader", "trackStream", "onProgress", "onFinish", "_onFinish", "loadedBytes", "isFetchSupported", "isReadableStreamSupported", "encodeText", "test", "supportsRequestStream", "duplexAccessed", "hasContentType", "DEFAULT_CHUNK_SIZE", "supportsResponseStream", "resolvers", "res", "getBody<PERSON><PERSON>th", "body", "resolveBody<PERSON><PERSON>th", "fetchAdapter", "cancelToken", "withCredentials", "fetchOptions", "composedSignal", "requestContentLength", "_request", "contentTypeHeader", "flush", "isCredentialsSupported", "isStreamResponse", "responseContentLength", "responseData", "knownAdapters", "renderReason", "isResolvedHandle", "adapter", "adapters", "nameOrAdapter", "rejectedReasons", "reasons", "s", "throwIfCancellationRequested", "dispatchRequest", "VERSION", "validators", "deprecatedWarnings", "version", "formatMessage", "opt", "desc", "opts", "correctSpelling", "assertOptions", "schema", "allowUnknown", "A<PERSON>os", "instanceConfig", "configOrUrl", "dummy", "paramsSerializer", "contextHeaders", "requestInterceptorChain", "synchronousRequestInterceptors", "interceptor", "responseInterceptorChain", "promise", "chain", "fullPath", "generateHTTPMethod", "isForm", "Axios$1", "CancelToken", "executor", "resolvePromise", "onfulfilled", "_resolve", "abort", "c", "CancelToken$1", "spread", "callback", "isAxiosError", "payload", "HttpStatusCode", "HttpStatusCode$1", "createInstance", "defaultConfig", "instance", "axios", "promises", "axios$1", "productApi", "limit", "categoryApi", "category", "cat", "categoryId", "useProducts", "useQuery", "useProduct", "usePopularProducts", "useRecommendedProducts", "useCategories", "Card", "hover", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "CardWithSubComponents", "ProductCard", "addItem", "isInCart", "getItemQuantity", "discount", "stars", "inCart", "handleAddToCart", "filled", "Loading", "PageLoading", "Skeleton", "ProductCardSkeleton", "ProductGrid", "products", "onProductClick", "HomePage", "navigate", "useNavigate", "searchParams", "productsData", "productsLoading", "popularProducts", "popularLoading", "categories", "categoriesLoading", "handleProductClick", "handleCategoryClick", "ProductDetailPage", "useParams", "setQuantity", "selectedImage", "setSelectedImage", "isLoading", "recommendedProducts", "cartQuantity", "images", "handleQuantityChange", "delta", "newQuantity", "clickedProduct", "image", "Fragment", "queryClient", "QueryClient", "App", "QueryClientProvider", "Router", "Routes", "Route", "NotFoundPage", "Footer"], "mappings": "u6BAEIA,GAAIC,GAENC,GAAqBF,GAAE,WACDA,GAAE,YCL1B;AAAA;AAAA;AAAA;AAAA;AAAA,GAOA,MAAMG,GAAeC,GAAWA,EAAO,QAAQ,qBAAsB,OAAO,EAAE,cACxEC,GAAeD,GAAWA,EAAO,QACrC,wBACA,CAACE,EAAOC,EAAIC,IAAOA,EAAKA,EAAG,YAAW,EAAKD,EAAG,YAAa,CAC7D,EACME,GAAgBL,GAAW,CAC/B,MAAMM,EAAYL,GAAYD,CAAM,EACpC,OAAOM,EAAU,OAAO,CAAC,EAAE,YAAW,EAAKA,EAAU,MAAM,CAAC,CAC9D,EACMC,GAAe,IAAIC,IAAYA,EAAQ,OAAO,CAACC,EAAWC,EAAOC,IAC9D,EAAQF,GAAcA,EAAU,KAAI,IAAO,IAAME,EAAM,QAAQF,CAAS,IAAMC,CACtF,EAAE,KAAK,GAAG,EAAE,KAAI,EACXE,GAAeC,GAAU,CAC7B,UAAWC,KAAQD,EACjB,GAAIC,EAAK,WAAW,OAAO,GAAKA,IAAS,QAAUA,IAAS,QAC1D,MAAO,EAGb,ECzBA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOA,IAAIC,GAAoB,CACtB,MAAO,6BACP,MAAO,GACP,OAAQ,GACR,QAAS,YACT,KAAM,OACN,OAAQ,eACR,YAAa,EACb,cAAe,QACf,eAAgB,OAClB,ECjBA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWA,MAAMC,GAAOC,EAAU,WACrB,CAAC,CACC,MAAAC,EAAQ,eACR,KAAAC,EAAO,GACP,YAAAC,EAAc,EACd,oBAAAC,EACA,UAAAZ,EAAY,GACZ,SAAAa,EACA,SAAAC,EACA,GAAGC,CACJ,EAAEC,IAAQC,EAAa,cACtB,MACA,CACE,IAAAD,EACA,GAAGV,GACH,MAAOI,EACP,OAAQA,EACR,OAAQD,EACR,YAAaG,EAAsB,OAAOD,CAAW,EAAI,GAAK,OAAOD,CAAI,EAAIC,EAC7E,UAAWb,GAAa,SAAUE,CAAS,EAC3C,GAAG,CAACa,GAAY,CAACV,GAAYY,CAAI,GAAK,CAAE,cAAe,MAAQ,EAC/D,GAAGA,CACJ,EACD,CACE,GAAGD,EAAS,IAAI,CAAC,CAACI,EAAKC,CAAK,IAAMF,EAAa,cAACC,EAAKC,CAAK,CAAC,EAC3D,GAAG,MAAM,QAAQN,CAAQ,EAAIA,EAAW,CAACA,CAAQ,CAClD,CACF,CACH,ECvCA;AAAA;AAAA;AAAA;AAAA;AAAA,GAWA,MAAMO,EAAmB,CAACC,EAAUP,IAAa,CAC/C,MAAMQ,EAAYd,EAAU,WAC1B,CAAC,CAAE,UAAAR,EAAW,GAAGI,CAAO,EAAEY,IAAQC,EAAa,cAACV,GAAM,CACpD,IAAAS,EACA,SAAAF,EACA,UAAWhB,GACT,UAAUR,GAAYM,GAAayB,CAAQ,CAAC,CAAC,GAC7C,UAAUA,CAAQ,GAClBrB,CACD,EACD,GAAGI,CACT,CAAK,CACL,EACE,OAAAkB,EAAU,YAAc1B,GAAayB,CAAQ,EACtCC,CACT,EC1BA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMC,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,iBAAkB,IAAK,QAAQ,CAAE,EAC/C,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,CAC3C,EACMC,GAAYJ,EAAiB,aAAcG,EAAU,ECb3D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAQ,CAAE,CAC1C,EACME,GAAOL,EAAiB,OAAQG,EAAU,ECdhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CAAC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAAC,EACxDG,GAAQN,EAAiB,QAASG,EAAU,ECVlD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,CAC3C,EACMI,GAAOP,EAAiB,OAAQG,EAAU,ECbhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,mBAAoB,IAAK,QAAQ,CAAE,EACjD,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,SAAU,CAC1D,EACMK,GAASR,EAAiB,SAAUG,EAAU,ECbpD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,SAAU,CAAE,GAAI,IAAK,GAAI,KAAM,EAAG,IAAK,IAAK,SAAU,EACvD,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,SAAU,EACxD,CACE,OACA,CACE,EAAG,mFACH,IAAK,QACN,CACF,CACH,EACMM,GAAeT,EAAiB,gBAAiBG,EAAU,ECpBjE;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,+WACH,IAAK,QACN,CACF,CACH,EACMO,GAAOV,EAAiB,OAAQG,EAAU,EClBhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,aAAc,IAAK,QAAQ,CAAE,EAC3C,CAAC,OAAQ,CAAE,EAAG,aAAc,IAAK,QAAQ,CAAE,CAC7C,EACMQ,GAAIX,EAAiB,IAAKG,EAAU,ECbpCS,GAAmBC,GAAgB,CACvC,IAAIC,EACJ,MAAMC,EAA4B,IAAI,IAChCC,EAAW,CAACC,EAASC,IAAY,CACrC,MAAMC,EAAY,OAAOF,GAAY,WAAaA,EAAQH,CAAK,EAAIG,EACnE,GAAI,CAAC,OAAO,GAAGE,EAAWL,CAAK,EAAG,CAChC,MAAMM,EAAgBN,EACtBA,EAASI,IAA4B,OAAOC,GAAc,UAAYA,IAAc,MAAQA,EAAY,OAAO,OAAO,CAAA,EAAIL,EAAOK,CAAS,EAC1IJ,EAAU,QAASM,GAAaA,EAASP,EAAOM,CAAa,CAAC,CAC/D,CACL,EACQE,EAAW,IAAMR,EAMjBS,EAAM,CAAE,SAAAP,EAAU,SAAAM,EAAU,gBALV,IAAME,EAKqB,UAJhCH,IACjBN,EAAU,IAAIM,CAAQ,EACf,IAAMN,EAAU,OAAOM,CAAQ,EAEoB,EACtDG,EAAeV,EAAQD,EAAYG,EAAUM,EAAUC,CAAG,EAChE,OAAOA,CACT,EACME,GAAeZ,GAAgBA,EAAcD,GAAgBC,CAAW,EAAID,GClB5Ec,GAAYC,GAAQA,EAC1B,SAASC,GAASL,EAAKM,EAAWH,GAAU,CAC1C,MAAMI,EAAQC,GAAM,qBAClBR,EAAI,UACJ,IAAMM,EAASN,EAAI,UAAU,EAC7B,IAAMM,EAASN,EAAI,iBAAiB,CACxC,EACE,OAAAQ,GAAM,cAAcD,CAAK,EAClBA,CACT,CACA,MAAME,GAAcnB,GAAgB,CAClC,MAAMU,EAAME,GAAYZ,CAAW,EAC7BoB,EAAiBJ,GAAaD,GAASL,EAAKM,CAAQ,EAC1D,cAAO,OAAOI,EAAeV,CAAG,EACzBU,CACT,EACMC,GAAUrB,GAAgBA,EAAcmB,GAAWnB,CAAW,EAAImB,GC+PxE,SAASG,GAAkBC,EAAYC,EAAS,CAC9C,IAAIC,EACJ,GAAI,CACFA,EAAUF,EAAU,CACrB,MAAW,CACV,MACD,CAmBD,MAlBuB,CACrB,QAAUG,GAAS,CACjB,IAAIC,EACJ,MAAMC,EAASC,GACTA,IAAS,KACJ,KAEF,KAAK,MAAMA,EAAML,GAAW,KAAO,OAASA,EAAQ,OAAO,EAE9DM,GAAOH,EAAKF,EAAQ,QAAQC,CAAI,IAAM,KAAOC,EAAK,KACxD,OAAIG,aAAe,QACVA,EAAI,KAAKF,CAAK,EAEhBA,EAAME,CAAG,CACjB,EACD,QAAS,CAACJ,EAAMK,IAAaN,EAAQ,QAAQC,EAAM,KAAK,UAAUK,EAAUP,GAAW,KAAO,OAASA,EAAQ,QAAQ,CAAC,EACxH,WAAaE,GAASD,EAAQ,WAAWC,CAAI,CACjD,CAEA,CACA,MAAMM,GAAcC,GAAQC,GAAU,CACpC,GAAI,CACF,MAAMC,EAASF,EAAGC,CAAK,EACvB,OAAIC,aAAkB,QACbA,EAEF,CACL,KAAKC,EAAa,CAChB,OAAOJ,GAAWI,CAAW,EAAED,CAAM,CACtC,EACD,MAAME,EAAa,CACjB,OAAO,IACR,CACP,CACG,OAAQC,EAAG,CACV,MAAO,CACL,KAAKC,EAAc,CACjB,OAAO,IACR,EACD,MAAMC,EAAY,CAChB,OAAOR,GAAWQ,CAAU,EAAEF,CAAC,CAChC,CACP,CACG,CACH,EACMG,GAAc,CAACC,EAAQC,IAAgB,CAACC,EAAKC,EAAKnC,IAAQ,CAC9D,IAAIc,EAAU,CACZ,QAASF,GAAkB,IAAM,YAAY,EAC7C,WAAarB,GAAUA,EACvB,QAAS,EACT,MAAO,CAAC6C,EAAgBC,KAAkB,CACxC,GAAGA,EACH,GAAGD,CACT,GACI,GAAGH,CACP,EACMK,EAAc,GAClB,MAAMC,EAAqC,IAAI,IACzCC,EAA2C,IAAI,IACrD,IAAIzB,EAAUD,EAAQ,QACtB,GAAI,CAACC,EACH,OAAOiB,EACL,IAAIS,IAAS,CACX,QAAQ,KACN,uDAAuD3B,EAAQ,IAAI,gDAC7E,EACQoB,EAAI,GAAGO,CAAI,CACZ,EACDN,EACAnC,CACN,EAEE,MAAM0C,EAAU,IAAM,CACpB,MAAMnD,EAAQuB,EAAQ,WAAW,CAAE,GAAGqB,EAAK,CAAA,CAAE,EAC7C,OAAOpB,EAAQ,QAAQD,EAAQ,KAAM,CACnC,MAAAvB,EACA,QAASuB,EAAQ,OACvB,CAAK,CACL,EACQ6B,EAAgB3C,EAAI,SAC1BA,EAAI,SAAW,CAACT,EAAOI,IAAY,CACjCgD,EAAcpD,EAAOI,CAAO,EACvB+C,EAAO,CAChB,EACE,MAAME,EAAeZ,EACnB,IAAIS,IAAS,CACXP,EAAI,GAAGO,CAAI,EACNC,EAAO,CACb,EACDP,EACAnC,CACJ,EACEA,EAAI,gBAAkB,IAAM4C,EAC5B,IAAIC,EACJ,MAAMC,EAAU,IAAM,CACpB,IAAI7B,EAAI8B,EACR,GAAI,CAAChC,EAAS,OACduB,EAAc,GACdC,EAAmB,QAASS,GAAO,CACjC,IAAIC,EACJ,OAAOD,GAAIC,EAAMd,EAAK,IAAK,KAAOc,EAAML,CAAY,CAC1D,CAAK,EACD,MAAMM,IAA4BH,EAAKjC,EAAQ,qBAAuB,KAAO,OAASiC,EAAG,KAAKjC,GAAUG,EAAKkB,EAAK,IAAK,KAAOlB,EAAK2B,CAAY,IAAM,OACrJ,OAAOtB,GAAWP,EAAQ,QAAQ,KAAKA,CAAO,CAAC,EAAED,EAAQ,IAAI,EAAE,KAAMqC,GAA6B,CAChG,GAAIA,EACF,GAAI,OAAOA,EAAyB,SAAY,UAAYA,EAAyB,UAAYrC,EAAQ,QAAS,CAChH,GAAIA,EAAQ,QAAS,CACnB,MAAMsC,EAAYtC,EAAQ,QACxBqC,EAAyB,MACzBA,EAAyB,OACvC,EACY,OAAIC,aAAqB,QAChBA,EAAU,KAAM3B,GAAW,CAAC,GAAMA,CAAM,CAAC,EAE3C,CAAC,GAAM2B,CAAS,CACxB,CACD,QAAQ,MACN,uFACZ,CACA,KACU,OAAO,CAAC,GAAOD,EAAyB,KAAK,EAGjD,MAAO,CAAC,GAAO,MAAM,CAC3B,CAAK,EAAE,KAAME,GAAoB,CAC3B,IAAIJ,EACJ,KAAM,CAACK,EAAUC,CAAa,EAAIF,EAMlC,GALAR,EAAmB/B,EAAQ,MACzByC,GACCN,EAAMd,EAAG,IAAO,KAAOc,EAAML,CACtC,EACMV,EAAIW,EAAkB,EAAI,EACtBS,EACF,OAAOZ,EAAO,CAEtB,CAAK,EAAE,KAAK,IAAM,CACZQ,GAA2B,MAAgBA,EAAwBL,EAAkB,MAAM,EAC3FA,EAAmBV,EAAG,EACtBG,EAAc,GACdE,EAAyB,QAASQ,GAAOA,EAAGH,CAAgB,CAAC,CACnE,CAAK,EAAE,MAAOjB,GAAM,CACdsB,GAA2B,MAAgBA,EAAwB,OAAQtB,CAAC,CAClF,CAAK,CACL,EACE,OAAA5B,EAAI,QAAU,CACZ,WAAawD,GAAe,CAC1B1C,EAAU,CACR,GAAGA,EACH,GAAG0C,CACX,EACUA,EAAW,UACbzC,EAAUyC,EAAW,QAExB,EACD,aAAc,IAAM,CAClBzC,GAAW,MAAgBA,EAAQ,WAAWD,EAAQ,IAAI,CAC3D,EACD,WAAY,IAAMA,EAClB,UAAW,IAAMgC,EAAS,EAC1B,YAAa,IAAMR,EACnB,UAAYU,IACVT,EAAmB,IAAIS,CAAE,EAClB,IAAM,CACXT,EAAmB,OAAOS,CAAE,CACpC,GAEI,kBAAoBA,IAClBR,EAAyB,IAAIQ,CAAE,EACxB,IAAM,CACXR,EAAyB,OAAOQ,CAAE,CAC1C,EAEA,EACOlC,EAAQ,eACXgC,IAEKD,GAAoBD,CAC7B,EACMa,GAAU1B,GC5bV2B,GAAmBC,GAAsB,CACvC,MAAAC,EAAaD,EAAM,OAAO,CAACE,EAAKC,IAASD,EAAMC,EAAK,SAAU,CAAC,EAC/DC,EAAaJ,EAAM,OAAO,CAACE,EAAKC,IAASD,EAAOC,EAAK,QAAQ,MAAQA,EAAK,SAAW,CAAC,EACrF,MAAA,CAAE,WAAAF,EAAY,WAAAG,EACvB,EAEaC,GAAerD,GAAkB,EAC5C8C,GACE,CAACvB,EAAKC,KAAS,CACb,MAAO,CAAC,EACR,WAAY,EACZ,WAAY,EAEZ,QAAS,CAAC8B,EAAkBC,EAAmB,IAAM,CACnDhC,EAAK3C,GAAU,CACP,MAAA4E,EAAe5E,EAAM,MAAM,QAAauE,EAAK,KAAOG,EAAQ,EAAE,EAEhE,IAAAG,EACJ,GAAID,EAEFC,EAAW7E,EAAM,MAAM,IACrBuE,GAAAA,EAAK,KAAOG,EAAQ,GAChB,CAAE,GAAGH,EAAM,SAAUA,EAAK,SAAWI,CACrC,EAAAJ,CAAA,MAED,CAEL,MAAMO,EAAoB,CACxB,GAAIJ,EAAQ,GACZ,QAAAA,EACA,SAAAC,EACA,WAAY,IAAI,KAAK,EAAE,YAAY,CAAA,EAErCE,EAAW,CAAC,GAAG7E,EAAM,MAAO8E,CAAO,CACrC,CAEA,KAAM,CAAE,WAAAT,EAAY,WAAAG,CAAW,EAAIL,GAAgBU,CAAQ,EACpD,MAAA,CACL,MAAOA,EACP,WAAAR,EACA,WAAAG,CAAA,CACF,CACD,CACH,EAEA,WAAaO,GAAsB,CACjCpC,EAAK3C,GAAU,CACb,MAAM6E,EAAW7E,EAAM,MAAM,OAAeuE,GAAAA,EAAK,KAAOQ,CAAS,EAC3D,CAAE,WAAAV,EAAY,WAAAG,CAAW,EAAIL,GAAgBU,CAAQ,EACpD,MAAA,CACL,MAAOA,EACP,WAAAR,EACA,WAAAG,CAAA,CACF,CACD,CACH,EAEA,eAAgB,CAACO,EAAmBJ,IAAqB,CACvD,GAAIA,GAAY,EAAG,CACb/B,EAAA,EAAE,WAAWmC,CAAS,EAC1B,MACF,CAEApC,EAAK3C,GAAU,CACP,MAAA6E,EAAW7E,EAAM,MAAM,IAAIuE,GAC/BA,EAAK,KAAOQ,EACR,CAAE,GAAGR,EAAM,SAAAI,GACXJ,CAAA,EAEA,CAAE,WAAAF,EAAY,WAAAG,CAAW,EAAIL,GAAgBU,CAAQ,EACpD,MAAA,CACL,MAAOA,EACP,WAAAR,EACA,WAAAG,CAAA,CACF,CACD,CACH,EAEA,UAAW,IAAM,CACX7B,EAAA,CACF,MAAO,CAAC,EACR,WAAY,EACZ,WAAY,CAAA,CACb,CACH,EAEA,gBAAkBoC,GAAsB,CAChC,MAAAR,EAAO3B,EAAM,EAAA,MAAM,KAAK2B,GAAQA,EAAK,KAAOQ,CAAS,EACpD,OAAAR,EAAOA,EAAK,SAAW,CAChC,EAEA,SAAWQ,GACFnC,IAAM,MAAM,KAAa2B,GAAAA,EAAK,KAAOQ,CAAS,CACvD,GAEF,CACE,KAAM,eACN,WAAa/E,IAAW,CACtB,MAAOA,EAAM,MACb,WAAYA,EAAM,WAClB,WAAYA,EAAM,UAAA,EAEtB,CACF,CACF,ECvHA,SAASgF,GAAE,EAAE,CAAC,IAAI,EAAEC,EAAE,EAAE,GAAG,GAAa,OAAO,GAAjB,UAA8B,OAAO,GAAjB,SAAmB,GAAG,UAAoB,OAAO,GAAjB,SAAmB,GAAG,MAAM,QAAQ,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO,IAAI,EAAE,CAAC,IAAIA,EAAED,GAAE,EAAE,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,GAAGC,OAAQ,KAAI,KAAK,EAAE,EAAE,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,GAAG,OAAO,CAAC,CAAQ,SAASC,IAAM,CAAC,QAAQ,EAAE,EAAED,EAAE,EAAE,EAAE,GAAGA,EAAE,UAAU,SAAS,EAAE,UAAUA,GAAG,KAAK,EAAED,GAAE,CAAC,KAAK,IAAI,GAAG,KAAK,GAAG,GAAG,OAAO,CAAC,CCAjW,MAAMG,GAAuB,IACvBC,GAAwB3C,GAAU,CACtC,MAAM4C,EAAWC,GAAe7C,CAAM,EAChC,CACJ,uBAAA8C,EACA,+BAAAC,CACD,EAAG/C,EAgBJ,MAAO,CACL,gBAhBsB3E,GAAa,CACnC,MAAM2H,EAAa3H,EAAU,MAAMqH,EAAoB,EAEvD,OAAIM,EAAW,CAAC,IAAM,IAAMA,EAAW,SAAW,GAChDA,EAAW,MAAK,EAEXC,GAAkBD,EAAYJ,CAAQ,GAAKM,GAA+B7H,CAAS,CAC9F,EAUI,4BATkC,CAAC8H,EAAcC,IAAuB,CACxE,MAAMC,EAAYP,EAAuBK,CAAY,GAAK,CAAA,EAC1D,OAAIC,GAAsBL,EAA+BI,CAAY,EAC5D,CAAC,GAAGE,EAAW,GAAGN,EAA+BI,CAAY,CAAC,EAEhEE,CACX,CAIA,CACA,EACMJ,GAAoB,CAACD,EAAYM,IAAoB,OACzD,GAAIN,EAAW,SAAW,EACxB,OAAOM,EAAgB,aAEzB,MAAMC,EAAmBP,EAAW,CAAC,EAC/BQ,EAAsBF,EAAgB,SAAS,IAAIC,CAAgB,EACnEE,EAA8BD,EAAsBP,GAAkBD,EAAW,MAAM,CAAC,EAAGQ,CAAmB,EAAI,OACxH,GAAIC,EACF,OAAOA,EAET,GAAIH,EAAgB,WAAW,SAAW,EACxC,OAEF,MAAMI,EAAYV,EAAW,KAAKN,EAAoB,EACtD,OAAOzD,EAAAqE,EAAgB,WAAW,KAAK,CAAC,CACtC,UAAAK,CACD,IAAKA,EAAUD,CAAS,CAAC,IAFnB,YAAAzE,EAEsB,YAC/B,EACM2E,GAAyB,aACzBV,GAAiC7H,GAAa,CAClD,GAAIuI,GAAuB,KAAKvI,CAAS,EAAG,CAC1C,MAAMwI,EAA6BD,GAAuB,KAAKvI,CAAS,EAAE,CAAC,EACrEyI,EAAWD,GAAA,YAAAA,EAA4B,UAAU,EAAGA,EAA2B,QAAQ,GAAG,GAChG,GAAIC,EAEF,MAAO,cAAgBA,CAE1B,CACH,EAIMjB,GAAiB7C,GAAU,CAC/B,KAAM,CACJ,MAAA+D,EACA,YAAAC,CACD,EAAGhE,EACE4C,EAAW,CACf,SAAU,IAAI,IACd,WAAY,CAAE,CAClB,EACE,UAAWO,KAAgBa,EACzBC,GAA0BD,EAAYb,CAAY,EAAGP,EAAUO,EAAcY,CAAK,EAEpF,OAAOnB,CACT,EACMqB,GAA4B,CAACC,EAAYZ,EAAiBH,EAAcY,IAAU,CACtFG,EAAW,QAAQC,GAAmB,CACpC,GAAI,OAAOA,GAAoB,SAAU,CACvC,MAAMC,EAAwBD,IAAoB,GAAKb,EAAkBe,GAAQf,EAAiBa,CAAe,EACjHC,EAAsB,aAAejB,EACrC,MACD,CACD,GAAI,OAAOgB,GAAoB,WAAY,CACzC,GAAIG,GAAcH,CAAe,EAAG,CAClCF,GAA0BE,EAAgBJ,CAAK,EAAGT,EAAiBH,EAAcY,CAAK,EACtF,MACD,CACDT,EAAgB,WAAW,KAAK,CAC9B,UAAWa,EACX,aAAAhB,CACR,CAAO,EACD,MACD,CACD,OAAO,QAAQgB,CAAe,EAAE,QAAQ,CAAC,CAACI,EAAKL,CAAU,IAAM,CAC7DD,GAA0BC,EAAYG,GAAQf,EAAiBiB,CAAG,EAAGpB,EAAcY,CAAK,CAC9F,CAAK,CACL,CAAG,CACH,EACMM,GAAU,CAACf,EAAiBkB,IAAS,CACzC,IAAIC,EAAyBnB,EAC7B,OAAAkB,EAAK,MAAM9B,EAAoB,EAAE,QAAQgC,GAAY,CAC9CD,EAAuB,SAAS,IAAIC,CAAQ,GAC/CD,EAAuB,SAAS,IAAIC,EAAU,CAC5C,SAAU,IAAI,IACd,WAAY,CAAE,CACtB,CAAO,EAEHD,EAAyBA,EAAuB,SAAS,IAAIC,CAAQ,CACzE,CAAG,EACMD,CACT,EACMH,GAAgBK,GAAQA,EAAK,cAG7BC,GAAiBC,GAAgB,CACrC,GAAIA,EAAe,EACjB,MAAO,CACL,IAAK,IAAA,GACL,IAAK,IAAM,CAAE,CACnB,EAEE,IAAIC,EAAY,EACZC,EAAQ,IAAI,IACZC,EAAgB,IAAI,IACxB,MAAMC,EAAS,CAACV,EAAKW,IAAU,CAC7BH,EAAM,IAAIR,EAAKW,CAAK,EACpBJ,IACIA,EAAYD,IACdC,EAAY,EACZE,EAAgBD,EAChBA,EAAQ,IAAI,IAElB,EACE,MAAO,CACL,IAAIR,EAAK,CACP,IAAIW,EAAQH,EAAM,IAAIR,CAAG,EACzB,GAAIW,IAAU,OACZ,OAAOA,EAET,IAAKA,EAAQF,EAAc,IAAIT,CAAG,KAAO,OACvC,OAAAU,EAAOV,EAAKW,CAAK,EACVA,CAEV,EACD,IAAIX,EAAKW,EAAO,CACVH,EAAM,IAAIR,CAAG,EACfQ,EAAM,IAAIR,EAAKW,CAAK,EAEpBD,EAAOV,EAAKW,CAAK,CAEpB,CACL,CACA,EACMC,GAAqB,IACrBC,GAAqB,IACrBC,GAA4BD,GAAmB,OAC/CE,GAAuBtF,GAAU,CACrC,KAAM,CACJ,OAAAuF,EACA,2BAAAC,CACD,EAAGxF,EAOJ,IAAIyF,EAAiBpK,GAAa,CAChC,MAAMqK,EAAY,CAAA,EAClB,IAAIC,EAAe,EACfC,EAAa,EACbC,EAAgB,EAChBC,EACJ,QAASxK,EAAQ,EAAGA,EAAQD,EAAU,OAAQC,IAAS,CACrD,IAAIyK,EAAmB1K,EAAUC,CAAK,EACtC,GAAIqK,IAAiB,GAAKC,IAAe,EAAG,CAC1C,GAAIG,IAAqBX,GAAoB,CAC3CM,EAAU,KAAKrK,EAAU,MAAMwK,EAAevK,CAAK,CAAC,EACpDuK,EAAgBvK,EAAQ+J,GACxB,QACD,CACD,GAAIU,IAAqB,IAAK,CAC5BD,EAA0BxK,EAC1B,QACD,CACF,CACGyK,IAAqB,IACvBJ,IACSI,IAAqB,IAC9BJ,IACSI,IAAqB,IAC9BH,IACSG,IAAqB,KAC9BH,GAEH,CACD,MAAMI,EAAqCN,EAAU,SAAW,EAAIrK,EAAYA,EAAU,UAAUwK,CAAa,EAC3GI,EAAgBC,GAAuBF,CAAkC,EACzEG,EAAuBF,IAAkBD,EACzCI,EAA+BN,GAA2BA,EAA0BD,EAAgBC,EAA0BD,EAAgB,OACpJ,MAAO,CACL,UAAAH,EACA,qBAAAS,EACA,cAAAF,EACA,6BAAAG,CACN,CACA,EACE,GAAIb,EAAQ,CACV,MAAMc,EAAad,EAASH,GACtBkB,EAAyBb,EAC/BA,EAAiBpK,GAAaA,EAAU,WAAWgL,CAAU,EAAIC,EAAuBjL,EAAU,UAAUgL,EAAW,MAAM,CAAC,EAAI,CAChI,WAAY,GACZ,UAAW,CAAE,EACb,qBAAsB,GACtB,cAAehL,EACf,6BAA8B,MACpC,CACG,CACD,GAAImK,EAA4B,CAC9B,MAAMc,EAAyBb,EAC/BA,EAAiBpK,GAAamK,EAA2B,CACvD,UAAAnK,EACA,eAAgBiL,CACtB,CAAK,CACF,CACD,OAAOb,CACT,EACMS,GAAyBD,GACzBA,EAAc,SAASd,EAAkB,EACpCc,EAAc,UAAU,EAAGA,EAAc,OAAS,CAAC,EAMxDA,EAAc,WAAWd,EAAkB,EACtCc,EAAc,UAAU,CAAC,EAE3BA,EAQHM,GAAsBvG,GAAU,CACpC,MAAMwG,EAA0B,OAAO,YAAYxG,EAAO,wBAAwB,IAAIyG,GAAY,CAACA,EAAU,EAAI,CAAC,CAAC,EAmBnH,OAlBsBf,GAAa,CACjC,GAAIA,EAAU,QAAU,EACtB,OAAOA,EAET,MAAMgB,EAAkB,CAAA,EACxB,IAAIC,EAAoB,CAAA,EACxB,OAAAjB,EAAU,QAAQe,GAAY,CACAA,EAAS,CAAC,IAAM,KAAOD,EAAwBC,CAAQ,GAEjFC,EAAgB,KAAK,GAAGC,EAAkB,KAAM,EAAEF,CAAQ,EAC1DE,EAAoB,CAAA,GAEpBA,EAAkB,KAAKF,CAAQ,CAEvC,CAAK,EACDC,EAAgB,KAAK,GAAGC,EAAkB,KAAM,CAAA,EACzCD,CACX,CAEA,EACME,GAAoB5G,IAAW,CACnC,MAAO4E,GAAe5E,EAAO,SAAS,EACtC,eAAgBsF,GAAqBtF,CAAM,EAC3C,cAAeuG,GAAoBvG,CAAM,EACzC,GAAG2C,GAAsB3C,CAAM,CACjC,GACM6G,GAAsB,MACtBC,GAAiB,CAACC,EAAWC,IAAgB,CACjD,KAAM,CACJ,eAAAvB,EACA,gBAAAwB,EACA,4BAAAC,EACA,cAAAC,CACD,EAAGH,EAQEI,EAAwB,CAAA,EACxBC,EAAaN,EAAU,KAAM,EAAC,MAAMF,EAAmB,EAC7D,IAAIpH,EAAS,GACb,QAASnE,EAAQ+L,EAAW,OAAS,EAAG/L,GAAS,EAAGA,GAAS,EAAG,CAC9D,MAAMgM,EAAoBD,EAAW/L,CAAK,EACpC,CACJ,WAAAiM,EACA,UAAA7B,EACA,qBAAAS,EACA,cAAAF,EACA,6BAAAG,CACN,EAAQX,EAAe6B,CAAiB,EACpC,GAAIC,EAAY,CACd9H,EAAS6H,GAAqB7H,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACD,CACD,IAAI2D,EAAqB,CAAC,CAACgD,EACvBjD,EAAe8D,EAAgB7D,EAAqB6C,EAAc,UAAU,EAAGG,CAA4B,EAAIH,CAAa,EAChI,GAAI,CAAC9C,EAAc,CACjB,GAAI,CAACC,EAAoB,CAEvB3D,EAAS6H,GAAqB7H,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACD,CAED,GADA0D,EAAe8D,EAAgBhB,CAAa,EACxC,CAAC9C,EAAc,CAEjB1D,EAAS6H,GAAqB7H,EAAO,OAAS,EAAI,IAAMA,EAASA,GACjE,QACD,CACD2D,EAAqB,EACtB,CACD,MAAMoE,EAAkBL,EAAczB,CAAS,EAAE,KAAK,GAAG,EACnD+B,EAAatB,EAAuBqB,EAAkBrC,GAAqBqC,EAC3EE,EAAUD,EAAatE,EAC7B,GAAIiE,EAAsB,SAASM,CAAO,EAExC,SAEFN,EAAsB,KAAKM,CAAO,EAClC,MAAMC,EAAiBT,EAA4B/D,EAAcC,CAAkB,EACnF,QAASwE,EAAI,EAAGA,EAAID,EAAe,OAAQ,EAAEC,EAAG,CAC9C,MAAMC,EAAQF,EAAeC,CAAC,EAC9BR,EAAsB,KAAKK,EAAaI,CAAK,CAC9C,CAEDpI,EAAS6H,GAAqB7H,EAAO,OAAS,EAAI,IAAMA,EAASA,EAClE,CACD,OAAOA,CACT,EAWA,SAASqI,IAAS,CAChB,IAAIxM,EAAQ,EACRyM,EACAC,EACApN,EAAS,GACb,KAAOU,EAAQ,UAAU,SACnByM,EAAW,UAAUzM,GAAO,KAC1B0M,EAAgBC,GAAQF,CAAQ,KAClCnN,IAAWA,GAAU,KACrBA,GAAUoN,GAIhB,OAAOpN,CACT,CACA,MAAMqN,GAAUC,GAAO,CACrB,GAAI,OAAOA,GAAQ,SACjB,OAAOA,EAET,IAAIF,EACApN,EAAS,GACb,QAASuN,EAAI,EAAGA,EAAID,EAAI,OAAQC,IAC1BD,EAAIC,CAAC,IACHH,EAAgBC,GAAQC,EAAIC,CAAC,CAAC,KAChCvN,IAAWA,GAAU,KACrBA,GAAUoN,GAIhB,OAAOpN,CACT,EACA,SAASwN,GAAoBC,KAAsBC,EAAkB,CACnE,IAAItB,EACAuB,EACAC,EACAC,EAAiBC,EACrB,SAASA,EAAkB3B,EAAW,CACpC,MAAM/G,EAASsI,EAAiB,OAAO,CAACK,EAAgBC,IAAwBA,EAAoBD,CAAc,EAAGN,EAAiB,CAAE,EACxI,OAAArB,EAAcJ,GAAkB5G,CAAM,EACtCuI,EAAWvB,EAAY,MAAM,IAC7BwB,EAAWxB,EAAY,MAAM,IAC7ByB,EAAiBI,EACVA,EAAc9B,CAAS,CAC/B,CACD,SAAS8B,EAAc9B,EAAW,CAChC,MAAM+B,EAAeP,EAASxB,CAAS,EACvC,GAAI+B,EACF,OAAOA,EAET,MAAMrJ,EAASqH,GAAeC,EAAWC,CAAW,EACpD,OAAAwB,EAASzB,EAAWtH,CAAM,EACnBA,CACR,CACD,OAAO,UAA6B,CAClC,OAAOgJ,EAAeX,GAAO,MAAM,KAAM,SAAS,CAAC,CACvD,CACA,CACA,MAAMiB,EAAYxE,GAAO,CACvB,MAAMyE,EAAcjF,GAASA,EAAMQ,CAAG,GAAK,CAAA,EAC3C,OAAAyE,EAAY,cAAgB,GACrBA,CACT,EACMC,GAAsB,8BACtBC,GAAyB,8BACzBC,GAAgB,aAChBC,GAAkB,mCAClBC,GAAkB,4HAClBC,GAAqB,qDAErBC,GAAc,kEACdC,GAAa,+FACbC,GAAavE,GAASiE,GAAc,KAAKjE,CAAK,EAC9CwE,EAAWxE,GAAS,CAAC,CAACA,GAAS,CAAC,OAAO,MAAM,OAAOA,CAAK,CAAC,EAC1DyE,EAAYzE,GAAS,CAAC,CAACA,GAAS,OAAO,UAAU,OAAOA,CAAK,CAAC,EAC9D0E,GAAY1E,GAASA,EAAM,SAAS,GAAG,GAAKwE,EAASxE,EAAM,MAAM,EAAG,EAAE,CAAC,EACvE2E,EAAe3E,GAASkE,GAAgB,KAAKlE,CAAK,EAClD4E,GAAQ,IAAM,GACdC,GAAe7E,GAIrBmE,GAAgB,KAAKnE,CAAK,GAAK,CAACoE,GAAmB,KAAKpE,CAAK,EACvD8E,GAAU,IAAM,GAChBC,GAAW/E,GAASqE,GAAY,KAAKrE,CAAK,EAC1CgF,GAAUhF,GAASsE,GAAW,KAAKtE,CAAK,EACxCiF,GAAoBjF,GAAS,CAACkF,EAAiBlF,CAAK,GAAK,CAACmF,EAAoBnF,CAAK,EACnFoF,GAAkBpF,GAASqF,GAAoBrF,EAAOsF,GAAaR,EAAO,EAC1EI,EAAmBlF,GAAS+D,GAAoB,KAAK/D,CAAK,EAC1DuF,EAAoBvF,GAASqF,GAAoBrF,EAAOwF,GAAeX,EAAY,EACnFY,GAAoBzF,GAASqF,GAAoBrF,EAAO0F,GAAelB,CAAQ,EAC/EmB,GAAsB3F,GAASqF,GAAoBrF,EAAO4F,GAAiBd,EAAO,EAClFe,GAAmB7F,GAASqF,GAAoBrF,EAAO8F,GAAcd,EAAO,EAC5Ee,GAAoB/F,GAASqF,GAAoBrF,EAAOgG,GAAejB,EAAQ,EAC/EI,EAAsBnF,GAASgE,GAAuB,KAAKhE,CAAK,EAChEiG,GAA4BjG,GAASkG,GAAuBlG,EAAOwF,EAAa,EAChFW,GAAgCnG,GAASkG,GAAuBlG,EAAOoG,EAAiB,EACxFC,GAA8BrG,GAASkG,GAAuBlG,EAAO4F,EAAe,EACpFU,GAA0BtG,GAASkG,GAAuBlG,EAAOsF,EAAW,EAC5EiB,GAA2BvG,GAASkG,GAAuBlG,EAAO8F,EAAY,EAC9EU,GAA4BxG,GAASkG,GAAuBlG,EAAOgG,GAAe,EAAI,EAEtFX,GAAsB,CAACrF,EAAOyG,EAAWC,IAAc,CAC3D,MAAMnM,EAASwJ,GAAoB,KAAK/D,CAAK,EAC7C,OAAIzF,EACEA,EAAO,CAAC,EACHkM,EAAUlM,EAAO,CAAC,CAAC,EAErBmM,EAAUnM,EAAO,CAAC,CAAC,EAErB,EACT,EACM2L,GAAyB,CAAClG,EAAOyG,EAAWE,EAAqB,KAAU,CAC/E,MAAMpM,EAASyJ,GAAuB,KAAKhE,CAAK,EAChD,OAAIzF,EACEA,EAAO,CAAC,EACHkM,EAAUlM,EAAO,CAAC,CAAC,EAErBoM,EAEF,EACT,EAEMf,GAAkBgB,GAASA,IAAU,YAAcA,IAAU,aAC7Dd,GAAec,GAASA,IAAU,SAAWA,IAAU,MACvDtB,GAAcsB,GAASA,IAAU,UAAYA,IAAU,QAAUA,IAAU,UAC3EpB,GAAgBoB,GAASA,IAAU,SACnClB,GAAgBkB,GAASA,IAAU,SACnCR,GAAoBQ,GAASA,IAAU,cACvCZ,GAAgBY,GAASA,IAAU,SA2BnCC,GAAmB,IAAM,CAM7B,MAAMC,EAAajD,EAAU,OAAO,EAC9BkD,EAAYlD,EAAU,MAAM,EAC5BmD,EAAYnD,EAAU,MAAM,EAC5BoD,EAAkBpD,EAAU,aAAa,EACzCqD,EAAgBrD,EAAU,UAAU,EACpCsD,EAAetD,EAAU,SAAS,EAClCuD,EAAkBvD,EAAU,YAAY,EACxCwD,EAAiBxD,EAAU,WAAW,EACtCyD,EAAezD,EAAU,SAAS,EAClC0D,EAAc1D,EAAU,QAAQ,EAChC2D,EAAc3D,EAAU,QAAQ,EAChC4D,EAAmB5D,EAAU,cAAc,EAC3C6D,EAAkB7D,EAAU,aAAa,EACzC8D,EAAkB9D,EAAU,aAAa,EACzC+D,EAAY/D,EAAU,MAAM,EAC5BgE,EAAmBhE,EAAU,aAAa,EAC1CiE,EAAcjE,EAAU,QAAQ,EAChCkE,EAAYlE,EAAU,MAAM,EAC5BmE,EAAenE,EAAU,SAAS,EAQlCoE,EAAa,IAAM,CAAC,OAAQ,QAAS,MAAO,aAAc,OAAQ,OAAQ,QAAS,QAAQ,EAC3FC,EAAgB,IAAM,CAAC,SAAU,MAAO,SAAU,OAAQ,QAAS,WAEzE,WAAY,YAEZ,YAAa,eAEb,eAAgB,cAEhB,aAAa,EACPC,EAA6B,IAAM,CAAC,GAAGD,EAAa,EAAI/C,EAAqBD,CAAgB,EAC7FkD,EAAgB,IAAM,CAAC,OAAQ,SAAU,OAAQ,UAAW,QAAQ,EACpEC,EAAkB,IAAM,CAAC,OAAQ,UAAW,MAAM,EAClDC,EAA0B,IAAM,CAACnD,EAAqBD,EAAkBoC,CAAY,EACpFiB,EAAa,IAAM,CAAChE,GAAY,OAAQ,OAAQ,GAAG+D,EAAuB,CAAE,EAC5EE,GAA4B,IAAM,CAAC/D,EAAW,OAAQ,UAAWU,EAAqBD,CAAgB,EACtGuD,GAA6B,IAAM,CAAC,OAAQ,CAChD,KAAM,CAAC,OAAQhE,EAAWU,EAAqBD,CAAgB,CACnE,EAAKT,EAAWU,EAAqBD,CAAgB,EAC7CwD,GAA4B,IAAM,CAACjE,EAAW,OAAQU,EAAqBD,CAAgB,EAC3FyD,GAAwB,IAAM,CAAC,OAAQ,MAAO,MAAO,KAAMxD,EAAqBD,CAAgB,EAChG0D,GAAwB,IAAM,CAAC,QAAS,MAAO,SAAU,UAAW,SAAU,SAAU,UAAW,WAAY,cAAe,UAAU,EACxIC,GAA0B,IAAM,CAAC,QAAS,MAAO,SAAU,UAAW,cAAe,UAAU,EAC/FC,EAAc,IAAM,CAAC,OAAQ,GAAGR,EAAyB,CAAA,EACzDS,EAAc,IAAM,CAACxE,GAAY,OAAQ,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,GAAG+D,EAAuB,CAAE,EAC5IU,EAAa,IAAM,CAAClC,EAAY3B,EAAqBD,CAAgB,EACrE+D,GAAkB,IAAM,CAAC,GAAGf,EAAa,EAAI7B,GAA6BV,GAAqB,CACnG,SAAU,CAACR,EAAqBD,CAAgB,CACpD,CAAG,EACKgE,GAAgB,IAAM,CAAC,YAAa,CACxC,OAAQ,CAAC,GAAI,IAAK,IAAK,QAAS,OAAO,CAC3C,CAAG,EACKC,GAAc,IAAM,CAAC,OAAQ,QAAS,UAAW7C,GAAyBlB,GAAiB,CAC/F,KAAM,CAACD,EAAqBD,CAAgB,CAChD,CAAG,EACKkE,GAA4B,IAAM,CAAC1E,GAAWuB,GAA2BV,CAAiB,EAC1F8D,EAAc,IAAM,CAE1B,GAAI,OAAQ,OAAQ9B,EAAapC,EAAqBD,CAAgB,EAChEoE,EAAmB,IAAM,CAAC,GAAI9E,EAAUyB,GAA2BV,CAAiB,EACpFgE,GAAiB,IAAM,CAAC,QAAS,SAAU,SAAU,QAAQ,EAC7DC,GAAiB,IAAM,CAAC,SAAU,WAAY,SAAU,UAAW,SAAU,UAAW,cAAe,aAAc,aAAc,aAAc,aAAc,YAAa,MAAO,aAAc,QAAS,YAAY,EACtNC,EAAyB,IAAM,CAACjF,EAAUE,GAAW2B,GAA6BV,EAAmB,EACrG+D,GAAY,IAAM,CAExB,GAAI,OAAQ9B,EAAWzC,EAAqBD,CAAgB,EACtDyE,GAAc,IAAM,CAAC,OAAQnF,EAAUW,EAAqBD,CAAgB,EAC5E0E,GAAa,IAAM,CAAC,OAAQpF,EAAUW,EAAqBD,CAAgB,EAC3E2E,GAAY,IAAM,CAACrF,EAAUW,EAAqBD,CAAgB,EAClE4E,GAAiB,IAAM,CAACvF,GAAY,OAAQ,GAAG+D,EAAuB,CAAE,EAC9E,MAAO,CACL,UAAW,IACX,MAAO,CACL,QAAS,CAAC,OAAQ,OAAQ,QAAS,QAAQ,EAC3C,OAAQ,CAAC,OAAO,EAChB,KAAM,CAAC3D,CAAY,EACnB,WAAY,CAACA,CAAY,EACzB,MAAO,CAACC,EAAK,EACb,UAAW,CAACD,CAAY,EACxB,cAAe,CAACA,CAAY,EAC5B,KAAM,CAAC,KAAM,MAAO,QAAQ,EAC5B,KAAM,CAACM,EAAiB,EACxB,cAAe,CAAC,OAAQ,aAAc,QAAS,SAAU,SAAU,WAAY,OAAQ,YAAa,OAAO,EAC3G,eAAgB,CAACN,CAAY,EAC7B,QAAS,CAAC,OAAQ,QAAS,OAAQ,SAAU,UAAW,OAAO,EAC/D,YAAa,CAAC,WAAY,OAAQ,SAAU,WAAY,UAAW,MAAM,EACzE,OAAQ,CAACA,CAAY,EACrB,OAAQ,CAACA,CAAY,EACrB,QAAS,CAAC,KAAMH,CAAQ,EACxB,KAAM,CAACG,CAAY,EACnB,cAAe,CAACA,CAAY,EAC5B,SAAU,CAAC,UAAW,QAAS,SAAU,OAAQ,QAAS,QAAQ,CACnE,EACD,YAAa,CAQX,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,SAAUJ,GAAYW,EAAkBC,EAAqB2C,CAAW,CACjG,CAAO,EAMD,UAAW,CAAC,WAAW,EAKvB,QAAS,CAAC,CACR,QAAS,CAACtD,EAAUU,EAAkBC,EAAqBkC,CAAc,CACjF,CAAO,EAKD,cAAe,CAAC,CACd,cAAeY,EAAY,CACnC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAY,CACpC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,QAAS,aAAc,cAAc,CACtE,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,QAAS,OAAO,CAC3C,CAAO,EAKD,IAAK,CAAC,CACJ,IAAK,CAAC,SAAU,SAAS,CACjC,CAAO,EAKD,QAAS,CAAC,QAAS,eAAgB,SAAU,OAAQ,cAAe,QAAS,eAAgB,gBAAiB,aAAc,eAAgB,qBAAsB,qBAAsB,qBAAsB,kBAAmB,YAAa,YAAa,OAAQ,cAAe,WAAY,YAAa,QAAQ,EAKnT,GAAI,CAAC,UAAW,aAAa,EAK7B,MAAO,CAAC,CACN,MAAO,CAAC,QAAS,OAAQ,OAAQ,QAAS,KAAK,CACvD,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,OAAQ,QAAS,OAAQ,OAAQ,QAAS,KAAK,CAC/D,CAAO,EAKD,UAAW,CAAC,UAAW,gBAAgB,EAKvC,aAAc,CAAC,CACb,OAAQ,CAAC,UAAW,QAAS,OAAQ,OAAQ,YAAY,CACjE,CAAO,EAKD,kBAAmB,CAAC,CAClB,OAAQE,EAA4B,CAC5C,CAAO,EAKD,SAAU,CAAC,CACT,SAAUC,EAAe,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAe,CACrC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAe,CACrC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYC,EAAiB,CACrC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAiB,CACzC,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgBA,EAAiB,CACzC,CAAO,EAKD,SAAU,CAAC,SAAU,QAAS,WAAY,WAAY,QAAQ,EAK9D,MAAO,CAAC,CACN,MAAOE,EAAY,CAC3B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,EAAY,CAC/B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,EAAY,CAC/B,CAAO,EAKD,MAAO,CAAC,CACN,MAAOA,EAAY,CAC3B,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKA,EAAY,CACzB,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKA,EAAY,CACzB,CAAO,EAKD,MAAO,CAAC,CACN,MAAOA,EAAY,CAC3B,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQA,EAAY,CAC5B,CAAO,EAKD,KAAM,CAAC,CACL,KAAMA,EAAY,CAC1B,CAAO,EAKD,WAAY,CAAC,UAAW,YAAa,UAAU,EAK/C,EAAG,CAAC,CACF,EAAG,CAAC9D,EAAW,OAAQU,EAAqBD,CAAgB,CACpE,CAAO,EAQD,MAAO,CAAC,CACN,MAAO,CAACX,GAAY,OAAQ,OAAQ8C,EAAgB,GAAGiB,GAAyB,CACxF,CAAO,EAKD,iBAAkB,CAAC,CACjB,KAAM,CAAC,MAAO,cAAe,MAAO,aAAa,CACzD,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,SAAU,OAAQ,cAAc,CAC/C,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC9D,EAAUD,GAAY,OAAQ,UAAW,OAAQW,CAAgB,CAChF,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAACT,EAAW,QAAS,OAAQ,OAAQU,EAAqBD,CAAgB,CACzF,CAAO,EAKD,YAAa,CAAC,CACZ,YAAasD,GAA2B,CAChD,CAAO,EAKD,gBAAiB,CAAC,CAChB,IAAKC,GAA4B,CACzC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,GAA2B,CAChD,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAA2B,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaF,GAA2B,CAChD,CAAO,EAKD,gBAAiB,CAAC,CAChB,IAAKC,GAA4B,CACzC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,GAA2B,CAChD,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAA2B,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,MAAO,MAAO,QAAS,YAAa,WAAW,CACrE,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaC,GAAuB,CAC5C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,GAAuB,CAC5C,CAAO,EAKD,IAAK,CAAC,CACJ,IAAKL,EAAyB,CACtC,CAAO,EAKD,QAAS,CAAC,CACR,QAASA,EAAyB,CAC1C,CAAO,EAKD,QAAS,CAAC,CACR,QAASA,EAAyB,CAC1C,CAAO,EAKD,kBAAmB,CAAC,CAClB,QAAS,CAAC,GAAGM,GAAuB,EAAE,QAAQ,CACtD,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiB,CAAC,GAAGC,GAAyB,EAAE,QAAQ,CAChE,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQ,GAAGA,IAAyB,CAC7D,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAAS,CAAC,SAAU,GAAGD,IAAuB,CACtD,CAAO,EAKD,cAAe,CAAC,CACd,MAAO,CAAC,GAAGC,KAA2B,CACpC,SAAU,CAAC,GAAI,MAAM,CAC/B,CAAS,CACT,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,OAAQ,GAAGA,KAA2B,CAC3C,SAAU,CAAC,GAAI,MAAM,CAC/B,CAAS,CACT,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiBD,GAAuB,CAChD,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,GAAGC,GAAyB,EAAE,UAAU,CAChE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAAC,OAAQ,GAAGA,IAAyB,CAC3D,CAAO,EAMD,EAAG,CAAC,CACF,EAAGP,EAAyB,CACpC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAyB,CACrC,CAAO,EAKD,EAAG,CAAC,CACF,EAAGQ,EAAa,CACxB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,GAAI,CAAC,CACH,GAAIA,EAAa,CACzB,CAAO,EAKD,UAAW,CAAC,CACV,UAAWR,EAAyB,CAC5C,CAAO,EAKD,kBAAmB,CAAC,iBAAiB,EAKrC,UAAW,CAAC,CACV,UAAWA,EAAyB,CAC5C,CAAO,EAKD,kBAAmB,CAAC,iBAAiB,EAQrC,KAAM,CAAC,CACL,KAAMS,EAAa,CAC3B,CAAO,EAKD,EAAG,CAAC,CACF,EAAG,CAAC1B,EAAgB,SAAU,GAAG0B,EAAW,CAAE,CACtD,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC1B,EAAgB,SAC1B,OAAQ,GAAG0B,GAAa,CAChC,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC1B,EAAgB,SAAU,OACpC,QACA,CACE,OAAQ,CAACD,CAAe,CAClC,EAAW,GAAG2B,EAAW,CAAE,CAC3B,CAAO,EAKD,EAAG,CAAC,CACF,EAAG,CAAC,SAAU,KAAM,GAAGA,EAAW,CAAE,CAC5C,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,SAAU,KAAM,OAAQ,GAAGA,EAAW,CAAE,CAC1D,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,SAAU,KAAM,GAAGA,EAAW,CAAE,CAClD,CAAO,EAQD,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ/B,EAAWf,GAA2BV,CAAiB,CAC9E,CAAO,EAKD,iBAAkB,CAAC,cAAe,sBAAsB,EAKxD,aAAc,CAAC,SAAU,YAAY,EAKrC,cAAe,CAAC,CACd,KAAM,CAAC0B,EAAiB9B,EAAqBM,EAAiB,CACtE,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,kBAAmB,kBAAmB,YAAa,iBAAkB,SAAU,gBAAiB,WAAY,iBAAkB,iBAAkBf,GAAWQ,CAAgB,CACpM,CAAO,EAKD,cAAe,CAAC,CACd,KAAM,CAACiB,GAA+BjB,EAAkB6B,CAAS,CACzE,CAAO,EAKD,aAAc,CAAC,aAAa,EAK5B,cAAe,CAAC,SAAS,EAKzB,mBAAoB,CAAC,cAAc,EAKnC,aAAc,CAAC,cAAe,eAAe,EAK7C,cAAe,CAAC,oBAAqB,cAAc,EAKnD,eAAgB,CAAC,qBAAsB,mBAAmB,EAK1D,SAAU,CAAC,CACT,SAAU,CAACG,EAAe/B,EAAqBD,CAAgB,CACvE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAACV,EAAU,OAAQW,EAAqBM,EAAiB,CAC/E,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CACT0B,EAAc,GAAGmB,GAAyB,CAClD,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAAC,OAAQnD,EAAqBD,CAAgB,CACpE,CAAO,EAKD,sBAAuB,CAAC,CACtB,KAAM,CAAC,SAAU,SAAS,CAClC,CAAO,EAKD,kBAAmB,CAAC,CAClB,KAAM,CAAC,OAAQ,UAAW,OAAQC,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,iBAAkB,CAAC,CACjB,KAAM,CAAC,OAAQ,SAAU,QAAS,UAAW,QAAS,KAAK,CACnE,CAAO,EAMD,oBAAqB,CAAC,CACpB,YAAa8D,EAAY,CACjC,CAAO,EAKD,aAAc,CAAC,CACb,KAAMA,EAAY,CAC1B,CAAO,EAKD,kBAAmB,CAAC,YAAa,WAAY,eAAgB,cAAc,EAK3E,wBAAyB,CAAC,CACxB,WAAY,CAAC,GAAGO,GAAgB,EAAE,MAAM,CAChD,CAAO,EAKD,4BAA6B,CAAC,CAC5B,WAAY,CAAC/E,EAAU,YAAa,OAAQW,EAAqBI,CAAiB,CAC1F,CAAO,EAKD,wBAAyB,CAAC,CACxB,WAAYyD,EAAY,CAChC,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoB,CAACxE,EAAU,OAAQW,EAAqBD,CAAgB,CACpF,CAAO,EAKD,iBAAkB,CAAC,YAAa,YAAa,aAAc,aAAa,EAKxE,gBAAiB,CAAC,WAAY,gBAAiB,WAAW,EAK1D,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ,SAAU,UAAW,QAAQ,CACpD,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQoD,EAAyB,CACzC,CAAO,EAKD,iBAAkB,CAAC,CACjB,MAAO,CAAC,WAAY,MAAO,SAAU,SAAU,WAAY,cAAe,MAAO,QAASnD,EAAqBD,CAAgB,CACvI,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAC,SAAU,SAAU,MAAO,WAAY,WAAY,cAAc,CACtF,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,SAAU,QAAS,MAAO,MAAM,CAChD,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,aAAc,WAAY,QAAQ,CACjD,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQ,SAAU,MAAM,CAC1C,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQC,EAAqBD,CAAgB,CAC/D,CAAO,EAQD,gBAAiB,CAAC,CAChB,GAAI,CAAC,QAAS,QAAS,QAAQ,CACvC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC,SAAU,UAAW,UAAW,MAAM,CAC1D,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,SAAU,UAAW,SAAS,CACpD,CAAO,EAKD,cAAe,CAAC,CACd,GAAI+D,GAAiB,CAC7B,CAAO,EAKD,YAAa,CAAC,CACZ,GAAIC,GAAe,CAC3B,CAAO,EAKD,UAAW,CAAC,CACV,GAAIC,GAAa,CACzB,CAAO,EAKD,WAAY,CAAC,CACX,GAAI,CAAC,OAAQ,CACX,OAAQ,CAAC,CACP,GAAI,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,IAAI,CAC3D,EAAa1E,EAAWU,EAAqBD,CAAgB,EACnD,OAAQ,CAAC,GAAIC,EAAqBD,CAAgB,EAClD,MAAO,CAACT,EAAWU,EAAqBD,CAAgB,CAClE,EAAWqB,GAA0BV,EAAgB,CACrD,CAAO,EAKD,WAAY,CAAC,CACX,GAAImD,EAAY,CACxB,CAAO,EAKD,oBAAqB,CAAC,CACpB,KAAMI,GAA2B,CACzC,CAAO,EAKD,mBAAoB,CAAC,CACnB,IAAKA,GAA2B,CACxC,CAAO,EAKD,kBAAmB,CAAC,CAClB,GAAIA,GAA2B,CACvC,CAAO,EAKD,gBAAiB,CAAC,CAChB,KAAMJ,EAAY,CAC1B,CAAO,EAKD,eAAgB,CAAC,CACf,IAAKA,EAAY,CACzB,CAAO,EAKD,cAAe,CAAC,CACd,GAAIA,EAAY,CACxB,CAAO,EAQD,QAAS,CAAC,CACR,QAASK,EAAa,CAC9B,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAa,CAClC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAa,CAClC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAa,CAClC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAa,CAClC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAa,CAClC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAa,CAClC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,aAAc,CAAC,CACb,aAAcA,EAAa,CACnC,CAAO,EAKD,WAAY,CAAC,CACX,OAAQC,EAAkB,CAClC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,aAAc,CAAC,CACb,WAAYA,EAAkB,CACtC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,EAAkB,CACtC,CAAO,EAKD,mBAAoB,CAAC,kBAAkB,EAKvC,WAAY,CAAC,CACX,WAAYA,EAAkB,CACtC,CAAO,EAKD,mBAAoB,CAAC,kBAAkB,EAKvC,eAAgB,CAAC,CACf,OAAQ,CAAC,GAAGC,KAAkB,SAAU,MAAM,CACtD,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQ,CAAC,GAAGA,KAAkB,SAAU,MAAM,CACtD,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQP,EAAY,CAC5B,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,iBAAkB,CAAC,CACjB,WAAYA,EAAY,CAChC,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQA,EAAY,CAC5B,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAAS,CAAC,GAAGO,KAAkB,OAAQ,QAAQ,CACvD,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC/E,EAAUW,EAAqBD,CAAgB,CAC1E,CAAO,EAKD,YAAa,CAAC,CACZ,QAAS,CAAC,GAAIV,EAAUyB,GAA2BV,CAAiB,CAC5E,CAAO,EAKD,gBAAiB,CAAC,CAChB,QAASyD,EAAY,CAC7B,CAAO,EAQD,OAAQ,CAAC,CACP,OAAQ,CAER,GAAI,OAAQxB,EAAahB,GAA2BT,EAAiB,CAC7E,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQiD,EAAY,CAC5B,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,OAAQvB,EAAkBjB,GAA2BT,EAAiB,CAC/F,CAAO,EAKD,qBAAsB,CAAC,CACrB,eAAgBiD,EAAY,CACpC,CAAO,EAKD,SAAU,CAAC,CACT,KAAMM,EAAkB,CAChC,CAAO,EAOD,eAAgB,CAAC,YAAY,EAK7B,aAAc,CAAC,CACb,KAAMN,EAAY,CAC1B,CAAO,EAOD,gBAAiB,CAAC,CAChB,cAAe,CAACxE,EAAUe,CAAiB,CACnD,CAAO,EAOD,oBAAqB,CAAC,CACpB,cAAeyD,EAAY,CACnC,CAAO,EAKD,eAAgB,CAAC,CACf,aAAcM,EAAkB,CACxC,CAAO,EAKD,mBAAoB,CAAC,CACnB,aAAcN,EAAY,CAClC,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,OAAQtB,EAAiBlB,GAA2BT,EAAiB,CAC7F,CAAO,EAKD,oBAAqB,CAAC,CACpB,cAAeiD,EAAY,CACnC,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAACxE,EAAUW,EAAqBD,CAAgB,CACjE,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,GAAGsE,KAAkB,cAAe,cAAc,CACxE,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAgB,CACpC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,SAAU,UAAW,UAAW,OAAQ,SAAU,MAAM,CACvE,EAAE,cAAc,EAKjB,iBAAkB,CAAC,CACjB,KAAM,CAAC,MAAO,WAAY,YAAa,SAAS,CACxD,CAAO,EAKD,wBAAyB,CAAC,CACxB,cAAe,CAAChF,CAAQ,CAChC,CAAO,EACD,6BAA8B,CAAC,CAC7B,mBAAoBiF,EAAwB,CACpD,CAAO,EACD,2BAA4B,CAAC,CAC3B,iBAAkBA,EAAwB,CAClD,CAAO,EACD,+BAAgC,CAAC,CAC/B,mBAAoBT,EAAY,CACxC,CAAO,EACD,6BAA8B,CAAC,CAC7B,iBAAkBA,EAAY,CACtC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAwB,CAC/C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAwB,CAC7C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAY,CACnC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAY,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAwB,CAC/C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAwB,CAC7C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAY,CACnC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAY,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAwB,CAC/C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAwB,CAC7C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAY,CACnC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAY,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAwB,CAC/C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAwB,CAC7C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAY,CACnC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAY,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAwB,CAC/C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAwB,CAC7C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAY,CACnC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAY,CACjC,CAAO,EACD,wBAAyB,CAAC,CACxB,cAAeS,EAAwB,CAC/C,CAAO,EACD,sBAAuB,CAAC,CACtB,YAAaA,EAAwB,CAC7C,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAeT,EAAY,CACnC,CAAO,EACD,wBAAyB,CAAC,CACxB,YAAaA,EAAY,CACjC,CAAO,EACD,oBAAqB,CAAC,CACpB,cAAe,CAAC7D,EAAqBD,CAAgB,CAC7D,CAAO,EACD,6BAA8B,CAAC,CAC7B,mBAAoBuE,EAAwB,CACpD,CAAO,EACD,2BAA4B,CAAC,CAC3B,iBAAkBA,EAAwB,CAClD,CAAO,EACD,+BAAgC,CAAC,CAC/B,mBAAoBT,EAAY,CACxC,CAAO,EACD,6BAA8B,CAAC,CAC7B,iBAAkBA,EAAY,CACtC,CAAO,EACD,0BAA2B,CAAC,CAC1B,cAAe,CAAC,SAAU,SAAS,CAC3C,CAAO,EACD,yBAA0B,CAAC,CACzB,cAAe,CAAC,CACd,QAAS,CAAC,OAAQ,QAAQ,EAC1B,SAAU,CAAC,OAAQ,QAAQ,CACrC,CAAS,CACT,CAAO,EACD,wBAAyB,CAAC,CACxB,iBAAkBd,EAAe,CACzC,CAAO,EACD,uBAAwB,CAAC,CACvB,aAAc,CAAC1D,CAAQ,CAC/B,CAAO,EACD,4BAA6B,CAAC,CAC5B,kBAAmBiF,EAAwB,CACnD,CAAO,EACD,0BAA2B,CAAC,CAC1B,gBAAiBA,EAAwB,CACjD,CAAO,EACD,8BAA+B,CAAC,CAC9B,kBAAmBT,EAAY,CACvC,CAAO,EACD,4BAA6B,CAAC,CAC5B,gBAAiBA,EAAY,CACrC,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,QAAS,YAAa,OAAO,CAC5C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,SAAU,UAAW,UAAW,OAAQ,SAAU,MAAM,CAChF,CAAO,EAKD,gBAAiB,CAAC,CAChB,KAAMC,GAAiB,CAC/B,CAAO,EAKD,cAAe,CAAC,CACd,KAAMC,GAAe,CAC7B,CAAO,EAKD,YAAa,CAAC,CACZ,KAAMC,GAAa,CAC3B,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC,QAAS,WAAW,CAC1C,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,OAAQhE,EAAqBD,CAAgB,CAC5D,CAAO,EAQD,OAAQ,CAAC,CACP,OAAQ,CAER,GAAI,OAAQC,EAAqBD,CAAgB,CACzD,CAAO,EAKD,KAAM,CAAC,CACL,KAAMwE,GAAW,CACzB,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAClF,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAEf,GAAI,OAAQyC,EAAiBnB,GAA2BT,EAAiB,CACjF,CAAO,EAKD,oBAAqB,CAAC,CACpB,cAAeiD,EAAY,CACnC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC,GAAIxE,EAAUW,EAAqBD,CAAgB,CACvE,CAAO,EAKD,aAAc,CAAC,CACb,aAAc,CAACV,EAAUW,EAAqBD,CAAgB,CACtE,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACpE,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAUW,EAAqBD,CAAgB,CAClE,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CACnE,CAAO,EAKD,kBAAmB,CAAC,CAClB,kBAAmB,CAEnB,GAAI,OAAQC,EAAqBD,CAAgB,CACzD,CAAO,EAKD,gBAAiB,CAAC,CAChB,gBAAiBwE,GAAW,CACpC,CAAO,EAKD,sBAAuB,CAAC,CACtB,sBAAuB,CAAClF,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,oBAAqB,CAAC,CACpB,oBAAqB,CAACV,EAAUW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,qBAAsB,CAAC,CACrB,qBAAsB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAClF,CAAO,EAKD,sBAAuB,CAAC,CACtB,sBAAuB,CAACV,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,kBAAmB,CAAC,CAClB,kBAAmB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAC/E,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoB,CAACV,EAAUW,EAAqBD,CAAgB,CAC5E,CAAO,EAKD,oBAAqB,CAAC,CACpB,oBAAqB,CAACV,EAAUW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,GAAIV,EAAUW,EAAqBD,CAAgB,CAC9E,CAAO,EAQD,kBAAmB,CAAC,CAClB,OAAQ,CAAC,WAAY,UAAU,CACvC,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkBoD,EAAyB,CACnD,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoBA,EAAyB,CACrD,CAAO,EAKD,mBAAoB,CAAC,CACnB,mBAAoBA,EAAyB,CACrD,CAAO,EAKD,eAAgB,CAAC,CACf,MAAO,CAAC,OAAQ,OAAO,CAC/B,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,MAAO,QAAQ,CACjC,CAAO,EAQD,WAAY,CAAC,CACX,WAAY,CAAC,GAAI,MAAO,SAAU,UAAW,SAAU,YAAa,OAAQnD,EAAqBD,CAAgB,CACzH,CAAO,EAKD,sBAAuB,CAAC,CACtB,WAAY,CAAC,SAAU,UAAU,CACzC,CAAO,EAKD,SAAU,CAAC,CACT,SAAU,CAACV,EAAU,UAAWW,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,KAAM,CAAC,CACL,KAAM,CAAC,SAAU,UAAW6C,EAAW5C,EAAqBD,CAAgB,CACpF,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAACV,EAAUW,EAAqBD,CAAgB,CAC/D,CAAO,EAKD,QAAS,CAAC,CACR,QAAS,CAAC,OAAQ8C,EAAc7C,EAAqBD,CAAgB,CAC7E,CAAO,EAQD,SAAU,CAAC,CACT,SAAU,CAAC,SAAU,SAAS,CACtC,CAAO,EAKD,YAAa,CAAC,CACZ,YAAa,CAAC2C,EAAkB1C,EAAqBD,CAAgB,CAC7E,CAAO,EAKD,qBAAsB,CAAC,CACrB,qBAAsBiD,EAA4B,CAC1D,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQwB,GAAa,CAC7B,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAa,CACjC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAa,CACjC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,GAAa,CACjC,CAAO,EAKD,MAAO,CAAC,CACN,MAAOC,GAAY,CAC3B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAY,CAC/B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAY,CAC/B,CAAO,EAKD,UAAW,CAAC,CACV,UAAWA,GAAY,CAC/B,CAAO,EAKD,WAAY,CAAC,UAAU,EAKvB,KAAM,CAAC,CACL,KAAMC,GAAW,CACzB,CAAO,EAKD,SAAU,CAAC,CACT,SAAUA,GAAW,CAC7B,CAAO,EAKD,SAAU,CAAC,CACT,SAAUA,GAAW,CAC7B,CAAO,EAKD,UAAW,CAAC,CACV,UAAW,CAAC1E,EAAqBD,EAAkB,GAAI,OAAQ,MAAO,KAAK,CACnF,CAAO,EAKD,mBAAoB,CAAC,CACnB,OAAQiD,EAA4B,CAC5C,CAAO,EAKD,kBAAmB,CAAC,CAClB,UAAW,CAAC,KAAM,MAAM,CAChC,CAAO,EAKD,UAAW,CAAC,CACV,UAAW2B,GAAgB,CACnC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAgB,CACvC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAgB,CACvC,CAAO,EAKD,cAAe,CAAC,CACd,cAAeA,GAAgB,CACvC,CAAO,EAKD,iBAAkB,CAAC,gBAAgB,EAQnC,OAAQ,CAAC,CACP,OAAQd,EAAY,CAC5B,CAAO,EAKD,WAAY,CAAC,CACX,WAAY,CAAC,OAAQ,MAAM,CACnC,CAAO,EAKD,cAAe,CAAC,CACd,MAAOA,EAAY,CAC3B,CAAO,EAKD,eAAgB,CAAC,CACf,OAAQ,CAAC,SAAU,OAAQ,QAAS,aAAc,YAAa,YAAY,CACnF,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,UAAW,UAAW,OAAQ,OAAQ,OAAQ,OAAQ,cAAe,OAAQ,eAAgB,WAAY,OAAQ,YAAa,gBAAiB,QAAS,OAAQ,UAAW,OAAQ,WAAY,aAAc,aAAc,aAAc,WAAY,WAAY,WAAY,WAAY,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,cAAe,cAAe,UAAW,WAAY7D,EAAqBD,CAAgB,CAC1d,CAAO,EAKD,eAAgB,CAAC,CACf,eAAgB,CAAC,QAAS,SAAS,CAC3C,CAAO,EAKD,iBAAkB,CAAC,CACjB,iBAAkB,CAAC,OAAQ,MAAM,CACzC,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,GAAI,IAAK,GAAG,CACrC,CAAO,EAKD,kBAAmB,CAAC,CAClB,OAAQ,CAAC,OAAQ,QAAQ,CACjC,CAAO,EAKD,WAAY,CAAC,CACX,WAAYoD,EAAyB,CAC7C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,WAAY,CAAC,CACX,WAAYA,EAAyB,CAC7C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,YAAa,CAAC,CACZ,YAAaA,EAAyB,CAC9C,CAAO,EAKD,aAAc,CAAC,CACb,KAAM,CAAC,QAAS,MAAO,SAAU,YAAY,CACrD,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,SAAU,QAAQ,CACjC,CAAO,EAKD,YAAa,CAAC,CACZ,KAAM,CAAC,OAAQ,IAAK,IAAK,MAAM,CACvC,CAAO,EAKD,kBAAmB,CAAC,CAClB,KAAM,CAAC,YAAa,WAAW,CACvC,CAAO,EAKD,MAAO,CAAC,CACN,MAAO,CAAC,OAAQ,OAAQ,cAAc,CAC9C,CAAO,EAKD,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,OAAQ,OAAO,CAC1C,CAAO,EAKD,UAAW,CAAC,CACV,YAAa,CAAC,IAAK,KAAM,MAAM,CACvC,CAAO,EAKD,WAAY,CAAC,kBAAkB,EAK/B,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,OAAQ,MAAO,MAAM,CAC9C,CAAO,EAKD,cAAe,CAAC,CACd,cAAe,CAAC,OAAQ,SAAU,WAAY,YAAanD,EAAqBD,CAAgB,CACxG,CAAO,EAQD,KAAM,CAAC,CACL,KAAM,CAAC,OAAQ,GAAG8D,GAAY,CACtC,CAAO,EAKD,WAAY,CAAC,CACX,OAAQ,CAACxE,EAAUyB,GAA2BV,EAAmBE,EAAiB,CAC1F,CAAO,EAKD,OAAQ,CAAC,CACP,OAAQ,CAAC,OAAQ,GAAGuD,GAAY,CACxC,CAAO,EAQD,sBAAuB,CAAC,CACtB,sBAAuB,CAAC,OAAQ,MAAM,CAC9C,CAAO,CACF,EACD,uBAAwB,CACtB,SAAU,CAAC,aAAc,YAAY,EACrC,WAAY,CAAC,eAAgB,cAAc,EAC3C,MAAO,CAAC,UAAW,UAAW,QAAS,MAAO,MAAO,QAAS,SAAU,MAAM,EAC9E,UAAW,CAAC,QAAS,MAAM,EAC3B,UAAW,CAAC,MAAO,QAAQ,EAC3B,KAAM,CAAC,QAAS,OAAQ,QAAQ,EAChC,IAAK,CAAC,QAAS,OAAO,EACtB,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClD,GAAI,CAAC,KAAM,IAAI,EACf,GAAI,CAAC,KAAM,IAAI,EACf,EAAG,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClD,GAAI,CAAC,KAAM,IAAI,EACf,GAAI,CAAC,KAAM,IAAI,EACf,KAAM,CAAC,IAAK,GAAG,EACf,YAAa,CAAC,SAAS,EACvB,aAAc,CAAC,cAAe,mBAAoB,aAAc,cAAe,cAAc,EAC7F,cAAe,CAAC,YAAY,EAC5B,mBAAoB,CAAC,YAAY,EACjC,aAAc,CAAC,YAAY,EAC3B,cAAe,CAAC,YAAY,EAC5B,eAAgB,CAAC,YAAY,EAC7B,aAAc,CAAC,UAAW,UAAU,EACpC,QAAS,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,YAAY,EACtM,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,YAAa,CAAC,aAAc,YAAY,EACxC,iBAAkB,CAAC,mBAAoB,kBAAkB,EACzD,WAAY,CAAC,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,YAAY,EAC3H,aAAc,CAAC,aAAc,YAAY,EACzC,aAAc,CAAC,aAAc,YAAY,EACzC,eAAgB,CAAC,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,iBAAkB,gBAAgB,EAC/J,iBAAkB,CAAC,iBAAkB,gBAAgB,EACrD,iBAAkB,CAAC,iBAAkB,gBAAgB,EACrD,UAAW,CAAC,cAAe,cAAe,gBAAgB,EAC1D,iBAAkB,CAAC,YAAa,cAAe,cAAe,aAAa,EAC3E,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,WAAW,EACnH,YAAa,CAAC,YAAa,WAAW,EACtC,YAAa,CAAC,YAAa,WAAW,EACtC,WAAY,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,WAAW,EACnH,YAAa,CAAC,YAAa,WAAW,EACtC,YAAa,CAAC,YAAa,WAAW,EACtC,MAAO,CAAC,UAAW,UAAW,UAAU,EACxC,UAAW,CAAC,OAAO,EACnB,UAAW,CAAC,OAAO,EACnB,WAAY,CAAC,OAAO,CACrB,EACD,+BAAgC,CAC9B,YAAa,CAAC,SAAS,CACxB,EACD,wBAAyB,CAAC,IAAK,KAAM,QAAS,WAAY,SAAU,kBAAmB,OAAQ,eAAgB,aAAc,SAAU,cAAe,WAAW,CACrK,CACA,EAsDMe,GAAuB7G,GAAoB2D,EAAgB,ECp9F1D,SAASmD,MAAMC,EAAsB,CACnC,OAAAF,GAAQxM,GAAK0M,CAAM,CAAC,CAC7B,CAGgB,SAAAC,GAAYC,EAAeC,EAAmB,IAAa,CACzE,MAAO,GAAGA,CAAQ,GAAGD,EAAM,QAAQ,CAAC,CAAC,EACvC,CAQgB,SAAAE,GAAkBC,EAAuBC,EAA8B,CACrF,OAAID,GAAiBC,EAAqB,EACnC,KAAK,OAAQD,EAAgBC,GAAgBD,EAAiB,GAAG,CAC1E,CAkBgB,SAAAE,GACd/K,EACAgL,EACkC,CAC9B,IAAAC,EACJ,MAAO,IAAInP,IAAwB,CACjC,aAAamP,CAAO,EACpBA,EAAU,WAAW,IAAMjL,EAAK,GAAGlE,CAAI,EAAGkP,CAAI,CAAA,CAElD,CAkFgB,SAAAE,GAAcC,EAAgBC,EAAmB,EAAc,CAC7E,OAAO,MAAM,KAAK,CAAE,OAAQA,CAAS,EAAG,CAACC,EAAG1U,IAAUA,EAAQ,KAAK,MAAMwU,CAAM,CAAC,CAClF,CCjIA,MAAMG,EAAgC,CAAC,CACrC,QAAAC,EAAU,UACV,KAAAnU,EAAO,KACP,QAAAoU,EAAU,GACV,SAAAC,EACA,UAAA/U,EACA,SAAAa,EACA,GAAGT,CACL,IAAM,CACJ,MAAM4U,EAAc,MACdC,EAAyC,CAC7C,QAAS,cACT,UAAW,gBACX,QAAS,cACT,MAAO,YACP,OAAQ,YAAA,EAEJC,EAAsC,CAC1C,GAAI,SACJ,GAAI,SACJ,GAAI,QAAA,EAIJ,OAAAC,EAAC,SAAA,CACC,UAAWtB,GACTmB,EACAC,EAAeJ,CAAO,EACtBK,EAAYxU,CAAI,EAChBoU,GAAW,gCACX9U,CACF,EACA,SAAU+U,GAAYD,EACrB,GAAG1U,EAEH,SAAA,CACC0U,GAAAM,EAAC,MAAI,CAAA,UAAU,8BAA+B,CAAA,EAE/CvU,CAAA,CAAA,CAAA,CAGP,ECvCMwU,GAAsC,CAAC,CAC3C,SAAAC,EACA,YAAAC,EAAc,UACd,aAAAC,EAAe,GACf,UAAAxV,CACF,IAAM,CACJ,KAAM,CAACyV,EAASC,CAAU,EAAIC,WAASH,CAAY,EAG7CI,EAAkBC,EAAA,YACtBxB,GAAUxK,GAAkB,CACjByL,EAAAzL,EAAM,MAAM,GACpB,GAAG,EACN,CAACyL,CAAQ,CAAA,EAoBT,OAAAF,EAAC,QAAK,SANc7Q,GAAuB,CAC3CA,EAAE,eAAe,EACR+Q,EAAAG,EAAQ,MAAM,CAAA,EAIO,UAAAzV,EAC5B,SAACmV,EAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAAAC,EAAC,OAAI,UAAU,uEACb,WAACxT,GAAO,CAAA,UAAU,wBAAwB,CAC5C,CAAA,EAEAwT,EAAC,QAAA,CACC,KAAK,OACL,MAAOK,EACP,SA1BmBlR,GAA2C,CAC9D,MAAAsF,EAAQtF,EAAE,OAAO,MACvBmR,EAAW7L,CAAK,EAChB+L,EAAgB/L,CAAK,CAAA,EAwBf,YAAA0L,EACA,UAAU,0BAAA,CACZ,EAECE,GACCL,EAAC,MAAI,CAAA,UAAU,oDACb,SAAAA,EAACR,EAAA,CACC,KAAK,SACL,QAAQ,QACR,KAAK,KACL,QA/BQ,IAAM,CACxBc,EAAW,EAAE,EACbJ,EAAS,EAAE,CAAA,EA8BD,UAAU,qCAEV,SAAAF,EAACrT,GAAE,CAAA,UAAU,uBAAwB,CAAA,CAAA,CAAA,EAEzC,CAAA,CAEJ,CAAA,CACF,CAAA,CAEJ,EC5DM+T,GAAgC,CAAC,CAAE,SAAAR,KAAe,CACtD,KAAM,CAACS,EAAkBC,CAAmB,EAAIL,WAAS,EAAK,EACxD,CAAE,WAAApP,EAAY,WAAAG,CAAW,EAAIC,GAAa,EAE1CsP,EAAmB,IAAM,CAC7BD,EAAoB,CAACD,CAAgB,CAAA,EAIrC,OAAAZ,EAAC,SAAO,CAAA,UAAU,gDAChB,SAAA,CAACA,EAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAA,MAAA,CAAI,UAAU,yCAEb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,oBACb,SAAAD,EAACe,IAAK,GAAG,IAAI,UAAU,8BACrB,SAAA,CAACd,EAAA,MAAA,CAAI,UAAU,qEACb,SAAAA,EAAC,QAAK,UAAU,+BAA+B,eAAG,CACpD,CAAA,EACCA,EAAA,OAAA,CAAK,UAAU,kDAAkD,SAElE,OAAA,CAAA,CAAA,CACF,CACF,CAAA,IAGC,MAAI,CAAA,UAAU,uCACb,SAACA,EAAAC,GAAA,CAAU,SAAAC,CAAoB,CAAA,EACjC,EAGAH,EAAC,MAAI,CAAA,UAAU,8BAEb,SAAA,CAAAC,EAACc,GAAK,CAAA,GAAG,QAAQ,UAAU,WACzB,SAAAf,EAACP,EAAO,CAAA,QAAQ,QAAQ,KAAK,KAAK,UAAU,MAC1C,SAAA,CAACQ,EAAAvT,GAAA,CAAa,UAAU,SAAU,CAAA,EACjC0E,EAAa,GACX6O,EAAA,OAAA,CAAK,UAAU,+GACb,SAAA7O,EAAa,GAAK,MAAQA,CAC7B,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,EAGA6O,EAACR,EAAA,CACC,QAAQ,QACR,KAAK,KACL,UAAU,gBACV,QAASqB,EAER,SAAAF,IACEhU,GAAE,CAAA,UAAU,UAAU,EAEvBqT,EAAC3T,GAAK,CAAA,UAAU,SAAU,CAAA,CAAA,CAE9B,CAAA,EACF,CAAA,EACF,IAGC,MAAI,CAAA,UAAU,iBACb,SAAC2T,EAAAC,GAAA,CAAU,SAAAC,CAAoB,CAAA,EACjC,CAAA,EACF,EAGCS,KACE,MAAI,CAAA,UAAU,8BACb,SAACZ,EAAA,MAAA,CAAI,UAAU,sBACb,SAAA,CAAAC,EAACc,GAAA,CACC,GAAG,IACH,UAAU,yDACV,QAAS,IAAMF,EAAoB,EAAK,EACzC,SAAA,IAAA,CAED,EACAZ,EAACc,GAAA,CACC,GAAG,cACH,UAAU,yDACV,QAAS,IAAMF,EAAoB,EAAK,EACzC,SAAA,IAAA,CAED,EACAb,EAACe,GAAA,CACC,GAAG,QACH,UAAU,qFACV,QAAS,IAAMF,EAAoB,EAAK,EAExC,SAAA,CAAAZ,EAAC,QAAK,SAAG,KAAA,CAAA,EACTD,EAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACA,EAAA,OAAA,CAAK,UAAU,wBACb,SAAA,CAAA5O,EAAW,MAAA,EACd,IACC,OAAK,CAAA,UAAU,uCACb,SAAAwN,GAAYrN,CAAU,EACzB,CAAA,EACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,CAAA,CAAA,CAEJ,EC9Ge,SAASyP,GAAKjS,EAAIkS,EAAS,CACxC,OAAO,UAAgB,CACrB,OAAOlS,EAAG,MAAMkS,EAAS,SAAS,CACtC,CACA,CCAA,KAAM,CAAC,SAAAC,EAAQ,EAAI,OAAO,UACpB,CAAC,eAAAC,EAAc,EAAI,OACnB,CAAC,SAAAC,GAAU,YAAAC,EAAW,EAAI,OAE1BC,IAAU/M,GAASgN,GAAS,CAC9B,MAAM3S,EAAMsS,GAAS,KAAKK,CAAK,EAC/B,OAAOhN,EAAM3F,CAAG,IAAM2F,EAAM3F,CAAG,EAAIA,EAAI,MAAM,EAAG,EAAE,EAAE,YAAa,EACrE,GAAG,OAAO,OAAO,IAAI,CAAC,EAEhB4S,EAAcC,IAClBA,EAAOA,EAAK,cACJF,GAAUD,GAAOC,CAAK,IAAME,GAGhCC,GAAaD,GAAQF,GAAS,OAAOA,IAAUE,EAS/C,CAAC,QAAAE,EAAO,EAAI,MASZC,GAAcF,GAAW,WAAW,EAS1C,SAASG,GAASC,EAAK,CACrB,OAAOA,IAAQ,MAAQ,CAACF,GAAYE,CAAG,GAAKA,EAAI,cAAgB,MAAQ,CAACF,GAAYE,EAAI,WAAW,GAC/FC,EAAWD,EAAI,YAAY,QAAQ,GAAKA,EAAI,YAAY,SAASA,CAAG,CAC3E,CASA,MAAME,GAAgBR,EAAW,aAAa,EAU9C,SAASS,GAAkBH,EAAK,CAC9B,IAAI7S,EACJ,OAAK,OAAO,YAAgB,KAAiB,YAAY,OACvDA,EAAS,YAAY,OAAO6S,CAAG,EAE/B7S,EAAU6S,GAASA,EAAI,QAAYE,GAAcF,EAAI,MAAM,EAEtD7S,CACT,CASA,MAAMiT,GAAWR,GAAW,QAAQ,EAQ9BK,EAAaL,GAAW,UAAU,EASlCxI,GAAWwI,GAAW,QAAQ,EAS9BS,GAAYZ,GAAUA,IAAU,MAAQ,OAAOA,GAAU,SAQzDa,GAAYb,GAASA,IAAU,IAAQA,IAAU,GASjDc,GAAiBP,GAAQ,CAC7B,GAAIR,GAAOQ,CAAG,IAAM,SAClB,MAAO,GAGT,MAAMQ,EAAYnB,GAAeW,CAAG,EACpC,OAAQQ,IAAc,MAAQA,IAAc,OAAO,WAAa,OAAO,eAAeA,CAAS,IAAM,OAAS,EAAEjB,MAAeS,IAAQ,EAAEV,MAAYU,EACvJ,EASMS,GAASf,EAAW,MAAM,EAS1BgB,GAAShB,EAAW,MAAM,EAS1BiB,GAASjB,EAAW,MAAM,EAS1BkB,GAAalB,EAAW,UAAU,EASlCmB,GAAYb,GAAQK,GAASL,CAAG,GAAKC,EAAWD,EAAI,IAAI,EASxDc,GAAcrB,GAAU,CAC5B,IAAIsB,EACJ,OAAOtB,IACJ,OAAO,UAAa,YAAcA,aAAiB,UAClDQ,EAAWR,EAAM,MAAM,KACpBsB,EAAOvB,GAAOC,CAAK,KAAO,YAE1BsB,IAAS,UAAYd,EAAWR,EAAM,QAAQ,GAAKA,EAAM,SAAU,IAAK,qBAIjF,EASMuB,GAAoBtB,EAAW,iBAAiB,EAEhD,CAACuB,GAAkBC,GAAWC,GAAYC,EAAS,EAAI,CAAC,iBAAkB,UAAW,WAAY,SAAS,EAAE,IAAI1B,CAAU,EAS1H2B,GAAQvU,GAAQA,EAAI,KACxBA,EAAI,KAAI,EAAKA,EAAI,QAAQ,qCAAsC,EAAE,EAiBnE,SAASwU,GAAQC,EAAKtU,EAAI,CAAC,WAAAuU,EAAa,EAAK,EAAI,GAAI,CAEnD,GAAID,IAAQ,MAAQ,OAAOA,EAAQ,IACjC,OAGF,IAAIjM,EACAmM,EAQJ,GALI,OAAOF,GAAQ,WAEjBA,EAAM,CAACA,CAAG,GAGR1B,GAAQ0B,CAAG,EAEb,IAAKjM,EAAI,EAAGmM,EAAIF,EAAI,OAAQjM,EAAImM,EAAGnM,IACjCrI,EAAG,KAAK,KAAMsU,EAAIjM,CAAC,EAAGA,EAAGiM,CAAG,MAEzB,CAEL,MAAMG,EAAOF,EAAa,OAAO,oBAAoBD,CAAG,EAAI,OAAO,KAAKA,CAAG,EACrEI,EAAMD,EAAK,OACjB,IAAIzP,EAEJ,IAAKqD,EAAI,EAAGA,EAAIqM,EAAKrM,IACnBrD,EAAMyP,EAAKpM,CAAC,EACZrI,EAAG,KAAK,KAAMsU,EAAItP,CAAG,EAAGA,EAAKsP,CAAG,CAEnC,CACH,CAEA,SAASK,GAAQL,EAAKtP,EAAK,CACzBA,EAAMA,EAAI,cACV,MAAMyP,EAAO,OAAO,KAAKH,CAAG,EAC5B,IAAIjM,EAAIoM,EAAK,OACTG,EACJ,KAAOvM,KAAM,GAEX,GADAuM,EAAOH,EAAKpM,CAAC,EACTrD,IAAQ4P,EAAK,cACf,OAAOA,EAGX,OAAO,IACT,CAEA,MAAMC,IAAW,IAEX,OAAO,WAAe,IAAoB,WACvC,OAAO,KAAS,IAAc,KAAQ,OAAO,OAAW,IAAc,OAAS,UAGlFC,GAAoBC,GAAY,CAAClC,GAAYkC,CAAO,GAAKA,IAAYF,GAoB3E,SAASG,IAAmC,CAC1C,KAAM,CAAC,SAAAC,CAAQ,EAAIH,GAAiB,IAAI,GAAK,MAAQ,GAC/C5U,EAAS,CAAA,EACTgV,EAAc,CAACnC,EAAK/N,IAAQ,CAChC,MAAMmQ,EAAYF,GAAYN,GAAQzU,EAAQ8E,CAAG,GAAKA,EAClDsO,GAAcpT,EAAOiV,CAAS,CAAC,GAAK7B,GAAcP,CAAG,EACvD7S,EAAOiV,CAAS,EAAIH,GAAM9U,EAAOiV,CAAS,EAAGpC,CAAG,EACvCO,GAAcP,CAAG,EAC1B7S,EAAOiV,CAAS,EAAIH,GAAM,CAAE,EAAEjC,CAAG,EACxBH,GAAQG,CAAG,EACpB7S,EAAOiV,CAAS,EAAIpC,EAAI,MAAK,EAE7B7S,EAAOiV,CAAS,EAAIpC,CAEvB,EAED,QAAS1K,EAAI,EAAGmM,EAAI,UAAU,OAAQnM,EAAImM,EAAGnM,IAC3C,UAAUA,CAAC,GAAKgM,GAAQ,UAAUhM,CAAC,EAAG6M,CAAW,EAEnD,OAAOhV,CACT,CAYA,MAAMkV,GAAS,CAACC,EAAGC,EAAGpD,EAAS,CAAC,WAAAqC,CAAU,EAAG,MAC3CF,GAAQiB,EAAG,CAACvC,EAAK/N,IAAQ,CACnBkN,GAAWc,EAAWD,CAAG,EAC3BsC,EAAErQ,CAAG,EAAIiN,GAAKc,EAAKb,CAAO,EAE1BmD,EAAErQ,CAAG,EAAI+N,CAEf,EAAK,CAAC,WAAAwB,CAAU,CAAC,EACRc,GAUHE,GAAYC,IACZA,EAAQ,WAAW,CAAC,IAAM,QAC5BA,EAAUA,EAAQ,MAAM,CAAC,GAEpBA,GAYHC,GAAW,CAACC,EAAaC,EAAkBzZ,EAAO0Z,IAAgB,CACtEF,EAAY,UAAY,OAAO,OAAOC,EAAiB,UAAWC,CAAW,EAC7EF,EAAY,UAAU,YAAcA,EACpC,OAAO,eAAeA,EAAa,QAAS,CAC1C,MAAOC,EAAiB,SAC5B,CAAG,EACDzZ,GAAS,OAAO,OAAOwZ,EAAY,UAAWxZ,CAAK,CACrD,EAWM2Z,GAAe,CAACC,EAAWC,EAASC,EAAQC,IAAe,CAC/D,IAAI/Z,EACAmM,EACAlM,EACJ,MAAM+Z,EAAS,CAAA,EAIf,GAFAH,EAAUA,GAAW,GAEjBD,GAAa,KAAM,OAAOC,EAE9B,EAAG,CAGD,IAFA7Z,EAAQ,OAAO,oBAAoB4Z,CAAS,EAC5CzN,EAAInM,EAAM,OACHmM,KAAM,GACXlM,EAAOD,EAAMmM,CAAC,GACT,CAAC4N,GAAcA,EAAW9Z,EAAM2Z,EAAWC,CAAO,IAAM,CAACG,EAAO/Z,CAAI,IACvE4Z,EAAQ5Z,CAAI,EAAI2Z,EAAU3Z,CAAI,EAC9B+Z,EAAO/Z,CAAI,EAAI,IAGnB2Z,EAAYE,IAAW,IAAS5D,GAAe0D,CAAS,CAC5D,OAAWA,IAAc,CAACE,GAAUA,EAAOF,EAAWC,CAAO,IAAMD,IAAc,OAAO,WAEtF,OAAOC,CACT,EAWMI,GAAW,CAACtW,EAAKuW,EAAcC,IAAa,CAChDxW,EAAM,OAAOA,CAAG,GACZwW,IAAa,QAAaA,EAAWxW,EAAI,UAC3CwW,EAAWxW,EAAI,QAEjBwW,GAAYD,EAAa,OACzB,MAAME,EAAYzW,EAAI,QAAQuW,EAAcC,CAAQ,EACpD,OAAOC,IAAc,IAAMA,IAAcD,CAC3C,EAUME,GAAW/D,GAAU,CACzB,GAAI,CAACA,EAAO,OAAO,KACnB,GAAII,GAAQJ,CAAK,EAAG,OAAOA,EAC3B,IAAInK,EAAImK,EAAM,OACd,GAAI,CAACrI,GAAS9B,CAAC,EAAG,OAAO,KACzB,MAAMmO,EAAM,IAAI,MAAMnO,CAAC,EACvB,KAAOA,KAAM,GACXmO,EAAInO,CAAC,EAAImK,EAAMnK,CAAC,EAElB,OAAOmO,CACT,EAWMC,IAAgBC,GAEblE,GACEkE,GAAclE,aAAiBkE,GAEvC,OAAO,WAAe,KAAetE,GAAe,UAAU,CAAC,EAU5DuE,GAAe,CAACrC,EAAKtU,IAAO,CAGhC,MAAM4W,GAFYtC,GAAOA,EAAIjC,EAAQ,GAET,KAAKiC,CAAG,EAEpC,IAAIpU,EAEJ,MAAQA,EAAS0W,EAAU,KAAI,IAAO,CAAC1W,EAAO,MAAM,CAClD,MAAM2W,EAAO3W,EAAO,MACpBF,EAAG,KAAKsU,EAAKuC,EAAK,CAAC,EAAGA,EAAK,CAAC,CAAC,CAC9B,CACH,EAUMC,GAAW,CAACC,EAAQlX,IAAQ,CAChC,IAAImX,EACJ,MAAMR,EAAM,CAAA,EAEZ,MAAQQ,EAAUD,EAAO,KAAKlX,CAAG,KAAO,MACtC2W,EAAI,KAAKQ,CAAO,EAGlB,OAAOR,CACT,EAGMS,GAAaxE,EAAW,iBAAiB,EAEzCnX,GAAcuE,GACXA,EAAI,cAAc,QAAQ,wBAC/B,SAAkB5E,EAAGO,EAAIC,EAAI,CAC3B,OAAOD,EAAG,YAAa,EAAGC,CAC3B,CACL,EAIMyb,IAAkB,CAAC,CAAC,eAAAA,CAAc,IAAM,CAAC5C,EAAKnY,IAAS+a,EAAe,KAAK5C,EAAKnY,CAAI,GAAG,OAAO,SAAS,EASvGgb,GAAW1E,EAAW,QAAQ,EAE9B2E,GAAoB,CAAC9C,EAAK+C,IAAY,CAC1C,MAAMzB,EAAc,OAAO,0BAA0BtB,CAAG,EAClDgD,EAAqB,CAAA,EAE3BjD,GAAQuB,EAAa,CAAC2B,EAAY9X,IAAS,CACzC,IAAI+X,GACCA,EAAMH,EAAQE,EAAY9X,EAAM6U,CAAG,KAAO,KAC7CgD,EAAmB7X,CAAI,EAAI+X,GAAOD,EAExC,CAAG,EAED,OAAO,iBAAiBjD,EAAKgD,CAAkB,CACjD,EAOMG,GAAiBnD,GAAQ,CAC7B8C,GAAkB9C,EAAK,CAACiD,EAAY9X,IAAS,CAE3C,GAAIuT,EAAWsB,CAAG,GAAK,CAAC,YAAa,SAAU,QAAQ,EAAE,QAAQ7U,CAAI,IAAM,GACzE,MAAO,GAGT,MAAMkG,EAAQ2O,EAAI7U,CAAI,EAEtB,GAAKuT,EAAWrN,CAAK,EAIrB,IAFA4R,EAAW,WAAa,GAEpB,aAAcA,EAAY,CAC5BA,EAAW,SAAW,GACtB,MACD,CAEIA,EAAW,MACdA,EAAW,IAAM,IAAM,CACrB,MAAM,MAAM,qCAAwC9X,EAAO,GAAI,CACvE,GAEA,CAAG,CACH,EAEMiY,GAAc,CAACC,EAAeC,IAAc,CAChD,MAAMtD,EAAM,CAAA,EAENuD,EAAUrB,GAAQ,CACtBA,EAAI,QAAQ7Q,GAAS,CACnB2O,EAAI3O,CAAK,EAAI,EACnB,CAAK,CACF,EAED,OAAAiN,GAAQ+E,CAAa,EAAIE,EAAOF,CAAa,EAAIE,EAAO,OAAOF,CAAa,EAAE,MAAMC,CAAS,CAAC,EAEvFtD,CACT,EAEMwD,GAAO,IAAM,CAAE,EAEfC,GAAiB,CAACpS,EAAO2L,IACtB3L,GAAS,MAAQ,OAAO,SAASA,EAAQ,CAACA,CAAK,EAAIA,EAAQ2L,EAUpE,SAAS0G,GAAoBxF,EAAO,CAClC,MAAO,CAAC,EAAEA,GAASQ,EAAWR,EAAM,MAAM,GAAKA,EAAMF,EAAW,IAAM,YAAcE,EAAMH,EAAQ,EACpG,CAEA,MAAM4F,GAAgB3D,GAAQ,CAC5B,MAAM4D,EAAQ,IAAI,MAAM,EAAE,EAEpBC,EAAQ,CAACC,EAAQ/P,IAAM,CAE3B,GAAI+K,GAASgF,CAAM,EAAG,CACpB,GAAIF,EAAM,QAAQE,CAAM,GAAK,EAC3B,OAGF,GAAG,EAAE,WAAYA,GAAS,CACxBF,EAAM7P,CAAC,EAAI+P,EACX,MAAMC,EAASzF,GAAQwF,CAAM,EAAI,CAAA,EAAK,CAAA,EAEtC,OAAA/D,GAAQ+D,EAAQ,CAACzS,EAAOX,IAAQ,CAC9B,MAAMsT,EAAeH,EAAMxS,EAAO0C,EAAI,CAAC,EACvC,CAACwK,GAAYyF,CAAY,IAAMD,EAAOrT,CAAG,EAAIsT,EACvD,CAAS,EAEDJ,EAAM7P,CAAC,EAAI,OAEJgQ,CACR,CACF,CAED,OAAOD,CACR,EAED,OAAOD,EAAM7D,EAAK,CAAC,CACrB,EAEMiE,GAAY9F,EAAW,eAAe,EAEtC+F,GAAchG,GAClBA,IAAUY,GAASZ,CAAK,GAAKQ,EAAWR,CAAK,IAAMQ,EAAWR,EAAM,IAAI,GAAKQ,EAAWR,EAAM,KAAK,EAK/FiG,IAAiB,CAACC,EAAuBC,IACzCD,EACK,aAGFC,GAAwB,CAACC,EAAOC,KACrChE,GAAQ,iBAAiB,UAAW,CAAC,CAAC,OAAAuD,EAAQ,KAAAU,CAAI,IAAM,CAClDV,IAAWvD,IAAWiE,IAASF,GACjCC,EAAU,QAAUA,EAAU,MAAO,EAAA,CAExC,EAAE,EAAK,EAEApX,GAAO,CACboX,EAAU,KAAKpX,CAAE,EACjBoT,GAAQ,YAAY+D,EAAO,GAAG,CAC/B,IACA,SAAS,KAAK,QAAQ,GAAI,CAAE,CAAA,EAAKnX,GAAO,WAAWA,CAAE,GAExD,OAAO,cAAiB,WACxBuR,EAAW6B,GAAQ,WAAW,CAChC,EAEMkE,GAAO,OAAO,eAAmB,IACrC,eAAe,KAAKlE,EAAO,EAAM,OAAO,QAAY,KAAe,QAAQ,UAAY4D,GAKnFO,GAAcxG,GAAUA,GAAS,MAAQQ,EAAWR,EAAMH,EAAQ,CAAC,EAG1D4G,EAAA,CACb,QAAArG,GACA,cAAAK,GACA,SAAAH,GACA,WAAAe,GACA,kBAAAX,GACA,SAAAC,GACA,SAAAhJ,GACA,UAAAkJ,GACA,SAAAD,GACA,cAAAE,GACA,iBAAAU,GACA,UAAAC,GACA,WAAAC,GACA,UAAAC,GACA,YAAAtB,GACA,OAAAW,GACA,OAAAC,GACA,OAAAC,GACA,SAAAyD,GACA,WAAAnE,EACA,SAAAY,GACA,kBAAAG,GACA,aAAA0C,GACA,WAAA9C,GACA,QAAAU,GACA,MAAAW,GACA,OAAAI,GACA,KAAAhB,GACA,SAAAmB,GACA,SAAAE,GACA,aAAAI,GACA,OAAAtD,GACA,WAAAE,EACA,SAAA0D,GACA,QAAAI,GACA,aAAAI,GACA,SAAAG,GACA,WAAAG,GACA,eAAAC,GACA,WAAYA,GACZ,kBAAAE,GACA,cAAAK,GACA,YAAAC,GACA,YAAApc,GACA,KAAAwc,GACA,eAAAC,GACA,QAAApD,GACA,OAAQE,GACR,iBAAAC,GACA,oBAAAkD,GACA,aAAAC,GACA,UAAAM,GACA,WAAAC,GACA,aAAcC,GACd,KAAAM,GACA,WAAAC,EACF,ECxtBA,SAASE,EAAWC,EAASC,EAAM3Y,EAAQ4Y,EAASC,EAAU,CAC5D,MAAM,KAAK,IAAI,EAEX,MAAM,kBACR,MAAM,kBAAkB,KAAM,KAAK,WAAW,EAE9C,KAAK,MAAS,IAAI,MAAK,EAAI,MAG7B,KAAK,QAAUH,EACf,KAAK,KAAO,aACZC,IAAS,KAAK,KAAOA,GACrB3Y,IAAW,KAAK,OAASA,GACzB4Y,IAAY,KAAK,QAAUA,GACvBC,IACF,KAAK,SAAWA,EAChB,KAAK,OAASA,EAAS,OAASA,EAAS,OAAS,KAEtD,CAEAC,EAAM,SAASL,EAAY,MAAO,CAChC,OAAQ,UAAkB,CACxB,MAAO,CAEL,QAAS,KAAK,QACd,KAAM,KAAK,KAEX,YAAa,KAAK,YAClB,OAAQ,KAAK,OAEb,SAAU,KAAK,SACf,WAAY,KAAK,WACjB,aAAc,KAAK,aACnB,MAAO,KAAK,MAEZ,OAAQK,EAAM,aAAa,KAAK,MAAM,EACtC,KAAM,KAAK,KACX,OAAQ,KAAK,MACnB,CACG,CACH,CAAC,EAED,MAAMhG,GAAY2F,EAAW,UACvBtD,GAAc,CAAA,EAEpB,CACE,uBACA,iBACA,eACA,YACA,cACA,4BACA,iBACA,mBACA,kBACA,eACA,kBACA,iBAEF,EAAE,QAAQwD,GAAQ,CAChBxD,GAAYwD,CAAI,EAAI,CAAC,MAAOA,CAAI,CAClC,CAAC,EAED,OAAO,iBAAiBF,EAAYtD,EAAW,EAC/C,OAAO,eAAerC,GAAW,eAAgB,CAAC,MAAO,EAAI,CAAC,EAG9D2F,EAAW,KAAO,CAACM,EAAOJ,EAAM3Y,EAAQ4Y,EAASC,EAAUG,IAAgB,CACzE,MAAMC,EAAa,OAAO,OAAOnG,EAAS,EAE1CgG,OAAAA,EAAM,aAAaC,EAAOE,EAAY,SAAgBpF,EAAK,CACzD,OAAOA,IAAQ,MAAM,SACtB,EAAEnY,GACMA,IAAS,cACjB,EAED+c,EAAW,KAAKQ,EAAYF,EAAM,QAASJ,EAAM3Y,EAAQ4Y,EAASC,CAAQ,EAE1EI,EAAW,MAAQF,EAEnBE,EAAW,KAAOF,EAAM,KAExBC,GAAe,OAAO,OAAOC,EAAYD,CAAW,EAE7CC,CACT,ECnGA,MAAAC,GAAe,KCaf,SAASC,GAAYpH,EAAO,CAC1B,OAAO+G,EAAM,cAAc/G,CAAK,GAAK+G,EAAM,QAAQ/G,CAAK,CAC1D,CASA,SAASqH,GAAe7U,EAAK,CAC3B,OAAOuU,EAAM,SAASvU,EAAK,IAAI,EAAIA,EAAI,MAAM,EAAG,EAAE,EAAIA,CACxD,CAWA,SAAS8U,GAAU7U,EAAMD,EAAK+U,EAAM,CAClC,OAAK9U,EACEA,EAAK,OAAOD,CAAG,EAAE,IAAI,SAAc4T,EAAOvQ,EAAG,CAElD,OAAAuQ,EAAQiB,GAAejB,CAAK,EACrB,CAACmB,GAAQ1R,EAAI,IAAMuQ,EAAQ,IAAMA,CACzC,CAAA,EAAE,KAAKmB,EAAO,IAAM,EAAE,EALL/U,CAMpB,CASA,SAASgV,GAAYxD,EAAK,CACxB,OAAO+C,EAAM,QAAQ/C,CAAG,GAAK,CAACA,EAAI,KAAKoD,EAAW,CACpD,CAEA,MAAMK,GAAaV,EAAM,aAAaA,EAAO,CAAE,EAAE,KAAM,SAAgBpd,EAAM,CAC3E,MAAO,WAAW,KAAKA,CAAI,CAC7B,CAAC,EAyBD,SAAS+d,GAAW5F,EAAK6F,EAAU5a,EAAS,CAC1C,GAAI,CAACga,EAAM,SAASjF,CAAG,EACrB,MAAM,IAAI,UAAU,0BAA0B,EAIhD6F,EAAWA,GAAY,IAAyB,SAGhD5a,EAAUga,EAAM,aAAaha,EAAS,CACpC,WAAY,GACZ,KAAM,GACN,QAAS,EACV,EAAE,GAAO,SAAiB6a,EAAQhC,EAAQ,CAEzC,MAAO,CAACmB,EAAM,YAAYnB,EAAOgC,CAAM,CAAC,CAC5C,CAAG,EAED,MAAMC,EAAa9a,EAAQ,WAErB+a,EAAU/a,EAAQ,SAAWgb,EAC7BR,EAAOxa,EAAQ,KACfib,EAAUjb,EAAQ,QAElBkb,GADQlb,EAAQ,MAAQ,OAAO,KAAS,KAAe,OACpCga,EAAM,oBAAoBY,CAAQ,EAE3D,GAAI,CAACZ,EAAM,WAAWe,CAAO,EAC3B,MAAM,IAAI,UAAU,4BAA4B,EAGlD,SAASI,EAAa/U,EAAO,CAC3B,GAAIA,IAAU,KAAM,MAAO,GAE3B,GAAI4T,EAAM,OAAO5T,CAAK,EACpB,OAAOA,EAAM,cAGf,GAAI4T,EAAM,UAAU5T,CAAK,EACvB,OAAOA,EAAM,WAGf,GAAI,CAAC8U,GAAWlB,EAAM,OAAO5T,CAAK,EAChC,MAAM,IAAIuT,EAAW,8CAA8C,EAGrE,OAAIK,EAAM,cAAc5T,CAAK,GAAK4T,EAAM,aAAa5T,CAAK,EACjD8U,GAAW,OAAO,MAAS,WAAa,IAAI,KAAK,CAAC9U,CAAK,CAAC,EAAI,OAAO,KAAKA,CAAK,EAG/EA,CACR,CAYD,SAAS4U,EAAe5U,EAAOX,EAAKC,EAAM,CACxC,IAAIuR,EAAM7Q,EAEV,GAAIA,GAAS,CAACV,GAAQ,OAAOU,GAAU,UACrC,GAAI4T,EAAM,SAASvU,EAAK,IAAI,EAE1BA,EAAMqV,EAAarV,EAAMA,EAAI,MAAM,EAAG,EAAE,EAExCW,EAAQ,KAAK,UAAUA,CAAK,UAE3B4T,EAAM,QAAQ5T,CAAK,GAAKqU,GAAYrU,CAAK,IACxC4T,EAAM,WAAW5T,CAAK,GAAK4T,EAAM,SAASvU,EAAK,IAAI,KAAOwR,EAAM+C,EAAM,QAAQ5T,CAAK,GAGrF,OAAAX,EAAM6U,GAAe7U,CAAG,EAExBwR,EAAI,QAAQ,SAAcmE,EAAI5e,EAAO,CACnC,EAAEwd,EAAM,YAAYoB,CAAE,GAAKA,IAAO,OAASR,EAAS,OAElDK,IAAY,GAAOV,GAAU,CAAC9U,CAAG,EAAGjJ,EAAOge,CAAI,EAAKS,IAAY,KAAOxV,EAAMA,EAAM,KACnF0V,EAAaC,CAAE,CAC3B,CACA,CAAS,EACM,GAIX,OAAIf,GAAYjU,CAAK,EACZ,IAGTwU,EAAS,OAAOL,GAAU7U,EAAMD,EAAK+U,CAAI,EAAGW,EAAa/U,CAAK,CAAC,EAExD,GACR,CAED,MAAMuS,EAAQ,CAAA,EAER0C,EAAiB,OAAO,OAAOX,GAAY,CAC/C,eAAAM,EACA,aAAAG,EACA,YAAAd,EACJ,CAAG,EAED,SAASiB,EAAMlV,EAAOV,EAAM,CAC1B,GAAIsU,CAAAA,EAAM,YAAY5T,CAAK,EAE3B,IAAIuS,EAAM,QAAQvS,CAAK,IAAM,GAC3B,MAAM,MAAM,kCAAoCV,EAAK,KAAK,GAAG,CAAC,EAGhEiT,EAAM,KAAKvS,CAAK,EAEhB4T,EAAM,QAAQ5T,EAAO,SAAcgV,EAAI3V,EAAK,EAC3B,EAAEuU,EAAM,YAAYoB,CAAE,GAAKA,IAAO,OAASL,EAAQ,KAChEH,EAAUQ,EAAIpB,EAAM,SAASvU,CAAG,EAAIA,EAAI,KAAM,EAAGA,EAAKC,EAAM2V,CACpE,KAEqB,IACbC,EAAMF,EAAI1V,EAAOA,EAAK,OAAOD,CAAG,EAAI,CAACA,CAAG,CAAC,CAEjD,CAAK,EAEDkT,EAAM,IAAG,EACV,CAED,GAAI,CAACqB,EAAM,SAASjF,CAAG,EACrB,MAAM,IAAI,UAAU,wBAAwB,EAG9C,OAAAuG,EAAMvG,CAAG,EAEF6F,CACT,CChNA,SAASW,GAAOjb,EAAK,CACnB,MAAMkb,EAAU,CACd,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,MACL,MAAO,IACP,MAAO,IACX,EACE,OAAO,mBAAmBlb,CAAG,EAAE,QAAQ,mBAAoB,SAAkBtE,EAAO,CAClF,OAAOwf,EAAQxf,CAAK,CACxB,CAAG,CACH,CAUA,SAASyf,GAAqBC,EAAQ1b,EAAS,CAC7C,KAAK,OAAS,GAEd0b,GAAUf,GAAWe,EAAQ,KAAM1b,CAAO,CAC5C,CAEA,MAAMgU,GAAYyH,GAAqB,UAEvCzH,GAAU,OAAS,SAAgB9T,EAAMkG,EAAO,CAC9C,KAAK,OAAO,KAAK,CAAClG,EAAMkG,CAAK,CAAC,CAChC,EAEA4N,GAAU,SAAW,SAAkB2H,EAAS,CAC9C,MAAMC,EAAUD,EAAU,SAASvV,EAAO,CACxC,OAAOuV,EAAQ,KAAK,KAAMvV,EAAOmV,EAAM,CACxC,EAAGA,GAEJ,OAAO,KAAK,OAAO,IAAI,SAAcjE,EAAM,CACzC,OAAOsE,EAAQtE,EAAK,CAAC,CAAC,EAAI,IAAMsE,EAAQtE,EAAK,CAAC,CAAC,CAChD,EAAE,EAAE,EAAE,KAAK,GAAG,CACjB,EC1CA,SAASiE,GAAO/H,EAAK,CACnB,OAAO,mBAAmBA,CAAG,EAC3B,QAAQ,QAAS,GAAG,EACpB,QAAQ,OAAQ,GAAG,EACnB,QAAQ,QAAS,GAAG,EACpB,QAAQ,OAAQ,GAAG,EACnB,QAAQ,QAAS,GAAG,EACpB,QAAQ,QAAS,GAAG,CACxB,CAWe,SAASqI,GAASC,EAAKJ,EAAQ1b,EAAS,CAErD,GAAI,CAAC0b,EACH,OAAOI,EAGT,MAAMF,EAAU5b,GAAWA,EAAQ,QAAUub,GAEzCvB,EAAM,WAAWha,CAAO,IAC1BA,EAAU,CACR,UAAWA,CACjB,GAGE,MAAM+b,EAAc/b,GAAWA,EAAQ,UAEvC,IAAIgc,EAUJ,GARID,EACFC,EAAmBD,EAAYL,EAAQ1b,CAAO,EAE9Cgc,EAAmBhC,EAAM,kBAAkB0B,CAAM,EAC/CA,EAAO,SAAU,EACjB,IAAID,GAAqBC,EAAQ1b,CAAO,EAAE,SAAS4b,CAAO,EAG1DI,EAAkB,CACpB,MAAMC,EAAgBH,EAAI,QAAQ,GAAG,EAEjCG,IAAkB,KACpBH,EAAMA,EAAI,MAAM,EAAGG,CAAa,GAElCH,IAAQA,EAAI,QAAQ,GAAG,IAAM,GAAK,IAAM,KAAOE,CAChD,CAED,OAAOF,CACT,CChEA,MAAMI,EAAmB,CACvB,aAAc,CACZ,KAAK,SAAW,EACjB,CAUD,IAAIC,EAAWC,EAAUpc,EAAS,CAChC,YAAK,SAAS,KAAK,CACjB,UAAAmc,EACA,SAAAC,EACA,YAAapc,EAAUA,EAAQ,YAAc,GAC7C,QAASA,EAAUA,EAAQ,QAAU,IAC3C,CAAK,EACM,KAAK,SAAS,OAAS,CAC/B,CASD,MAAMqc,EAAI,CACJ,KAAK,SAASA,CAAE,IAClB,KAAK,SAASA,CAAE,EAAI,KAEvB,CAOD,OAAQ,CACF,KAAK,WACP,KAAK,SAAW,GAEnB,CAYD,QAAQ5b,EAAI,CACVuZ,EAAM,QAAQ,KAAK,SAAU,SAAwBsC,EAAG,CAClDA,IAAM,MACR7b,EAAG6b,CAAC,CAEZ,CAAK,CACF,CACH,CAEA,MAAAC,GAAeL,GCpEAM,GAAA,CACb,kBAAmB,GACnB,kBAAmB,GACnB,oBAAqB,EACvB,ECHAC,GAAe,OAAO,gBAAoB,IAAc,gBAAkBhB,GCD1EiB,GAAe,OAAO,SAAa,IAAc,SAAW,KCA5DC,GAAe,OAAO,KAAS,IAAc,KAAO,KCErCC,GAAA,CACb,UAAW,GACX,QAAS,CACX,gBAAIC,GACJ,SAAIC,GACJ,KAAIC,EACD,EACD,UAAW,CAAC,OAAQ,QAAS,OAAQ,OAAQ,MAAO,MAAM,CAC5D,ECZMC,GAAgB,OAAO,OAAW,KAAe,OAAO,SAAa,IAErEC,GAAa,OAAO,WAAc,UAAY,WAAa,OAmB3DC,GAAwBF,KAC3B,CAACC,IAAc,CAAC,cAAe,eAAgB,IAAI,EAAE,QAAQA,GAAW,OAAO,EAAI,GAWhFE,IAAkC,IAEpC,OAAO,kBAAsB,KAE7B,gBAAgB,mBAChB,OAAO,KAAK,eAAkB,cAI5BC,GAASJ,IAAiB,OAAO,SAAS,MAAQ,oNCvCzCK,EAAA,CACb,GAAGrD,GACH,GAAGqD,EACL,ECAe,SAASC,GAAiB/D,EAAMvZ,EAAS,CACtD,OAAO2a,GAAWpB,EAAM,IAAI8D,EAAS,QAAQ,gBAAmB,OAAO,OAAO,CAC5E,QAAS,SAASjX,EAAOX,EAAKC,EAAM6X,EAAS,CAC3C,OAAIF,EAAS,QAAUrD,EAAM,SAAS5T,CAAK,GACzC,KAAK,OAAOX,EAAKW,EAAM,SAAS,QAAQ,CAAC,EAClC,IAGFmX,EAAQ,eAAe,MAAM,KAAM,SAAS,CACpD,CACL,EAAKvd,CAAO,CAAC,CACb,CCNA,SAASwd,GAActd,EAAM,CAK3B,OAAO8Z,EAAM,SAAS,gBAAiB9Z,CAAI,EAAE,IAAIlE,GACxCA,EAAM,CAAC,IAAM,KAAO,GAAKA,EAAM,CAAC,GAAKA,EAAM,CAAC,CACpD,CACH,CASA,SAASyhB,GAAcxG,EAAK,CAC1B,MAAMlC,EAAM,CAAA,EACNG,EAAO,OAAO,KAAK+B,CAAG,EAC5B,IAAInO,EACJ,MAAMqM,EAAMD,EAAK,OACjB,IAAIzP,EACJ,IAAKqD,EAAI,EAAGA,EAAIqM,EAAKrM,IACnBrD,EAAMyP,EAAKpM,CAAC,EACZiM,EAAItP,CAAG,EAAIwR,EAAIxR,CAAG,EAEpB,OAAOsP,CACT,CASA,SAAS2I,GAAe9C,EAAU,CAChC,SAAS+C,EAAUjY,EAAMU,EAAO0S,EAAQtc,EAAO,CAC7C,IAAI0D,EAAOwF,EAAKlJ,GAAO,EAEvB,GAAI0D,IAAS,YAAa,MAAO,GAEjC,MAAM0d,EAAe,OAAO,SAAS,CAAC1d,CAAI,EACpC2d,EAASrhB,GAASkJ,EAAK,OAG7B,OAFAxF,EAAO,CAACA,GAAQ8Z,EAAM,QAAQlB,CAAM,EAAIA,EAAO,OAAS5Y,EAEpD2d,GACE7D,EAAM,WAAWlB,EAAQ5Y,CAAI,EAC/B4Y,EAAO5Y,CAAI,EAAI,CAAC4Y,EAAO5Y,CAAI,EAAGkG,CAAK,EAEnC0S,EAAO5Y,CAAI,EAAIkG,EAGV,CAACwX,KAGN,CAAC9E,EAAO5Y,CAAI,GAAK,CAAC8Z,EAAM,SAASlB,EAAO5Y,CAAI,CAAC,KAC/C4Y,EAAO5Y,CAAI,EAAI,IAGFyd,EAAUjY,EAAMU,EAAO0S,EAAO5Y,CAAI,EAAG1D,CAAK,GAE3Cwd,EAAM,QAAQlB,EAAO5Y,CAAI,CAAC,IACtC4Y,EAAO5Y,CAAI,EAAIud,GAAc3E,EAAO5Y,CAAI,CAAC,GAGpC,CAAC0d,EACT,CAED,GAAI5D,EAAM,WAAWY,CAAQ,GAAKZ,EAAM,WAAWY,EAAS,OAAO,EAAG,CACpE,MAAM7F,EAAM,CAAA,EAEZiF,OAAAA,EAAM,aAAaY,EAAU,CAAC1a,EAAMkG,IAAU,CAC5CuX,EAAUH,GAActd,CAAI,EAAGkG,EAAO2O,EAAK,CAAC,CAClD,CAAK,EAEMA,CACR,CAED,OAAO,IACT,CCxEA,SAAS+I,GAAgBC,EAAUC,EAAQrC,EAAS,CAClD,GAAI3B,EAAM,SAAS+D,CAAQ,EACzB,GAAI,CACF,OAACC,GAAU,KAAK,OAAOD,CAAQ,EACxB/D,EAAM,KAAK+D,CAAQ,CAC3B,OAAQjd,EAAG,CACV,GAAIA,EAAE,OAAS,cACb,MAAMA,CAET,CAGH,OAAQ6a,GAAW,KAAK,WAAWoC,CAAQ,CAC7C,CAEA,MAAME,GAAW,CAEf,aAAczB,GAEd,QAAS,CAAC,MAAO,OAAQ,OAAO,EAEhC,iBAAkB,CAAC,SAA0BjD,EAAM2E,EAAS,CAC1D,MAAMC,EAAcD,EAAQ,eAAc,GAAM,GAC1CE,EAAqBD,EAAY,QAAQ,kBAAkB,EAAI,GAC/DE,EAAkBrE,EAAM,SAAST,CAAI,EAQ3C,GANI8E,GAAmBrE,EAAM,WAAWT,CAAI,IAC1CA,EAAO,IAAI,SAASA,CAAI,GAGPS,EAAM,WAAWT,CAAI,EAGtC,OAAO6E,EAAqB,KAAK,UAAUV,GAAenE,CAAI,CAAC,EAAIA,EAGrE,GAAIS,EAAM,cAAcT,CAAI,GAC1BS,EAAM,SAAST,CAAI,GACnBS,EAAM,SAAST,CAAI,GACnBS,EAAM,OAAOT,CAAI,GACjBS,EAAM,OAAOT,CAAI,GACjBS,EAAM,iBAAiBT,CAAI,EAE3B,OAAOA,EAET,GAAIS,EAAM,kBAAkBT,CAAI,EAC9B,OAAOA,EAAK,OAEd,GAAIS,EAAM,kBAAkBT,CAAI,EAC9B,OAAA2E,EAAQ,eAAe,kDAAmD,EAAK,EACxE3E,EAAK,WAGd,IAAInF,EAEJ,GAAIiK,EAAiB,CACnB,GAAIF,EAAY,QAAQ,mCAAmC,EAAI,GAC7D,OAAOb,GAAiB/D,EAAM,KAAK,cAAc,EAAE,SAAQ,EAG7D,IAAKnF,EAAa4F,EAAM,WAAWT,CAAI,IAAM4E,EAAY,QAAQ,qBAAqB,EAAI,GAAI,CAC5F,MAAMG,EAAY,KAAK,KAAO,KAAK,IAAI,SAEvC,OAAO3D,GACLvG,EAAa,CAAC,UAAWmF,CAAI,EAAIA,EACjC+E,GAAa,IAAIA,EACjB,KAAK,cACf,CACO,CACF,CAED,OAAID,GAAmBD,GACrBF,EAAQ,eAAe,mBAAoB,EAAK,EACzCJ,GAAgBvE,CAAI,GAGtBA,CACX,CAAG,EAED,kBAAmB,CAAC,SAA2BA,EAAM,CACnD,MAAMgF,EAAe,KAAK,cAAgBN,GAAS,aAC7CO,EAAoBD,GAAgBA,EAAa,kBACjDE,EAAgB,KAAK,eAAiB,OAE5C,GAAIzE,EAAM,WAAWT,CAAI,GAAKS,EAAM,iBAAiBT,CAAI,EACvD,OAAOA,EAGT,GAAIA,GAAQS,EAAM,SAAST,CAAI,IAAOiF,GAAqB,CAAC,KAAK,cAAiBC,GAAgB,CAEhG,MAAMC,EAAoB,EADAH,GAAgBA,EAAa,oBACPE,EAEhD,GAAI,CACF,OAAO,KAAK,MAAMlF,CAAI,CACvB,OAAQzY,EAAG,CACV,GAAI4d,EACF,MAAI5d,EAAE,OAAS,cACP6Y,EAAW,KAAK7Y,EAAG6Y,EAAW,iBAAkB,KAAM,KAAM,KAAK,QAAQ,EAE3E7Y,CAET,CACF,CAED,OAAOyY,CACX,CAAG,EAMD,QAAS,EAET,eAAgB,aAChB,eAAgB,eAEhB,iBAAkB,GAClB,cAAe,GAEf,IAAK,CACH,SAAU8D,EAAS,QAAQ,SAC3B,KAAMA,EAAS,QAAQ,IACxB,EAED,eAAgB,SAAwBsB,EAAQ,CAC9C,OAAOA,GAAU,KAAOA,EAAS,GAClC,EAED,QAAS,CACP,OAAQ,CACN,OAAU,oCACV,eAAgB,MACjB,CACF,CACH,EAEA3E,EAAM,QAAQ,CAAC,SAAU,MAAO,OAAQ,OAAQ,MAAO,OAAO,EAAI4E,GAAW,CAC3EX,GAAS,QAAQW,CAAM,EAAI,EAC7B,CAAC,EAED,MAAAC,GAAeZ,GC1JTa,GAAoB9E,EAAM,YAAY,CAC1C,MAAO,gBAAiB,iBAAkB,eAAgB,OAC1D,UAAW,OAAQ,OAAQ,oBAAqB,sBAChD,gBAAiB,WAAY,eAAgB,sBAC7C,UAAW,cAAe,YAC5B,CAAC,EAgBD+E,GAAeC,GAAc,CAC3B,MAAMC,EAAS,CAAA,EACf,IAAIxZ,EACA+N,EACA1K,EAEJ,OAAAkW,GAAcA,EAAW,MAAM;AAAA,CAAI,EAAE,QAAQ,SAAgBE,EAAM,CACjEpW,EAAIoW,EAAK,QAAQ,GAAG,EACpBzZ,EAAMyZ,EAAK,UAAU,EAAGpW,CAAC,EAAE,KAAI,EAAG,cAClC0K,EAAM0L,EAAK,UAAUpW,EAAI,CAAC,EAAE,OAExB,GAACrD,GAAQwZ,EAAOxZ,CAAG,GAAKqZ,GAAkBrZ,CAAG,KAI7CA,IAAQ,aACNwZ,EAAOxZ,CAAG,EACZwZ,EAAOxZ,CAAG,EAAE,KAAK+N,CAAG,EAEpByL,EAAOxZ,CAAG,EAAI,CAAC+N,CAAG,EAGpByL,EAAOxZ,CAAG,EAAIwZ,EAAOxZ,CAAG,EAAIwZ,EAAOxZ,CAAG,EAAI,KAAO+N,EAAMA,EAE7D,CAAG,EAEMyL,CACT,ECjDME,GAAa,OAAO,WAAW,EAErC,SAASC,GAAgBC,EAAQ,CAC/B,OAAOA,GAAU,OAAOA,CAAM,EAAE,KAAI,EAAG,aACzC,CAEA,SAASC,GAAelZ,EAAO,CAC7B,OAAIA,IAAU,IAASA,GAAS,KACvBA,EAGF4T,EAAM,QAAQ5T,CAAK,EAAIA,EAAM,IAAIkZ,EAAc,EAAI,OAAOlZ,CAAK,CACxE,CAEA,SAASmZ,GAAYjf,EAAK,CACxB,MAAMkf,EAAS,OAAO,OAAO,IAAI,EAC3BC,EAAW,mCACjB,IAAIzjB,EAEJ,KAAQA,EAAQyjB,EAAS,KAAKnf,CAAG,GAC/Bkf,EAAOxjB,EAAM,CAAC,CAAC,EAAIA,EAAM,CAAC,EAG5B,OAAOwjB,CACT,CAEA,MAAME,GAAqBpf,GAAQ,iCAAiC,KAAKA,EAAI,KAAI,CAAE,EAEnF,SAASqf,GAAiBnK,EAASpP,EAAOiZ,EAAQ5I,EAAQmJ,EAAoB,CAC5E,GAAI5F,EAAM,WAAWvD,CAAM,EACzB,OAAOA,EAAO,KAAK,KAAMrQ,EAAOiZ,CAAM,EAOxC,GAJIO,IACFxZ,EAAQiZ,GAGN,EAACrF,EAAM,SAAS5T,CAAK,EAEzB,IAAI4T,EAAM,SAASvD,CAAM,EACvB,OAAOrQ,EAAM,QAAQqQ,CAAM,IAAM,GAGnC,GAAIuD,EAAM,SAASvD,CAAM,EACvB,OAAOA,EAAO,KAAKrQ,CAAK,EAE5B,CAEA,SAASyZ,GAAaR,EAAQ,CAC5B,OAAOA,EAAO,KAAM,EACjB,YAAW,EAAG,QAAQ,kBAAmB,CAACS,EAAGC,EAAMzf,IAC3Cyf,EAAK,YAAa,EAAGzf,CAC7B,CACL,CAEA,SAAS0f,GAAejL,EAAKsK,EAAQ,CACnC,MAAMY,EAAejG,EAAM,YAAY,IAAMqF,CAAM,EAEnD,CAAC,MAAO,MAAO,KAAK,EAAE,QAAQa,GAAc,CAC1C,OAAO,eAAenL,EAAKmL,EAAaD,EAAc,CACpD,MAAO,SAASE,EAAMC,EAAMC,EAAM,CAChC,OAAO,KAAKH,CAAU,EAAE,KAAK,KAAMb,EAAQc,EAAMC,EAAMC,CAAI,CAC5D,EACD,aAAc,EACpB,CAAK,CACL,CAAG,CACH,CAEA,MAAMC,EAAa,CACjB,YAAYpC,EAAS,CACnBA,GAAW,KAAK,IAAIA,CAAO,CAC5B,CAED,IAAImB,EAAQkB,EAAgBC,EAAS,CACnC,MAAMC,EAAO,KAEb,SAASC,EAAUC,EAAQC,EAASC,EAAU,CAC5C,MAAMC,EAAU1B,GAAgBwB,CAAO,EAEvC,GAAI,CAACE,EACH,MAAM,IAAI,MAAM,wCAAwC,EAG1D,MAAMrb,EAAMuU,EAAM,QAAQyG,EAAMK,CAAO,GAEpC,CAACrb,GAAOgb,EAAKhb,CAAG,IAAM,QAAaob,IAAa,IAASA,IAAa,QAAaJ,EAAKhb,CAAG,IAAM,MAClGgb,EAAKhb,GAAOmb,CAAO,EAAItB,GAAeqB,CAAM,EAE/C,CAED,MAAMI,EAAa,CAAC7C,EAAS2C,IAC3B7G,EAAM,QAAQkE,EAAS,CAACyC,EAAQC,IAAYF,EAAUC,EAAQC,EAASC,CAAQ,CAAC,EAElF,GAAI7G,EAAM,cAAcqF,CAAM,GAAKA,aAAkB,KAAK,YACxD0B,EAAW1B,EAAQkB,CAAc,UACzBvG,EAAM,SAASqF,CAAM,IAAMA,EAASA,EAAO,SAAW,CAACK,GAAkBL,CAAM,EACvF0B,EAAWhC,GAAaM,CAAM,EAAGkB,CAAc,UACtCvG,EAAM,SAASqF,CAAM,GAAKrF,EAAM,WAAWqF,CAAM,EAAG,CAC7D,IAAItK,EAAM,CAAA,EAAIiM,EAAMvb,EACpB,UAAWwb,KAAS5B,EAAQ,CAC1B,GAAI,CAACrF,EAAM,QAAQiH,CAAK,EACtB,MAAM,UAAU,8CAA8C,EAGhElM,EAAItP,EAAMwb,EAAM,CAAC,CAAC,GAAKD,EAAOjM,EAAItP,CAAG,GAClCuU,EAAM,QAAQgH,CAAI,EAAI,CAAC,GAAGA,EAAMC,EAAM,CAAC,CAAC,EAAI,CAACD,EAAMC,EAAM,CAAC,CAAC,EAAKA,EAAM,CAAC,CAC3E,CAEDF,EAAWhM,EAAKwL,CAAc,CACpC,MACMlB,GAAU,MAAQqB,EAAUH,EAAgBlB,EAAQmB,CAAO,EAG7D,OAAO,IACR,CAED,IAAInB,EAAQrB,EAAQ,CAGlB,GAFAqB,EAASD,GAAgBC,CAAM,EAE3BA,EAAQ,CACV,MAAM5Z,EAAMuU,EAAM,QAAQ,KAAMqF,CAAM,EAEtC,GAAI5Z,EAAK,CACP,MAAMW,EAAQ,KAAKX,CAAG,EAEtB,GAAI,CAACuY,EACH,OAAO5X,EAGT,GAAI4X,IAAW,GACb,OAAOuB,GAAYnZ,CAAK,EAG1B,GAAI4T,EAAM,WAAWgE,CAAM,EACzB,OAAOA,EAAO,KAAK,KAAM5X,EAAOX,CAAG,EAGrC,GAAIuU,EAAM,SAASgE,CAAM,EACvB,OAAOA,EAAO,KAAK5X,CAAK,EAG1B,MAAM,IAAI,UAAU,wCAAwC,CAC7D,CACF,CACF,CAED,IAAIiZ,EAAQ6B,EAAS,CAGnB,GAFA7B,EAASD,GAAgBC,CAAM,EAE3BA,EAAQ,CACV,MAAM5Z,EAAMuU,EAAM,QAAQ,KAAMqF,CAAM,EAEtC,MAAO,CAAC,EAAE5Z,GAAO,KAAKA,CAAG,IAAM,SAAc,CAACyb,GAAWvB,GAAiB,KAAM,KAAKla,CAAG,EAAGA,EAAKyb,CAAO,GACxG,CAED,MAAO,EACR,CAED,OAAO7B,EAAQ6B,EAAS,CACtB,MAAMT,EAAO,KACb,IAAIU,EAAU,GAEd,SAASC,EAAaR,EAAS,CAG7B,GAFAA,EAAUxB,GAAgBwB,CAAO,EAE7BA,EAAS,CACX,MAAMnb,EAAMuU,EAAM,QAAQyG,EAAMG,CAAO,EAEnCnb,IAAQ,CAACyb,GAAWvB,GAAiBc,EAAMA,EAAKhb,CAAG,EAAGA,EAAKyb,CAAO,KACpE,OAAOT,EAAKhb,CAAG,EAEf0b,EAAU,GAEb,CACF,CAED,OAAInH,EAAM,QAAQqF,CAAM,EACtBA,EAAO,QAAQ+B,CAAY,EAE3BA,EAAa/B,CAAM,EAGd8B,CACR,CAED,MAAMD,EAAS,CACb,MAAMhM,EAAO,OAAO,KAAK,IAAI,EAC7B,IAAIpM,EAAIoM,EAAK,OACTiM,EAAU,GAEd,KAAOrY,KAAK,CACV,MAAMrD,EAAMyP,EAAKpM,CAAC,GACf,CAACoY,GAAWvB,GAAiB,KAAM,KAAKla,CAAG,EAAGA,EAAKyb,EAAS,EAAI,KACjE,OAAO,KAAKzb,CAAG,EACf0b,EAAU,GAEb,CAED,OAAOA,CACR,CAED,UAAUE,EAAQ,CAChB,MAAMZ,EAAO,KACPvC,EAAU,CAAA,EAEhBlE,OAAAA,EAAM,QAAQ,KAAM,CAAC5T,EAAOiZ,IAAW,CACrC,MAAM5Z,EAAMuU,EAAM,QAAQkE,EAASmB,CAAM,EAEzC,GAAI5Z,EAAK,CACPgb,EAAKhb,CAAG,EAAI6Z,GAAelZ,CAAK,EAChC,OAAOqa,EAAKpB,CAAM,EAClB,MACD,CAED,MAAMiC,EAAaD,EAASxB,GAAaR,CAAM,EAAI,OAAOA,CAAM,EAAE,OAE9DiC,IAAejC,GACjB,OAAOoB,EAAKpB,CAAM,EAGpBoB,EAAKa,CAAU,EAAIhC,GAAelZ,CAAK,EAEvC8X,EAAQoD,CAAU,EAAI,EAC5B,CAAK,EAEM,IACR,CAED,UAAUC,EAAS,CACjB,OAAO,KAAK,YAAY,OAAO,KAAM,GAAGA,CAAO,CAChD,CAED,OAAOC,EAAW,CAChB,MAAMzM,EAAM,OAAO,OAAO,IAAI,EAE9BiF,OAAAA,EAAM,QAAQ,KAAM,CAAC5T,EAAOiZ,IAAW,CACrCjZ,GAAS,MAAQA,IAAU,KAAU2O,EAAIsK,CAAM,EAAImC,GAAaxH,EAAM,QAAQ5T,CAAK,EAAIA,EAAM,KAAK,IAAI,EAAIA,EAChH,CAAK,EAEM2O,CACR,CAED,CAAC,OAAO,QAAQ,GAAI,CAClB,OAAO,OAAO,QAAQ,KAAK,OAAQ,CAAA,EAAE,OAAO,QAAQ,GACrD,CAED,UAAW,CACT,OAAO,OAAO,QAAQ,KAAK,OAAQ,CAAA,EAAE,IAAI,CAAC,CAACsK,EAAQjZ,CAAK,IAAMiZ,EAAS,KAAOjZ,CAAK,EAAE,KAAK;AAAA,CAAI,CAC/F,CAED,cAAe,CACb,OAAO,KAAK,IAAI,YAAY,GAAK,CAAA,CAClC,CAED,IAAK,OAAO,WAAW,GAAI,CACzB,MAAO,cACR,CAED,OAAO,KAAK6M,EAAO,CACjB,OAAOA,aAAiB,KAAOA,EAAQ,IAAI,KAAKA,CAAK,CACtD,CAED,OAAO,OAAOwO,KAAUF,EAAS,CAC/B,MAAMG,EAAW,IAAI,KAAKD,CAAK,EAE/B,OAAAF,EAAQ,QAASzI,GAAW4I,EAAS,IAAI5I,CAAM,CAAC,EAEzC4I,CACR,CAED,OAAO,SAASrC,EAAQ,CAKtB,MAAMsC,GAJY,KAAKxC,EAAU,EAAK,KAAKA,EAAU,EAAI,CACvD,UAAW,CAAE,CACnB,GAEgC,UACtBnL,EAAY,KAAK,UAEvB,SAAS4N,EAAehB,EAAS,CAC/B,MAAME,EAAU1B,GAAgBwB,CAAO,EAElCe,EAAUb,CAAO,IACpBd,GAAehM,EAAW4M,CAAO,EACjCe,EAAUb,CAAO,EAAI,GAExB,CAED9G,OAAAA,EAAM,QAAQqF,CAAM,EAAIA,EAAO,QAAQuC,CAAc,EAAIA,EAAevC,CAAM,EAEvE,IACR,CACH,CAEAiB,GAAa,SAAS,CAAC,eAAgB,iBAAkB,SAAU,kBAAmB,aAAc,eAAe,CAAC,EAGpHtG,EAAM,kBAAkBsG,GAAa,UAAW,CAAC,CAAC,MAAAla,CAAK,EAAGX,IAAQ,CAChE,IAAIoc,EAASpc,EAAI,CAAC,EAAE,YAAW,EAAKA,EAAI,MAAM,CAAC,EAC/C,MAAO,CACL,IAAK,IAAMW,EACX,IAAI0b,EAAa,CACf,KAAKD,CAAM,EAAIC,CAChB,CACF,CACH,CAAC,EAED9H,EAAM,cAAcsG,EAAY,EAEhC,MAAAyB,EAAezB,GC3SA,SAAS0B,GAAcC,EAAKlI,EAAU,CACnD,MAAM7Y,EAAS,MAAQ+c,GACjBzI,EAAUuE,GAAY7Y,EACtBgd,EAAUoC,EAAa,KAAK9K,EAAQ,OAAO,EACjD,IAAI+D,EAAO/D,EAAQ,KAEnBwE,OAAAA,EAAM,QAAQiI,EAAK,SAAmBxhB,EAAI,CACxC8Y,EAAO9Y,EAAG,KAAKS,EAAQqY,EAAM2E,EAAQ,UAAS,EAAInE,EAAWA,EAAS,OAAS,MAAS,CAC5F,CAAG,EAEDmE,EAAQ,UAAS,EAEV3E,CACT,CCzBe,SAAS2I,GAAS9b,EAAO,CACtC,MAAO,CAAC,EAAEA,GAASA,EAAM,WAC3B,CCUA,SAAS+b,GAAcvI,EAAS1Y,EAAQ4Y,EAAS,CAE/CH,EAAW,KAAK,KAAMC,GAAkB,WAAsBD,EAAW,aAAczY,EAAQ4Y,CAAO,EACtG,KAAK,KAAO,eACd,CAEAE,EAAM,SAASmI,GAAexI,EAAY,CACxC,WAAY,EACd,CAAC,ECTc,SAASyI,GAAOC,EAASC,EAAQvI,EAAU,CACxD,MAAMwI,EAAiBxI,EAAS,OAAO,eACnC,CAACA,EAAS,QAAU,CAACwI,GAAkBA,EAAexI,EAAS,MAAM,EACvEsI,EAAQtI,CAAQ,EAEhBuI,EAAO,IAAI3I,EACT,mCAAqCI,EAAS,OAC9C,CAACJ,EAAW,gBAAiBA,EAAW,gBAAgB,EAAE,KAAK,MAAMI,EAAS,OAAS,GAAG,EAAI,CAAC,EAC/FA,EAAS,OACTA,EAAS,QACTA,CACN,CAAK,CAEL,CCxBe,SAASyI,GAAc1G,EAAK,CACzC,MAAM9f,EAAQ,4BAA4B,KAAK8f,CAAG,EAClD,OAAO9f,GAASA,EAAM,CAAC,GAAK,EAC9B,CCGA,SAASymB,GAAYC,EAAcC,EAAK,CACtCD,EAAeA,GAAgB,GAC/B,MAAME,EAAQ,IAAI,MAAMF,CAAY,EAC9BG,EAAa,IAAI,MAAMH,CAAY,EACzC,IAAII,EAAO,EACPC,EAAO,EACPC,EAEJ,OAAAL,EAAMA,IAAQ,OAAYA,EAAM,IAEzB,SAAcM,EAAa,CAChC,MAAMC,EAAM,KAAK,MAEXC,EAAYN,EAAWE,CAAI,EAE5BC,IACHA,EAAgBE,GAGlBN,EAAME,CAAI,EAAIG,EACdJ,EAAWC,CAAI,EAAII,EAEnB,IAAIpa,EAAIia,EACJK,EAAa,EAEjB,KAAOta,IAAMga,GACXM,GAAcR,EAAM9Z,GAAG,EACvBA,EAAIA,EAAI4Z,EASV,GANAI,GAAQA,EAAO,GAAKJ,EAEhBI,IAASC,IACXA,GAAQA,EAAO,GAAKL,GAGlBQ,EAAMF,EAAgBL,EACxB,OAGF,MAAMU,EAASF,GAAaD,EAAMC,EAElC,OAAOE,EAAS,KAAK,MAAMD,EAAa,IAAOC,CAAM,EAAI,MAC7D,CACA,CC9CA,SAASC,GAAS7iB,EAAI8iB,EAAM,CAC1B,IAAIC,EAAY,EACZC,EAAY,IAAOF,EACnBG,EACAC,EAEJ,MAAMC,EAAS,CAACjiB,EAAMuhB,EAAM,KAAK,IAAG,IAAO,CACzCM,EAAYN,EACZQ,EAAW,KACPC,IACF,aAAaA,CAAK,EAClBA,EAAQ,MAEVljB,EAAG,MAAM,KAAMkB,CAAI,CACpB,EAoBD,MAAO,CAlBW,IAAIA,IAAS,CAC7B,MAAMuhB,EAAM,KAAK,MACXG,EAASH,EAAMM,EAChBH,GAAUI,EACbG,EAAOjiB,EAAMuhB,CAAG,GAEhBQ,EAAW/hB,EACNgiB,IACHA,EAAQ,WAAW,IAAM,CACvBA,EAAQ,KACRC,EAAOF,CAAQ,CACzB,EAAWD,EAAYJ,CAAM,GAG1B,EAEa,IAAMK,GAAYE,EAAOF,CAAQ,CAEvB,CAC1B,CCrCO,MAAMG,GAAuB,CAAC7kB,EAAU8kB,EAAkBP,EAAO,IAAM,CAC5E,IAAIQ,EAAgB,EACpB,MAAMC,EAAevB,GAAY,GAAI,GAAG,EAExC,OAAOa,GAASxiB,GAAK,CACnB,MAAMmjB,EAASnjB,EAAE,OACXojB,EAAQpjB,EAAE,iBAAmBA,EAAE,MAAQ,OACvCqjB,EAAgBF,EAASF,EACzBK,EAAOJ,EAAaG,CAAa,EACjCE,EAAUJ,GAAUC,EAE1BH,EAAgBE,EAEhB,MAAM1K,EAAO,CACX,OAAA0K,EACA,MAAAC,EACA,SAAUA,EAASD,EAASC,EAAS,OACrC,MAAOC,EACP,KAAMC,GAAc,OACpB,UAAWA,GAAQF,GAASG,GAAWH,EAAQD,GAAUG,EAAO,OAChE,MAAOtjB,EACP,iBAAkBojB,GAAS,KAC3B,CAACJ,EAAmB,WAAa,QAAQ,EAAG,EAClD,EAEI9kB,EAASua,CAAI,CACd,EAAEgK,CAAI,CACT,EAEae,GAAyB,CAACJ,EAAOK,IAAc,CAC1D,MAAMC,EAAmBN,GAAS,KAElC,MAAO,CAAED,GAAWM,EAAU,CAAC,EAAE,CAC/B,iBAAAC,EACA,MAAAN,EACA,OAAAD,CACJ,CAAG,EAAGM,EAAU,CAAC,CAAC,CAClB,EAEaE,GAAkBhkB,GAAO,IAAIkB,IAASqY,EAAM,KAAK,IAAMvZ,EAAG,GAAGkB,CAAI,CAAC,ECzC/E+iB,GAAerH,EAAS,uBAAyB,CAACD,EAAQuH,IAAY7I,IACpEA,EAAM,IAAI,IAAIA,EAAKuB,EAAS,MAAM,EAGhCD,EAAO,WAAatB,EAAI,UACxBsB,EAAO,OAAStB,EAAI,OACnB6I,GAAUvH,EAAO,OAAStB,EAAI,QAGjC,IAAI,IAAIuB,EAAS,MAAM,EACvBA,EAAS,WAAa,kBAAkB,KAAKA,EAAS,UAAU,SAAS,CAC3E,EAAI,IAAM,GCVKuH,GAAAvH,EAAS,sBAGtB,CACE,MAAMnd,EAAMkG,EAAOye,EAASnf,EAAMof,EAAQC,EAAQ,CAChD,MAAMC,EAAS,CAAC9kB,EAAO,IAAM,mBAAmBkG,CAAK,CAAC,EAEtD4T,EAAM,SAAS6K,CAAO,GAAKG,EAAO,KAAK,WAAa,IAAI,KAAKH,CAAO,EAAE,YAAa,CAAA,EAEnF7K,EAAM,SAAStU,CAAI,GAAKsf,EAAO,KAAK,QAAUtf,CAAI,EAElDsU,EAAM,SAAS8K,CAAM,GAAKE,EAAO,KAAK,UAAYF,CAAM,EAExDC,IAAW,IAAQC,EAAO,KAAK,QAAQ,EAEvC,SAAS,OAASA,EAAO,KAAK,IAAI,CACnC,EAED,KAAK9kB,EAAM,CACT,MAAMlE,EAAQ,SAAS,OAAO,MAAM,IAAI,OAAO,aAAekE,EAAO,WAAW,CAAC,EACjF,OAAQlE,EAAQ,mBAAmBA,EAAM,CAAC,CAAC,EAAI,IAChD,EAED,OAAOkE,EAAM,CACX,KAAK,MAAMA,EAAM,GAAI,KAAK,IAAG,EAAK,KAAQ,CAC3C,CACF,EAKD,CACE,OAAQ,CAAE,EACV,MAAO,CACL,OAAO,IACR,EACD,QAAS,CAAE,CACZ,EC/BY,SAAS+kB,GAAcnJ,EAAK,CAIzC,MAAO,8BAA8B,KAAKA,CAAG,CAC/C,CCJe,SAASoJ,GAAYC,EAASC,EAAa,CACxD,OAAOA,EACHD,EAAQ,QAAQ,SAAU,EAAE,EAAI,IAAMC,EAAY,QAAQ,OAAQ,EAAE,EACpED,CACN,CCCe,SAASE,GAAcF,EAASG,EAAcC,EAAmB,CAC9E,IAAIC,EAAgB,CAACP,GAAcK,CAAY,EAC/C,OAAIH,IAAYK,GAAiBD,GAAqB,IAC7CL,GAAYC,EAASG,CAAY,EAEnCA,CACT,CChBA,MAAMG,GAAmBxS,GAAUA,aAAiBqN,EAAe,CAAE,GAAGrN,CAAO,EAAGA,EAWnE,SAASyS,GAAYC,EAASC,EAAS,CAEpDA,EAAUA,GAAW,GACrB,MAAM1kB,EAAS,CAAA,EAEf,SAAS2kB,EAAe/M,EAAQD,EAAQjc,EAAM8Y,EAAU,CACtD,OAAIsE,EAAM,cAAclB,CAAM,GAAKkB,EAAM,cAAcnB,CAAM,EACpDmB,EAAM,MAAM,KAAK,CAAC,SAAAtE,CAAQ,EAAGoD,EAAQD,CAAM,EACzCmB,EAAM,cAAcnB,CAAM,EAC5BmB,EAAM,MAAM,CAAE,EAAEnB,CAAM,EACpBmB,EAAM,QAAQnB,CAAM,EACtBA,EAAO,QAETA,CACR,CAGD,SAASiN,EAAoBhQ,EAAGC,EAAGnZ,EAAO8Y,EAAU,CAClD,GAAKsE,EAAM,YAAYjE,CAAC,GAEjB,GAAI,CAACiE,EAAM,YAAYlE,CAAC,EAC7B,OAAO+P,EAAe,OAAW/P,EAAGlZ,EAAO8Y,CAAQ,MAFnD,QAAOmQ,EAAe/P,EAAGC,EAAGnZ,EAAO8Y,CAAQ,CAI9C,CAGD,SAASqQ,EAAiBjQ,EAAGC,EAAG,CAC9B,GAAI,CAACiE,EAAM,YAAYjE,CAAC,EACtB,OAAO8P,EAAe,OAAW9P,CAAC,CAErC,CAGD,SAASiQ,EAAiBlQ,EAAGC,EAAG,CAC9B,GAAKiE,EAAM,YAAYjE,CAAC,GAEjB,GAAI,CAACiE,EAAM,YAAYlE,CAAC,EAC7B,OAAO+P,EAAe,OAAW/P,CAAC,MAFlC,QAAO+P,EAAe,OAAW9P,CAAC,CAIrC,CAGD,SAASkQ,EAAgBnQ,EAAGC,EAAGnZ,EAAM,CACnC,GAAIA,KAAQgpB,EACV,OAAOC,EAAe/P,EAAGC,CAAC,EACrB,GAAInZ,KAAQ+oB,EACjB,OAAOE,EAAe,OAAW/P,CAAC,CAErC,CAED,MAAMoQ,EAAW,CACf,IAAKH,EACL,OAAQA,EACR,KAAMA,EACN,QAASC,EACT,iBAAkBA,EAClB,kBAAmBA,EACnB,iBAAkBA,EAClB,QAASA,EACT,eAAgBA,EAChB,gBAAiBA,EACjB,cAAeA,EACf,QAASA,EACT,aAAcA,EACd,eAAgBA,EAChB,eAAgBA,EAChB,iBAAkBA,EAClB,mBAAoBA,EACpB,WAAYA,EACZ,iBAAkBA,EAClB,cAAeA,EACf,eAAgBA,EAChB,UAAWA,EACX,UAAWA,EACX,WAAYA,EACZ,YAAaA,EACb,WAAYA,EACZ,iBAAkBA,EAClB,eAAgBC,EAChB,QAAS,CAACnQ,EAAGC,EAAInZ,IAASkpB,EAAoBL,GAAgB3P,CAAC,EAAG2P,GAAgB1P,CAAC,EAAEnZ,EAAM,EAAI,CACnG,EAEEod,OAAAA,EAAM,QAAQ,OAAO,KAAK,OAAO,OAAO,GAAI2L,EAASC,CAAO,CAAC,EAAG,SAA4BhpB,EAAM,CAChG,MAAM6Y,EAAQyQ,EAAStpB,CAAI,GAAKkpB,EAC1BK,EAAc1Q,EAAMkQ,EAAQ/oB,CAAI,EAAGgpB,EAAQhpB,CAAI,EAAGA,CAAI,EAC3Dod,EAAM,YAAYmM,CAAW,GAAK1Q,IAAUwQ,IAAqB/kB,EAAOtE,CAAI,EAAIupB,EACrF,CAAG,EAEMjlB,CACT,CChGA,MAAeklB,GAACllB,GAAW,CACzB,MAAMmlB,EAAYX,GAAY,CAAE,EAAExkB,CAAM,EAExC,GAAI,CAAC,KAAAqY,EAAM,cAAA+M,EAAe,eAAAC,EAAgB,eAAAC,EAAgB,QAAAtI,EAAS,KAAAuI,CAAI,EAAIJ,EAE3EA,EAAU,QAAUnI,EAAUoC,EAAa,KAAKpC,CAAO,EAEvDmI,EAAU,IAAMxK,GAASwJ,GAAcgB,EAAU,QAASA,EAAU,IAAKA,EAAU,iBAAiB,EAAGnlB,EAAO,OAAQA,EAAO,gBAAgB,EAGzIulB,GACFvI,EAAQ,IAAI,gBAAiB,SAC3B,MAAMuI,EAAK,UAAY,IAAM,KAAOA,EAAK,SAAW,SAAS,mBAAmBA,EAAK,QAAQ,CAAC,EAAI,GAAG,CAC3G,EAGE,IAAItI,EAEJ,GAAInE,EAAM,WAAWT,CAAI,GACvB,GAAI8D,EAAS,uBAAyBA,EAAS,+BAC7Ca,EAAQ,eAAe,MAAS,WACtBC,EAAcD,EAAQ,eAAc,KAAQ,GAAO,CAE7D,KAAM,CAAC/K,EAAM,GAAGqM,CAAM,EAAIrB,EAAcA,EAAY,MAAM,GAAG,EAAE,IAAI9E,GAASA,EAAM,KAAI,CAAE,EAAE,OAAO,OAAO,EAAI,GAC5G6E,EAAQ,eAAe,CAAC/K,GAAQ,sBAAuB,GAAGqM,CAAM,EAAE,KAAK,IAAI,CAAC,CAC7E,EAOH,GAAInC,EAAS,wBACXiJ,GAAiBtM,EAAM,WAAWsM,CAAa,IAAMA,EAAgBA,EAAcD,CAAS,GAExFC,GAAkBA,IAAkB,IAAS5B,GAAgB2B,EAAU,GAAG,GAAI,CAEhF,MAAMK,EAAYH,GAAkBC,GAAkB5B,GAAQ,KAAK4B,CAAc,EAE7EE,GACFxI,EAAQ,IAAIqI,EAAgBG,CAAS,CAExC,CAGH,OAAOL,CACT,EC5CMM,GAAwB,OAAO,eAAmB,IAExDC,GAAeD,IAAyB,SAAUzlB,EAAQ,CACxD,OAAO,IAAI,QAAQ,SAA4BmhB,EAASC,EAAQ,CAC9D,MAAMuE,EAAUT,GAAcllB,CAAM,EACpC,IAAI4lB,EAAcD,EAAQ,KAC1B,MAAME,EAAiBzG,EAAa,KAAKuG,EAAQ,OAAO,EAAE,YAC1D,GAAI,CAAC,aAAAG,EAAc,iBAAAC,EAAkB,mBAAAC,CAAkB,EAAIL,EACvDM,EACAC,EAAiBC,EACjBC,EAAaC,EAEjB,SAASC,GAAO,CACdF,GAAeA,EAAW,EAC1BC,GAAiBA,EAAa,EAE9BV,EAAQ,aAAeA,EAAQ,YAAY,YAAYM,CAAU,EAEjEN,EAAQ,QAAUA,EAAQ,OAAO,oBAAoB,QAASM,CAAU,CACzE,CAED,IAAIrN,EAAU,IAAI,eAElBA,EAAQ,KAAK+M,EAAQ,OAAO,YAAW,EAAIA,EAAQ,IAAK,EAAI,EAG5D/M,EAAQ,QAAU+M,EAAQ,QAE1B,SAASY,GAAY,CACnB,GAAI,CAAC3N,EACH,OAGF,MAAM4N,EAAkBpH,EAAa,KACnC,0BAA2BxG,GAAWA,EAAQ,sBAAuB,CAC7E,EAGYC,EAAW,CACf,KAHmB,CAACiN,GAAgBA,IAAiB,QAAUA,IAAiB,OAChFlN,EAAQ,aAAeA,EAAQ,SAG/B,OAAQA,EAAQ,OAChB,WAAYA,EAAQ,WACpB,QAAS4N,EACT,OAAAxmB,EACA,QAAA4Y,CACR,EAEMsI,GAAO,SAAkBhc,EAAO,CAC9Bic,EAAQjc,CAAK,EACbohB,GACR,EAAS,SAAiBG,EAAK,CACvBrF,EAAOqF,CAAG,EACVH,GACD,EAAEzN,CAAQ,EAGXD,EAAU,IACX,CAEG,cAAeA,EAEjBA,EAAQ,UAAY2N,EAGpB3N,EAAQ,mBAAqB,UAAsB,CAC7C,CAACA,GAAWA,EAAQ,aAAe,GAQnCA,EAAQ,SAAW,GAAK,EAAEA,EAAQ,aAAeA,EAAQ,YAAY,QAAQ,OAAO,IAAM,IAK9F,WAAW2N,CAAS,CAC5B,EAII3N,EAAQ,QAAU,UAAuB,CAClCA,IAILwI,EAAO,IAAI3I,EAAW,kBAAmBA,EAAW,aAAczY,EAAQ4Y,CAAO,CAAC,EAGlFA,EAAU,KAChB,EAGIA,EAAQ,QAAU,UAAuB,CAGvCwI,EAAO,IAAI3I,EAAW,gBAAiBA,EAAW,YAAazY,EAAQ4Y,CAAO,CAAC,EAG/EA,EAAU,IAChB,EAGIA,EAAQ,UAAY,UAAyB,CAC3C,IAAI8N,EAAsBf,EAAQ,QAAU,cAAgBA,EAAQ,QAAU,cAAgB,mBAC9F,MAAMtI,EAAesI,EAAQ,cAAgBrK,GACzCqK,EAAQ,sBACVe,EAAsBf,EAAQ,qBAEhCvE,EAAO,IAAI3I,EACTiO,EACArJ,EAAa,oBAAsB5E,EAAW,UAAYA,EAAW,aACrEzY,EACA4Y,CAAO,CAAC,EAGVA,EAAU,IAChB,EAGIgN,IAAgB,QAAaC,EAAe,eAAe,IAAI,EAG3D,qBAAsBjN,GACxBE,EAAM,QAAQ+M,EAAe,OAAQ,EAAE,SAA0BvT,EAAK/N,EAAK,CACzEqU,EAAQ,iBAAiBrU,EAAK+N,CAAG,CACzC,CAAO,EAIEwG,EAAM,YAAY6M,EAAQ,eAAe,IAC5C/M,EAAQ,gBAAkB,CAAC,CAAC+M,EAAQ,iBAIlCG,GAAgBA,IAAiB,SACnClN,EAAQ,aAAe+M,EAAQ,cAI7BK,IACD,CAACG,EAAmBE,CAAa,EAAI1D,GAAqBqD,EAAoB,EAAI,EACnFpN,EAAQ,iBAAiB,WAAYuN,CAAiB,GAIpDJ,GAAoBnN,EAAQ,SAC7B,CAACsN,EAAiBE,CAAW,EAAIzD,GAAqBoD,CAAgB,EAEvEnN,EAAQ,OAAO,iBAAiB,WAAYsN,CAAe,EAE3DtN,EAAQ,OAAO,iBAAiB,UAAWwN,CAAW,IAGpDT,EAAQ,aAAeA,EAAQ,UAGjCM,EAAaU,GAAU,CAChB/N,IAGLwI,EAAO,CAACuF,GAAUA,EAAO,KAAO,IAAI1F,GAAc,KAAMjhB,EAAQ4Y,CAAO,EAAI+N,CAAM,EACjF/N,EAAQ,MAAK,EACbA,EAAU,KAClB,EAEM+M,EAAQ,aAAeA,EAAQ,YAAY,UAAUM,CAAU,EAC3DN,EAAQ,SACVA,EAAQ,OAAO,QAAUM,EAAY,EAAGN,EAAQ,OAAO,iBAAiB,QAASM,CAAU,IAI/F,MAAMW,EAAWtF,GAAcqE,EAAQ,GAAG,EAE1C,GAAIiB,GAAYzK,EAAS,UAAU,QAAQyK,CAAQ,IAAM,GAAI,CAC3DxF,EAAO,IAAI3I,EAAW,wBAA0BmO,EAAW,IAAKnO,EAAW,gBAAiBzY,CAAM,CAAC,EACnG,MACD,CAID4Y,EAAQ,KAAKgN,GAAe,IAAI,CACpC,CAAG,CACH,EChMMiB,GAAiB,CAACC,EAASlX,IAAY,CAC3C,KAAM,CAAC,OAAAmX,CAAM,EAAKD,EAAUA,EAAUA,EAAQ,OAAO,OAAO,EAAI,CAAA,EAEhE,GAAIlX,GAAWmX,EAAQ,CACrB,IAAIC,EAAa,IAAI,gBAEjBC,EAEJ,MAAMC,EAAU,SAAUC,EAAQ,CAChC,GAAI,CAACF,EAAS,CACZA,EAAU,GACVG,IACA,MAAMX,EAAMU,aAAkB,MAAQA,EAAS,KAAK,OACpDH,EAAW,MAAMP,aAAehO,EAAagO,EAAM,IAAIxF,GAAcwF,aAAe,MAAQA,EAAI,QAAUA,CAAG,CAAC,CAC/G,CACF,EAED,IAAIhE,EAAQ7S,GAAW,WAAW,IAAM,CACtC6S,EAAQ,KACRyE,EAAQ,IAAIzO,EAAW,WAAW7I,CAAO,kBAAmB6I,EAAW,SAAS,CAAC,CAClF,EAAE7I,CAAO,EAEV,MAAMwX,EAAc,IAAM,CACpBN,IACFrE,GAAS,aAAaA,CAAK,EAC3BA,EAAQ,KACRqE,EAAQ,QAAQO,GAAU,CACxBA,EAAO,YAAcA,EAAO,YAAYH,CAAO,EAAIG,EAAO,oBAAoB,QAASH,CAAO,CACxG,CAAS,EACDJ,EAAU,KAEb,EAEDA,EAAQ,QAASO,GAAWA,EAAO,iBAAiB,QAASH,CAAO,CAAC,EAErE,KAAM,CAAC,OAAAG,CAAM,EAAIL,EAEjB,OAAAK,EAAO,YAAc,IAAMvO,EAAM,KAAKsO,CAAW,EAE1CC,CACR,CACH,EAEAC,GAAeT,GC9CFU,GAAc,UAAWC,EAAOC,EAAW,CACtD,IAAIxT,EAAMuT,EAAM,WAEhB,GAAI,CAACC,GAAaxT,EAAMwT,EAAW,CACjC,MAAMD,EACN,MACD,CAED,IAAIE,EAAM,EACNC,EAEJ,KAAOD,EAAMzT,GACX0T,EAAMD,EAAMD,EACZ,MAAMD,EAAM,MAAME,EAAKC,CAAG,EAC1BD,EAAMC,CAEV,EAEaC,GAAY,gBAAiBC,EAAUJ,EAAW,CAC7D,gBAAiBD,KAASM,GAAWD,CAAQ,EAC3C,MAAON,GAAYC,EAAOC,CAAS,CAEvC,EAEMK,GAAa,gBAAiBC,EAAQ,CAC1C,GAAIA,EAAO,OAAO,aAAa,EAAG,CAChC,MAAOA,EACP,MACD,CAED,MAAMC,EAASD,EAAO,YACtB,GAAI,CACF,OAAS,CACP,KAAM,CAAC,KAAAzB,EAAM,MAAAphB,CAAK,EAAI,MAAM8iB,EAAO,KAAI,EACvC,GAAI1B,EACF,MAEF,MAAMphB,CACP,CACL,QAAY,CACR,MAAM8iB,EAAO,QACd,CACH,EAEaC,GAAc,CAACF,EAAQN,EAAWS,EAAYC,IAAa,CACtE,MAAMvW,EAAWgW,GAAUG,EAAQN,CAAS,EAE5C,IAAI/F,EAAQ,EACR4E,EACA8B,EAAaxoB,GAAM,CAChB0mB,IACHA,EAAO,GACP6B,GAAYA,EAASvoB,CAAC,EAEzB,EAED,OAAO,IAAI,eAAe,CACxB,MAAM,KAAKonB,EAAY,CACrB,GAAI,CACF,KAAM,CAAC,KAAAV,EAAM,MAAAphB,CAAK,EAAI,MAAM0M,EAAS,KAAI,EAEzC,GAAI0U,EAAM,CACT8B,IACCpB,EAAW,MAAK,EAChB,MACD,CAED,IAAI/S,EAAM/O,EAAM,WAChB,GAAIgjB,EAAY,CACd,IAAIG,EAAc3G,GAASzN,EAC3BiU,EAAWG,CAAW,CACvB,CACDrB,EAAW,QAAQ,IAAI,WAAW9hB,CAAK,CAAC,CACzC,OAAQuhB,EAAK,CACZ,MAAA2B,EAAU3B,CAAG,EACPA,CACP,CACF,EACD,OAAOU,EAAQ,CACb,OAAAiB,EAAUjB,CAAM,EACTvV,EAAS,QACjB,CACL,EAAK,CACD,cAAe,CACnB,CAAG,CACH,EC5EM0W,GAAmB,OAAO,OAAU,YAAc,OAAO,SAAY,YAAc,OAAO,UAAa,WACvGC,GAA4BD,IAAoB,OAAO,gBAAmB,WAG1EE,GAAaF,KAAqB,OAAO,aAAgB,YACzD7N,GAAarb,GAAQqb,EAAQ,OAAOrb,CAAG,GAAG,IAAI,WAAa,EAC7D,MAAOA,GAAQ,IAAI,WAAW,MAAM,IAAI,SAASA,CAAG,EAAE,aAAa,GAGjEqpB,GAAO,CAAClpB,KAAOkB,IAAS,CAC5B,GAAI,CACF,MAAO,CAAC,CAAClB,EAAG,GAAGkB,CAAI,CACpB,MAAW,CACV,MAAO,EACR,CACH,EAEMioB,GAAwBH,IAA6BE,GAAK,IAAM,CACpE,IAAIE,EAAiB,GAErB,MAAMC,EAAiB,IAAI,QAAQzM,EAAS,OAAQ,CAClD,KAAM,IAAI,eACV,OAAQ,OACR,IAAI,QAAS,CACX,OAAAwM,EAAiB,GACV,MACR,CACF,CAAA,EAAE,QAAQ,IAAI,cAAc,EAE7B,OAAOA,GAAkB,CAACC,CAC5B,CAAC,EAEKC,GAAqB,GAAK,KAE1BC,GAAyBP,IAC7BE,GAAK,IAAM3P,EAAM,iBAAiB,IAAI,SAAS,EAAE,EAAE,IAAI,CAAC,EAGpDiQ,GAAY,CAChB,OAAQD,KAA4BE,GAAQA,EAAI,KAClD,EAEAV,KAAuBU,GAAQ,CAC7B,CAAC,OAAQ,cAAe,OAAQ,WAAY,QAAQ,EAAE,QAAQ/W,GAAQ,CACpE,CAAC8W,GAAU9W,CAAI,IAAM8W,GAAU9W,CAAI,EAAI6G,EAAM,WAAWkQ,EAAI/W,CAAI,CAAC,EAAK+W,GAAQA,EAAI/W,CAAI,EAAG,EACvF,CAACjC,EAAGhQ,IAAW,CACb,MAAM,IAAIyY,EAAW,kBAAkBxG,CAAI,qBAAsBwG,EAAW,gBAAiBzY,CAAM,CAC3G,EACA,CAAG,CACH,GAAG,IAAI,QAAQ,EAEf,MAAMipB,GAAgB,MAAOC,GAAS,CACpC,GAAIA,GAAQ,KACV,MAAO,GAGT,GAAGpQ,EAAM,OAAOoQ,CAAI,EAClB,OAAOA,EAAK,KAGd,GAAGpQ,EAAM,oBAAoBoQ,CAAI,EAK/B,OAAQ,MAJS,IAAI,QAAQ/M,EAAS,OAAQ,CAC5C,OAAQ,OACR,KAAA+M,CACN,CAAK,EACsB,YAAW,GAAI,WAGxC,GAAGpQ,EAAM,kBAAkBoQ,CAAI,GAAKpQ,EAAM,cAAcoQ,CAAI,EAC1D,OAAOA,EAAK,WAOd,GAJGpQ,EAAM,kBAAkBoQ,CAAI,IAC7BA,EAAOA,EAAO,IAGbpQ,EAAM,SAASoQ,CAAI,EACpB,OAAQ,MAAMV,GAAWU,CAAI,GAAG,UAEpC,EAEMC,GAAoB,MAAOnM,EAASkM,IAAS,CACjD,MAAMnC,EAASjO,EAAM,eAAekE,EAAQ,iBAAkB,CAAA,EAE9D,OAAO+J,GAAiBkC,GAAcC,CAAI,CAC5C,EAEAE,GAAed,KAAqB,MAAOtoB,GAAW,CACpD,GAAI,CACF,IAAA4a,EACA,OAAA8C,EACA,KAAArF,EACA,OAAAgP,EACA,YAAAgC,EACA,QAAAzZ,EACA,mBAAAoW,EACA,iBAAAD,EACA,aAAAD,EACA,QAAA9I,EACA,gBAAAsM,EAAkB,cAClB,aAAAC,CACJ,EAAMrE,GAAcllB,CAAM,EAExB8lB,EAAeA,GAAgBA,EAAe,IAAI,YAAa,EAAG,OAElE,IAAI0D,EAAiB3C,GAAe,CAACQ,EAAQgC,GAAeA,EAAY,cAAa,CAAE,EAAGzZ,CAAO,EAE7FgJ,EAEJ,MAAMwO,EAAcoC,GAAkBA,EAAe,cAAgB,IAAM,CACvEA,EAAe,YAAW,CAChC,GAEE,IAAIC,EAEJ,GAAI,CACF,GACE1D,GAAoB2C,IAAyBhL,IAAW,OAASA,IAAW,SAC3E+L,EAAuB,MAAMN,GAAkBnM,EAAS3E,CAAI,KAAO,EACpE,CACA,IAAIqR,EAAW,IAAI,QAAQ9O,EAAK,CAC9B,OAAQ,OACR,KAAMvC,EACN,OAAQ,MAChB,CAAO,EAEGsR,EAMJ,GAJI7Q,EAAM,WAAWT,CAAI,IAAMsR,EAAoBD,EAAS,QAAQ,IAAI,cAAc,IACpF1M,EAAQ,eAAe2M,CAAiB,EAGtCD,EAAS,KAAM,CACjB,KAAM,CAACxB,EAAY0B,CAAK,EAAIxG,GAC1BqG,EACA9G,GAAqBY,GAAewC,CAAgB,CAAC,CAC/D,EAEQ1N,EAAO4P,GAAYyB,EAAS,KAAMb,GAAoBX,EAAY0B,CAAK,CACxE,CACF,CAEI9Q,EAAM,SAASwQ,CAAe,IACjCA,EAAkBA,EAAkB,UAAY,QAKlD,MAAMO,EAAyB,gBAAiB,QAAQ,UACxDjR,EAAU,IAAI,QAAQgC,EAAK,CACzB,GAAG2O,EACH,OAAQC,EACR,OAAQ9L,EAAO,YAAa,EAC5B,QAASV,EAAQ,UAAW,EAAC,OAAQ,EACrC,KAAM3E,EACN,OAAQ,OACR,YAAawR,EAAyBP,EAAkB,MAC9D,CAAK,EAED,IAAIzQ,EAAW,MAAM,MAAMD,EAAS2Q,CAAY,EAEhD,MAAMO,EAAmBhB,KAA2BhD,IAAiB,UAAYA,IAAiB,YAElG,GAAIgD,KAA2B9C,GAAuB8D,GAAoB1C,GAAe,CACvF,MAAMtoB,EAAU,CAAA,EAEhB,CAAC,SAAU,aAAc,SAAS,EAAE,QAAQpD,GAAQ,CAClDoD,EAAQpD,CAAI,EAAImd,EAASnd,CAAI,CACrC,CAAO,EAED,MAAMquB,EAAwBjR,EAAM,eAAeD,EAAS,QAAQ,IAAI,gBAAgB,CAAC,EAEnF,CAACqP,EAAY0B,CAAK,EAAI5D,GAAsB5C,GAChD2G,EACApH,GAAqBY,GAAeyC,CAAkB,EAAG,EAAI,CAC9D,GAAI,GAELnN,EAAW,IAAI,SACboP,GAAYpP,EAAS,KAAMgQ,GAAoBX,EAAY,IAAM,CAC/D0B,GAASA,EAAK,EACdxC,GAAeA,EAAW,CACpC,CAAS,EACDtoB,CACR,CACK,CAEDgnB,EAAeA,GAAgB,OAE/B,IAAIkE,EAAe,MAAMjB,GAAUjQ,EAAM,QAAQiQ,GAAWjD,CAAY,GAAK,MAAM,EAAEjN,EAAU7Y,CAAM,EAErG,OAAC8pB,GAAoB1C,GAAeA,IAE7B,MAAM,IAAI,QAAQ,CAACjG,EAASC,IAAW,CAC5CF,GAAOC,EAASC,EAAQ,CACtB,KAAM4I,EACN,QAAS5K,EAAa,KAAKvG,EAAS,OAAO,EAC3C,OAAQA,EAAS,OACjB,WAAYA,EAAS,WACrB,OAAA7Y,EACA,QAAA4Y,CACR,CAAO,CACP,CAAK,CACF,OAAQ6N,EAAK,CAGZ,MAFAW,GAAeA,EAAW,EAEtBX,GAAOA,EAAI,OAAS,aAAe,qBAAqB,KAAKA,EAAI,OAAO,EACpE,OAAO,OACX,IAAIhO,EAAW,gBAAiBA,EAAW,YAAazY,EAAQ4Y,CAAO,EACvE,CACE,MAAO6N,EAAI,OAASA,CACrB,CACF,EAGGhO,EAAW,KAAKgO,EAAKA,GAAOA,EAAI,KAAMzmB,EAAQ4Y,CAAO,CAC5D,CACH,GC5NMqR,GAAgB,CACpB,KAAM/Q,GACN,IAAKwM,GACL,MAAO0D,EACT,EAEAtQ,EAAM,QAAQmR,GAAe,CAAC1qB,EAAI2F,IAAU,CAC1C,GAAI3F,EAAI,CACN,GAAI,CACF,OAAO,eAAeA,EAAI,OAAQ,CAAC,MAAA2F,CAAK,CAAC,CAC1C,MAAW,CAEX,CACD,OAAO,eAAe3F,EAAI,cAAe,CAAC,MAAA2F,CAAK,CAAC,CACjD,CACH,CAAC,EAED,MAAMglB,GAAgB/C,GAAW,KAAKA,CAAM,GAEtCgD,GAAoBC,GAAYtR,EAAM,WAAWsR,CAAO,GAAKA,IAAY,MAAQA,IAAY,GAEpFC,GAAA,CACb,WAAaA,GAAa,CACxBA,EAAWvR,EAAM,QAAQuR,CAAQ,EAAIA,EAAW,CAACA,CAAQ,EAEzD,KAAM,CAAC,OAAAtD,CAAM,EAAIsD,EACjB,IAAIC,EACAF,EAEJ,MAAMG,EAAkB,CAAA,EAExB,QAAS3iB,EAAI,EAAGA,EAAImf,EAAQnf,IAAK,CAC/B0iB,EAAgBD,EAASziB,CAAC,EAC1B,IAAIuT,EAIJ,GAFAiP,EAAUE,EAEN,CAACH,GAAiBG,CAAa,IACjCF,EAAUH,IAAe9O,EAAK,OAAOmP,CAAa,GAAG,YAAW,CAAE,EAE9DF,IAAY,QACd,MAAM,IAAI3R,EAAW,oBAAoB0C,CAAE,GAAG,EAIlD,GAAIiP,EACF,MAGFG,EAAgBpP,GAAM,IAAMvT,CAAC,EAAIwiB,CAClC,CAED,GAAI,CAACA,EAAS,CAEZ,MAAMI,EAAU,OAAO,QAAQD,CAAe,EAC3C,IAAI,CAAC,CAACpP,EAAI5d,CAAK,IAAM,WAAW4d,CAAE,KAChC5d,IAAU,GAAQ,sCAAwC,gCACrE,EAEM,IAAIktB,EAAI1D,EACLyD,EAAQ,OAAS,EAAI;AAAA,EAAcA,EAAQ,IAAIN,EAAY,EAAE,KAAK;AAAA,CAAI,EAAI,IAAMA,GAAaM,EAAQ,CAAC,CAAC,EACxG,0BAEF,MAAM,IAAI/R,EACR,wDAA0DgS,EAC1D,iBACR,CACK,CAED,OAAOL,CACR,EACD,SAAUH,EACZ,EC9DA,SAASS,GAA6B1qB,EAAQ,CAK5C,GAJIA,EAAO,aACTA,EAAO,YAAY,mBAGjBA,EAAO,QAAUA,EAAO,OAAO,QACjC,MAAM,IAAIihB,GAAc,KAAMjhB,CAAM,CAExC,CASe,SAAS2qB,GAAgB3qB,EAAQ,CAC9C,OAAA0qB,GAA6B1qB,CAAM,EAEnCA,EAAO,QAAUof,EAAa,KAAKpf,EAAO,OAAO,EAGjDA,EAAO,KAAO8gB,GAAc,KAC1B9gB,EACAA,EAAO,gBACX,EAEM,CAAC,OAAQ,MAAO,OAAO,EAAE,QAAQA,EAAO,MAAM,IAAM,IACtDA,EAAO,QAAQ,eAAe,oCAAqC,EAAK,EAG1DqqB,GAAS,WAAWrqB,EAAO,SAAW+c,GAAS,OAAO,EAEvD/c,CAAM,EAAE,KAAK,SAA6B6Y,EAAU,CACjE,OAAA6R,GAA6B1qB,CAAM,EAGnC6Y,EAAS,KAAOiI,GAAc,KAC5B9gB,EACAA,EAAO,kBACP6Y,CACN,EAEIA,EAAS,QAAUuG,EAAa,KAAKvG,EAAS,OAAO,EAE9CA,CACX,EAAK,SAA4BsO,EAAQ,CACrC,OAAKnG,GAASmG,CAAM,IAClBuD,GAA6B1qB,CAAM,EAG/BmnB,GAAUA,EAAO,WACnBA,EAAO,SAAS,KAAOrG,GAAc,KACnC9gB,EACAA,EAAO,kBACPmnB,EAAO,QACjB,EACQA,EAAO,SAAS,QAAU/H,EAAa,KAAK+H,EAAO,SAAS,OAAO,IAIhE,QAAQ,OAAOA,CAAM,CAChC,CAAG,CACH,CChFO,MAAMyD,GAAU,SCKjBC,GAAa,CAAA,EAGnB,CAAC,SAAU,UAAW,SAAU,WAAY,SAAU,QAAQ,EAAE,QAAQ,CAAC5Y,EAAMrK,IAAM,CACnFijB,GAAW5Y,CAAI,EAAI,SAAmBF,EAAO,CAC3C,OAAO,OAAOA,IAAUE,GAAQ,KAAOrK,EAAI,EAAI,KAAO,KAAOqK,CACjE,CACA,CAAC,EAED,MAAM6Y,GAAqB,CAAA,EAW3BD,GAAW,aAAe,SAAsBlnB,EAAWonB,EAASrS,EAAS,CAC3E,SAASsS,EAAcC,EAAKC,EAAM,CAChC,MAAO,WAAaN,GAAU,0BAA6BK,EAAM,IAAOC,GAAQxS,EAAU,KAAOA,EAAU,GAC5G,CAGD,MAAO,CAACxT,EAAO+lB,EAAKE,IAAS,CAC3B,GAAIxnB,IAAc,GAChB,MAAM,IAAI8U,EACRuS,EAAcC,EAAK,qBAAuBF,EAAU,OAASA,EAAU,GAAG,EAC1EtS,EAAW,cACnB,EAGI,OAAIsS,GAAW,CAACD,GAAmBG,CAAG,IACpCH,GAAmBG,CAAG,EAAI,GAE1B,QAAQ,KACND,EACEC,EACA,+BAAiCF,EAAU,yCAC5C,CACT,GAGWpnB,EAAYA,EAAUuB,EAAO+lB,EAAKE,CAAI,EAAI,EACrD,CACA,EAEAN,GAAW,SAAW,SAAkBO,EAAiB,CACvD,MAAO,CAAClmB,EAAO+lB,KAEb,QAAQ,KAAK,GAAGA,CAAG,+BAA+BG,CAAe,EAAE,EAC5D,GAEX,EAYA,SAASC,GAAcvsB,EAASwsB,EAAQC,EAAc,CACpD,GAAI,OAAOzsB,GAAY,SACrB,MAAM,IAAI2Z,EAAW,4BAA6BA,EAAW,oBAAoB,EAEnF,MAAMzE,EAAO,OAAO,KAAKlV,CAAO,EAChC,IAAI8I,EAAIoM,EAAK,OACb,KAAOpM,KAAM,GAAG,CACd,MAAMqjB,EAAMjX,EAAKpM,CAAC,EACZjE,EAAY2nB,EAAOL,CAAG,EAC5B,GAAItnB,EAAW,CACb,MAAMuB,EAAQpG,EAAQmsB,CAAG,EACnBxrB,EAASyF,IAAU,QAAavB,EAAUuB,EAAO+lB,EAAKnsB,CAAO,EACnE,GAAIW,IAAW,GACb,MAAM,IAAIgZ,EAAW,UAAYwS,EAAM,YAAcxrB,EAAQgZ,EAAW,oBAAoB,EAE9F,QACD,CACD,GAAI8S,IAAiB,GACnB,MAAM,IAAI9S,EAAW,kBAAoBwS,EAAKxS,EAAW,cAAc,CAE1E,CACH,CAEA,MAAe9U,GAAA,CACb,cAAA0nB,GACF,WAAER,EACF,ECvFMA,EAAalnB,GAAU,WAS7B,MAAM6nB,EAAM,CACV,YAAYC,EAAgB,CAC1B,KAAK,SAAWA,GAAkB,GAClC,KAAK,aAAe,CAClB,QAAS,IAAIzQ,GACb,SAAU,IAAIA,EACpB,CACG,CAUD,MAAM,QAAQ0Q,EAAa1rB,EAAQ,CACjC,GAAI,CACF,OAAO,MAAM,KAAK,SAAS0rB,EAAa1rB,CAAM,CAC/C,OAAQymB,EAAK,CACZ,GAAIA,aAAe,MAAO,CACxB,IAAIkF,EAAQ,CAAA,EAEZ,MAAM,kBAAoB,MAAM,kBAAkBA,CAAK,EAAKA,EAAQ,IAAI,MAGxE,MAAMlU,EAAQkU,EAAM,MAAQA,EAAM,MAAM,QAAQ,QAAS,EAAE,EAAI,GAC/D,GAAI,CACGlF,EAAI,MAGEhP,GAAS,CAAC,OAAOgP,EAAI,KAAK,EAAE,SAAShP,EAAM,QAAQ,YAAa,EAAE,CAAC,IAC5EgP,EAAI,OAAS;AAAA,EAAOhP,GAHpBgP,EAAI,MAAQhP,CAKf,MAAW,CAEX,CACF,CAED,MAAMgP,CACP,CACF,CAED,SAASiF,EAAa1rB,EAAQ,CAGxB,OAAO0rB,GAAgB,UACzB1rB,EAASA,GAAU,GACnBA,EAAO,IAAM0rB,GAEb1rB,EAAS0rB,GAAe,GAG1B1rB,EAASwkB,GAAY,KAAK,SAAUxkB,CAAM,EAE1C,KAAM,CAAC,aAAAqd,EAAc,iBAAAuO,EAAkB,QAAA5O,CAAO,EAAIhd,EAE9Cqd,IAAiB,QACnB1Z,GAAU,cAAc0Z,EAAc,CACpC,kBAAmBwN,EAAW,aAAaA,EAAW,OAAO,EAC7D,kBAAmBA,EAAW,aAAaA,EAAW,OAAO,EAC7D,oBAAqBA,EAAW,aAAaA,EAAW,OAAO,CAChE,EAAE,EAAK,EAGNe,GAAoB,OAClB9S,EAAM,WAAW8S,CAAgB,EACnC5rB,EAAO,iBAAmB,CACxB,UAAW4rB,CACZ,EAEDjoB,GAAU,cAAcioB,EAAkB,CACxC,OAAQf,EAAW,SACnB,UAAWA,EAAW,QACvB,EAAE,EAAI,GAKP7qB,EAAO,oBAAsB,SAEtB,KAAK,SAAS,oBAAsB,OAC7CA,EAAO,kBAAoB,KAAK,SAAS,kBAEzCA,EAAO,kBAAoB,IAG7B2D,GAAU,cAAc3D,EAAQ,CAC9B,QAAS6qB,EAAW,SAAS,SAAS,EACtC,cAAeA,EAAW,SAAS,eAAe,CACnD,EAAE,EAAI,EAGP7qB,EAAO,QAAUA,EAAO,QAAU,KAAK,SAAS,QAAU,OAAO,cAGjE,IAAI6rB,EAAiB7O,GAAWlE,EAAM,MACpCkE,EAAQ,OACRA,EAAQhd,EAAO,MAAM,CAC3B,EAEIgd,GAAWlE,EAAM,QACf,CAAC,SAAU,MAAO,OAAQ,OAAQ,MAAO,QAAS,QAAQ,EACzD4E,GAAW,CACV,OAAOV,EAAQU,CAAM,CACtB,CACP,EAEI1d,EAAO,QAAUof,EAAa,OAAOyM,EAAgB7O,CAAO,EAG5D,MAAM8O,EAA0B,CAAA,EAChC,IAAIC,EAAiC,GACrC,KAAK,aAAa,QAAQ,QAAQ,SAAoCC,EAAa,CAC7E,OAAOA,EAAY,SAAY,YAAcA,EAAY,QAAQhsB,CAAM,IAAM,KAIjF+rB,EAAiCA,GAAkCC,EAAY,YAE/EF,EAAwB,QAAQE,EAAY,UAAWA,EAAY,QAAQ,EACjF,CAAK,EAED,MAAMC,EAA2B,CAAA,EACjC,KAAK,aAAa,SAAS,QAAQ,SAAkCD,EAAa,CAChFC,EAAyB,KAAKD,EAAY,UAAWA,EAAY,QAAQ,CAC/E,CAAK,EAED,IAAIE,EACAtkB,EAAI,EACJqM,EAEJ,GAAI,CAAC8X,EAAgC,CACnC,MAAMI,EAAQ,CAACxB,GAAgB,KAAK,IAAI,EAAG,MAAS,EAOpD,IANAwB,EAAM,QAAQ,MAAMA,EAAOL,CAAuB,EAClDK,EAAM,KAAK,MAAMA,EAAOF,CAAwB,EAChDhY,EAAMkY,EAAM,OAEZD,EAAU,QAAQ,QAAQlsB,CAAM,EAEzB4H,EAAIqM,GACTiY,EAAUA,EAAQ,KAAKC,EAAMvkB,GAAG,EAAGukB,EAAMvkB,GAAG,CAAC,EAG/C,OAAOskB,CACR,CAEDjY,EAAM6X,EAAwB,OAE9B,IAAI3G,EAAYnlB,EAIhB,IAFA4H,EAAI,EAEGA,EAAIqM,GAAK,CACd,MAAMvU,EAAcosB,EAAwBlkB,GAAG,EACzC9H,EAAagsB,EAAwBlkB,GAAG,EAC9C,GAAI,CACFud,EAAYzlB,EAAYylB,CAAS,CAClC,OAAQpM,EAAO,CACdjZ,EAAW,KAAK,KAAMiZ,CAAK,EAC3B,KACD,CACF,CAED,GAAI,CACFmT,EAAUvB,GAAgB,KAAK,KAAMxF,CAAS,CAC/C,OAAQpM,EAAO,CACd,OAAO,QAAQ,OAAOA,CAAK,CAC5B,CAKD,IAHAnR,EAAI,EACJqM,EAAMgY,EAAyB,OAExBrkB,EAAIqM,GACTiY,EAAUA,EAAQ,KAAKD,EAAyBrkB,GAAG,EAAGqkB,EAAyBrkB,GAAG,CAAC,EAGrF,OAAOskB,CACR,CAED,OAAOlsB,EAAQ,CACbA,EAASwkB,GAAY,KAAK,SAAUxkB,CAAM,EAC1C,MAAMosB,EAAWjI,GAAcnkB,EAAO,QAASA,EAAO,IAAKA,EAAO,iBAAiB,EACnF,OAAO2a,GAASyR,EAAUpsB,EAAO,OAAQA,EAAO,gBAAgB,CACjE,CACH,CAGA8Y,EAAM,QAAQ,CAAC,SAAU,MAAO,OAAQ,SAAS,EAAG,SAA6B4E,EAAQ,CAEvF8N,GAAM,UAAU9N,CAAM,EAAI,SAAS9C,EAAK5a,EAAQ,CAC9C,OAAO,KAAK,QAAQwkB,GAAYxkB,GAAU,CAAA,EAAI,CAC5C,OAAA0d,EACA,IAAA9C,EACA,MAAO5a,GAAU,CAAA,GAAI,IACtB,CAAA,CAAC,CACN,CACA,CAAC,EAED8Y,EAAM,QAAQ,CAAC,OAAQ,MAAO,OAAO,EAAG,SAA+B4E,EAAQ,CAG7E,SAAS2O,EAAmBC,EAAQ,CAClC,OAAO,SAAoB1R,EAAKvC,EAAMrY,EAAQ,CAC5C,OAAO,KAAK,QAAQwkB,GAAYxkB,GAAU,CAAA,EAAI,CAC5C,OAAA0d,EACA,QAAS4O,EAAS,CAChB,eAAgB,qBAC1B,EAAY,CAAE,EACN,IAAA1R,EACA,KAAAvC,CACD,CAAA,CAAC,CACR,CACG,CAEDmT,GAAM,UAAU9N,CAAM,EAAI2O,EAAkB,EAE5Cb,GAAM,UAAU9N,EAAS,MAAM,EAAI2O,EAAmB,EAAI,CAC5D,CAAC,EAED,MAAAE,GAAef,GCtOf,MAAMgB,EAAY,CAChB,YAAYC,EAAU,CACpB,GAAI,OAAOA,GAAa,WACtB,MAAM,IAAI,UAAU,8BAA8B,EAGpD,IAAIC,EAEJ,KAAK,QAAU,IAAI,QAAQ,SAAyBvL,EAAS,CAC3DuL,EAAiBvL,CACvB,CAAK,EAED,MAAMhJ,EAAQ,KAGd,KAAK,QAAQ,KAAKwO,GAAU,CAC1B,GAAI,CAACxO,EAAM,WAAY,OAEvB,IAAIvQ,EAAIuQ,EAAM,WAAW,OAEzB,KAAOvQ,KAAM,GACXuQ,EAAM,WAAWvQ,CAAC,EAAE+e,CAAM,EAE5BxO,EAAM,WAAa,IACzB,CAAK,EAGD,KAAK,QAAQ,KAAOwU,GAAe,CACjC,IAAIC,EAEJ,MAAMV,EAAU,IAAI,QAAQ/K,GAAW,CACrChJ,EAAM,UAAUgJ,CAAO,EACvByL,EAAWzL,CACnB,CAAO,EAAE,KAAKwL,CAAW,EAEnB,OAAAT,EAAQ,OAAS,UAAkB,CACjC/T,EAAM,YAAYyU,CAAQ,CAClC,EAEaV,CACb,EAEIO,EAAS,SAAgB/T,EAAS1Y,EAAQ4Y,EAAS,CAC7CT,EAAM,SAKVA,EAAM,OAAS,IAAI8I,GAAcvI,EAAS1Y,EAAQ4Y,CAAO,EACzD8T,EAAevU,EAAM,MAAM,EACjC,CAAK,CACF,CAKD,kBAAmB,CACjB,GAAI,KAAK,OACP,MAAM,KAAK,MAEd,CAMD,UAAUra,EAAU,CAClB,GAAI,KAAK,OAAQ,CACfA,EAAS,KAAK,MAAM,EACpB,MACD,CAEG,KAAK,WACP,KAAK,WAAW,KAAKA,CAAQ,EAE7B,KAAK,WAAa,CAACA,CAAQ,CAE9B,CAMD,YAAYA,EAAU,CACpB,GAAI,CAAC,KAAK,WACR,OAEF,MAAMxC,EAAQ,KAAK,WAAW,QAAQwC,CAAQ,EAC1CxC,IAAU,IACZ,KAAK,WAAW,OAAOA,EAAO,CAAC,CAElC,CAED,eAAgB,CACd,MAAM0rB,EAAa,IAAI,gBAEjB6F,EAASpG,GAAQ,CACrBO,EAAW,MAAMP,CAAG,CAC1B,EAEI,YAAK,UAAUoG,CAAK,EAEpB7F,EAAW,OAAO,YAAc,IAAM,KAAK,YAAY6F,CAAK,EAErD7F,EAAW,MACnB,CAMD,OAAO,QAAS,CACd,IAAIL,EAIJ,MAAO,CACL,MAJY,IAAI6F,GAAY,SAAkBM,EAAG,CACjDnG,EAASmG,CACf,CAAK,EAGC,OAAAnG,CACN,CACG,CACH,CAEA,MAAAoG,GAAeP,GC/GA,SAASQ,GAAOC,EAAU,CACvC,OAAO,SAAclX,EAAK,CACxB,OAAOkX,EAAS,MAAM,KAAMlX,CAAG,CACnC,CACA,CChBe,SAASmX,GAAaC,EAAS,CAC5C,OAAOrU,EAAM,SAASqU,CAAO,GAAMA,EAAQ,eAAiB,EAC9D,CCbA,MAAMC,GAAiB,CACrB,SAAU,IACV,mBAAoB,IACpB,WAAY,IACZ,WAAY,IACZ,GAAI,IACJ,QAAS,IACT,SAAU,IACV,4BAA6B,IAC7B,UAAW,IACX,aAAc,IACd,eAAgB,IAChB,YAAa,IACb,gBAAiB,IACjB,OAAQ,IACR,gBAAiB,IACjB,iBAAkB,IAClB,MAAO,IACP,SAAU,IACV,YAAa,IACb,SAAU,IACV,OAAQ,IACR,kBAAmB,IACnB,kBAAmB,IACnB,WAAY,IACZ,aAAc,IACd,gBAAiB,IACjB,UAAW,IACX,SAAU,IACV,iBAAkB,IAClB,cAAe,IACf,4BAA6B,IAC7B,eAAgB,IAChB,SAAU,IACV,KAAM,IACN,eAAgB,IAChB,mBAAoB,IACpB,gBAAiB,IACjB,WAAY,IACZ,qBAAsB,IACtB,oBAAqB,IACrB,kBAAmB,IACnB,UAAW,IACX,mBAAoB,IACpB,oBAAqB,IACrB,OAAQ,IACR,iBAAkB,IAClB,SAAU,IACV,gBAAiB,IACjB,qBAAsB,IACtB,gBAAiB,IACjB,4BAA6B,IAC7B,2BAA4B,IAC5B,oBAAqB,IACrB,eAAgB,IAChB,WAAY,IACZ,mBAAoB,IACpB,eAAgB,IAChB,wBAAyB,IACzB,sBAAuB,IACvB,oBAAqB,IACrB,aAAc,IACd,YAAa,IACb,8BAA+B,GACjC,EAEA,OAAO,QAAQA,EAAc,EAAE,QAAQ,CAAC,CAAC7oB,EAAKW,CAAK,IAAM,CACvDkoB,GAAeloB,CAAK,EAAIX,CAC1B,CAAC,EAED,MAAA8oB,GAAeD,GC3Cf,SAASE,GAAeC,EAAe,CACrC,MAAMjZ,EAAU,IAAIkX,GAAM+B,CAAa,EACjCC,EAAWhc,GAAKga,GAAM,UAAU,QAASlX,CAAO,EAGtDwE,OAAAA,EAAM,OAAO0U,EAAUhC,GAAM,UAAWlX,EAAS,CAAC,WAAY,EAAI,CAAC,EAGnEwE,EAAM,OAAO0U,EAAUlZ,EAAS,KAAM,CAAC,WAAY,EAAI,CAAC,EAGxDkZ,EAAS,OAAS,SAAgB/B,EAAgB,CAChD,OAAO6B,GAAe9I,GAAY+I,EAAe9B,CAAc,CAAC,CACpE,EAES+B,CACT,CAGA,MAAMC,EAAQH,GAAevQ,EAAQ,EAGrC0Q,EAAM,MAAQjC,GAGdiC,EAAM,cAAgBxM,GACtBwM,EAAM,YAAcjB,GACpBiB,EAAM,SAAWzM,GACjByM,EAAM,QAAU7C,GAChB6C,EAAM,WAAahU,GAGnBgU,EAAM,WAAahV,EAGnBgV,EAAM,OAASA,EAAM,cAGrBA,EAAM,IAAM,SAAaC,EAAU,CACjC,OAAO,QAAQ,IAAIA,CAAQ,CAC7B,EAEAD,EAAM,OAAST,GAGfS,EAAM,aAAeP,GAGrBO,EAAM,YAAcjJ,GAEpBiJ,EAAM,aAAerO,EAErBqO,EAAM,WAAa1b,GAASyK,GAAe1D,EAAM,WAAW/G,CAAK,EAAI,IAAI,SAASA,CAAK,EAAIA,CAAK,EAEhG0b,EAAM,WAAapD,GAAS,WAE5BoD,EAAM,eAAiBL,GAEvBK,EAAM,QAAUA,EAGhB,MAAeE,GAAAF,ECpFTzvB,EAAqByvB,GAAM,OAAO,CACtC,QAAS,OACT,QAAS,IACT,QAAS,CACP,eAAgB,kBAClB,CACF,CAAC,EAGDzvB,EAAI,aAAa,QAAQ,IACtBgC,GAAW,OAEV,eAAQ,IAAI,gBAAgBf,EAAAe,EAAO,SAAP,YAAAf,EAAe,cAAee,EAAO,GAAG,EAC7DA,CACT,EACC+Y,GACQ,QAAQ,OAAOA,CAAK,CAE/B,EAGA/a,EAAI,aAAa,SAAS,IACvB6a,IACC,QAAQ,IAAI,gBAAiBA,EAAS,OAAQA,EAAS,OAAO,GAAG,EAC1DA,GAERE,GAAU,SACT,eAAQ,MAAM,cAAc9Z,EAAA8Z,EAAM,WAAN,YAAA9Z,EAAgB,QAAQ8B,EAAAgY,EAAM,WAAN,YAAAhY,EAAgB,IAAI,EACjE,QAAQ,OAAOgY,CAAK,CAC7B,CACF,EAGO,MAAM6U,GAAa,CAExB,YAAa,MAAOpT,IACD,MAAMxc,EAAI,IAAI,YAAa,CAAE,OAAAwc,EAAQ,GACtC,KAIlB,WAAY,MAAOW,IACA,MAAMnd,EAAI,IAAI,aAAamd,CAAE,EAAE,GAChC,KAIlB,eAAgB,MAAOrK,EAAiB0J,KACrB,MAAMxc,EAAI,IAAI,mBAAoB,CACjD,OAAQ,CAAE,QAAA8S,EAAS,GAAG0J,CAAO,CAAA,CAC9B,GACe,KAIlB,mBAAoB,MAAOqT,EAAgB,MACxB,MAAM7vB,EAAI,IAAI,oBAAqB,CAAE,OAAQ,CAAE,MAAA6vB,CAAM,CAAA,CAAG,GACzD,KAIlB,uBAAwB,MAAOvrB,EAAoBurB,EAAgB,MAChD,MAAM7vB,EAAI,IAAI,wBAAyB,CACtD,OAAQ,CAAE,UAAAsE,EAAW,MAAAurB,CAAM,CAAA,CAC5B,GACe,IAEpB,EAGaC,GAAc,CAEzB,cAAe,UACI,MAAM9vB,EAAI,IAAI,sBAAsB,GACZ,KAGpB,IAAI,CAACgB,EAAM1D,KAAW,CACzC,GAAIA,EAAQ,EACZ,KAAA0D,EACA,KAAMA,EAAK,YAAA,EAAc,QAAQ,OAAQ,GAAG,EAC5C,YAAa,GAAGA,CAAI,MACpB,EAAA,EAIJ,YAAa,MAAOmc,GAAkC,CAEpD,MAAM4S,GADa,MAAMD,GAAY,iBACT,KAAYE,GAAAA,EAAI,KAAO7S,CAAE,EACrD,GAAI,CAAC4S,EACH,MAAM,IAAI,MAAM,oBAAoB5S,CAAE,YAAY,EAE7C,OAAA4S,CACT,EAGA,oBAAqB,MAAOE,EAAoBzT,IAAsE,CAEpH,MAAMuT,GADa,MAAMD,GAAY,iBACT,KAAYE,GAAAA,EAAI,KAAOC,CAAU,EAC7D,GAAI,CAACF,EACH,MAAM,IAAI,MAAM,oBAAoBE,CAAU,YAAY,EAI5D,OADiB,MAAMjwB,EAAI,IAAI,sBAAsB,mBAAmB+vB,EAAS,IAAI,CAAC,GAAI,CAAE,OAAAvT,CAAQ,CAAA,GACpF,IAClB,CACF,ECzGa0T,GAAe1T,GACnB2T,GAAS,CACd,SAAU,CAAC,WAAY3T,CAAM,EAC7B,QAAS,IAAMoT,GAAW,YAAYpT,CAAM,EAC5C,UAAW,EAAI,GAAK,IACpB,OAAQ,GAAK,GAAK,GAAA,CACnB,EAIU4T,GAAcjT,GAClBgT,GAAS,CACd,SAAU,CAAC,UAAWhT,CAAE,EACxB,QAAS,IAAMyS,GAAW,WAAWzS,CAAE,EACvC,QAAS,CAAC,CAACA,EACX,UAAW,EAAI,GAAK,GAAA,CACrB,EA6BUkT,GAAqB,CAACR,EAAgB,KAC1CM,GAAS,CACd,SAAU,CAAC,kBAAmBN,CAAK,EACnC,QAAS,IAAMD,GAAW,mBAAmBC,CAAK,EAClD,UAAW,GAAK,GAAK,GAAA,CACtB,EAIUS,GAAyB,CAAChsB,EAAoBurB,EAAgB,KAClEM,GAAS,CACd,SAAU,CAAC,sBAAuB7rB,EAAWurB,CAAK,EAClD,QAAS,IAAMD,GAAW,uBAAuBtrB,EAAWurB,CAAK,EACjE,QAAS,CAAC,CAACvrB,EACX,UAAW,GAAK,GAAK,GAAA,CACtB,EC5DUisB,GAAgB,IACpBJ,GAAS,CACd,SAAU,CAAC,YAAY,EACvB,QAASL,GAAY,cACrB,UAAW,GAAK,GAAK,IACrB,OAAQ,GAAK,GAAK,GAAA,CACnB,ECIGU,GAAO,CAAC,CACZ,SAAAtyB,EACA,UAAAb,EACA,MAAAozB,EAAQ,GACR,QAAAC,CACF,IAEIje,EAAC,MAAA,CACC,UAAWvB,GACT,OACAuf,GAAS,iDACTC,GAAW,iBACXrzB,CACF,EACA,QAAAqzB,EAEC,SAAAxyB,CAAA,CAAA,EAKDyyB,GAA2C,CAAC,CAAE,SAAAzyB,EAAU,UAAAb,OAEzD,MAAI,CAAA,UAAW6T,GAAG,cAAe7T,CAAS,EACxC,SAAAa,CACH,CAAA,EAIE0yB,GAA4C,CAAC,CAAE,SAAA1yB,EAAU,UAAAb,OAE1D,MAAI,CAAA,UAAW6T,GAAG,eAAgB7T,CAAS,EACzC,SAAAa,CACH,CAAA,EAIE2yB,GAA2C,CAAC,CAAE,SAAA3yB,EAAU,UAAAb,OAEzD,MAAI,CAAA,UAAW6T,GAAG,cAAe7T,CAAS,EACxC,SAAAa,CACH,CAAA,EAKE4yB,EAAwBN,GAC9BM,EAAsB,OAASH,GAC/BG,EAAsB,QAAUF,GAChCE,EAAsB,OAASD,GCxD/B,MAAME,GAA0C,CAAC,CAAE,QAAA9sB,EAAS,QAAAysB,KAAc,CACxE,KAAM,CAAE,QAAAM,EAAS,SAAAC,EAAU,gBAAAC,GAAoBltB,GAAa,EAEtDmtB,EAAWltB,EAAQ,cACrBsN,GAAkBtN,EAAQ,cAAeA,EAAQ,KAAK,EACtD,EAEEmtB,EAAQvf,GAAc5N,EAAQ,MAAM,EACpCotB,EAASJ,EAAShtB,EAAQ,EAAE,EAC5BC,EAAWgtB,EAAgBjtB,EAAQ,EAAE,EAErCqtB,EAAmB1vB,GAAwB,CAC/CA,EAAE,gBAAgB,EAClBovB,EAAQ/sB,CAAO,CAAA,EAOjB,OACGuO,EAAAge,EAAA,CAAK,UAAU,eAAe,MAAK,GAClC,SAAA,CAAChe,EAAA,MAAA,CAAI,QANe,IAAM,CAC5Bke,GAAA,MAAAA,EAAUzsB,EAAO,EAOb,SAAA,CAACuO,EAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAC,EAAC,MAAA,CACC,IAAKxO,EAAQ,SACb,IAAKA,EAAQ,KACb,UAAU,6EACV,QAAQ,MAAA,CACV,EAGCktB,EAAW,GACT3e,EAAA,MAAA,CAAI,UAAU,uFAAuF,SAAA,CAAA,IAClG2e,EAAS,GAAA,EACb,EAIDltB,EAAQ,QAAU,GACjBwO,EAAC,MAAI,CAAA,UAAU,2EACb,SAAAA,EAAC,OAAK,CAAA,UAAU,yBAAyB,SAAA,IAAE,CAAA,EAC7C,CAAA,EAEJ,EAGCD,EAAAge,EAAK,QAAL,CAAa,UAAU,MAEtB,SAAA,CAAA/d,EAAC,KAAG,CAAA,UAAU,uFACX,SAAAxO,EAAQ,KACX,EAGCwO,EAAA,IAAA,CAAE,UAAU,6BAA8B,WAAQ,MAAM,EAGzDD,EAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAC,EAAC,OAAI,UAAU,eACZ,WAAM,IAAI,CAAC8e,EAAQj0B,IAClBmV,EAACtT,GAAA,CAEC,UAAW,WAAWoyB,EAAS,cAAgB,YAAY,GAC3D,KAAK,cAAA,EAFAj0B,CAIR,CAAA,EACH,EACAkV,EAAC,OAAK,CAAA,UAAU,6BAA6B,SAAA,CAAA,IACzCvO,EAAQ,YAAY,GAAA,EACxB,CAAA,EACF,IAGC,MAAI,CAAA,UAAU,yCACb,SAACuO,EAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,EAAC,QAAK,UAAU,gBACb,SAAYrB,GAAAnN,EAAQ,KAAK,EAC5B,EACCA,EAAQ,eAAiBA,EAAQ,cAAgBA,EAAQ,OACvDwO,EAAA,OAAA,CAAK,UAAU,iBACb,SAAYrB,GAAAnN,EAAQ,aAAa,EACpC,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,EACF,CAAA,EACF,EAGCwO,EAAA+d,EAAK,OAAL,CAAY,UAAU,WACrB,SAAAhe,EAACP,EAAA,CACC,QAASqf,EACT,SAAUrtB,EAAQ,QAAU,EAC5B,UAAU,SACV,QAASotB,EAAS,YAAc,UAEhC,SAAA,CAAC5e,EAAAvT,GAAA,CAAa,UAAU,cAAe,CAAA,EACtC+E,EAAQ,QAAU,EACf,KACAotB,EACE,QAAQntB,CAAQ,IAChB,OAAA,CAAA,CAAA,EAGV,CACF,CAAA,CAAA,CAEJ,EChHMstB,GAAkC,CAAC,CAAE,KAAAzzB,EAAO,KAAM,UAAAV,KAQpDoV,EAAC,OAAI,UAAWvB,GAAG,kBAPuB,CAC1C,GAAI,UACJ,GAAI,UACJ,GAAI,WAAA,EAI8CnT,CAAI,EAAGV,CAAS,CAAG,CAAA,EAK5Do0B,GAAwB,MAEhC,MAAI,CAAA,UAAU,iDACb,SAACjf,EAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA+e,GAAA,CAAQ,KAAK,IAAK,CAAA,EAClB/e,EAAA,IAAA,CAAE,UAAU,qBAAqB,SAAM,SAAA,CAAA,CAC1C,CAAA,CACF,CAAA,EAKSif,GAA6C,CAAC,CAAE,UAAAr0B,OACnD,MAAI,CAAA,UAAW6T,GAAG,WAAY7T,CAAS,CAAG,CAAA,EAIvCs0B,GAAgC,IAEzCnf,EAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACC,EAAAif,GAAA,CAAS,UAAU,0BAA2B,CAAA,EAC/Clf,EAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAACC,EAAAif,GAAA,CAAS,UAAU,WAAY,CAAA,EAChCjf,EAACif,GAAS,CAAA,UAAU,WAAY,CAAA,EAChClf,EAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAAif,GAAA,CAAS,UAAU,UAAW,CAAA,EAC/Bjf,EAACif,GAAS,CAAA,UAAU,UAAW,CAAA,CAAA,EACjC,CAAA,EACF,CACF,CAAA,CAAA,ECzCEE,GAA0C,CAAC,CAC/C,SAAAC,EACA,QAAA1f,EAAU,GACV,eAAA2f,CACF,IACM3f,IAEC,MAAI,CAAA,UAAU,eACZ,SAAM,MAAA,KAAK,CAAE,OAAQ,EAAI,CAAA,EAAE,IAAI,CAACH,EAAG1U,MACjCq0B,GAAyB,GAAAr0B,CAAO,CAClC,CACH,CAAA,EAIAu0B,EAAS,SAAW,EAEpBrf,EAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,8BAA8B,SAAE,KAAA,EAC9CA,EAAA,KAAA,CAAG,UAAU,yCAAyC,SAAI,OAAA,EAC1DA,EAAA,IAAA,CAAE,UAAU,gBAAgB,SAAe,kBAAA,CAC9C,CAAA,CAAA,IAKD,MAAI,CAAA,UAAU,eACZ,SAASof,EAAA,IAAK5tB,GACbwO,EAACse,GAAA,CAEC,QAAA9sB,EACA,QAAS6tB,CAAA,EAFJ7tB,EAAQ,EAIhB,CAAA,CACH,CAAA,EC7BE8tB,GAAqB,IAAM,CAC/B,MAAMC,EAAWC,KACX,CAACC,CAAY,EAAIlf,WAA8B,CACnD,KAAM,EACN,KAAM,GACN,OAAQ,YACR,UAAW,MAAA,CACZ,EAEK,CAAE,KAAMmf,EAAc,UAAWC,GAAoBlC,GAAYgC,CAAY,EAC7E,CAAE,KAAMG,EAAiB,UAAWC,GAAmBjC,GAAmB,CAAC,EAC3E,CAAE,KAAMkC,EAAY,UAAWC,CAAA,EAAsBjC,KAErDkC,EAAsBxuB,GAAqB,CACtC+tB,EAAA,aAAa/tB,EAAQ,EAAE,EAAE,CAAA,EAG9ByuB,EAAuBzC,GAAuB,CACzC+B,EAAA,eAAe/B,CAAU,EAAE,CAAA,EAGlC,OAAAmC,GAAmBE,GAAkBE,IAC/Bf,GAAY,CAAA,CAAA,EAIpBjf,EAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAAAC,EAAC,WAAQ,UAAU,0CACjB,SAACD,EAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,KAAA,CAAG,UAAU,sCAAsC,SAEpD,UAAA,EACCA,EAAA,IAAA,CAAE,UAAU,sCAAsC,SAEnD,gBAAA,EACAA,EAACR,EAAA,CACC,KAAK,KACL,QAAQ,YACR,QAAS,IAAM+f,EAAS,WAAW,EACnC,UAAU,mBACX,SAAA,MAAA,CAED,CAAA,CAAA,CACF,CACF,CAAA,EAGCO,GAAcA,EAAW,OAAS,KAChC,UACC,CAAA,SAAA,CAAC9f,EAAA,KAAA,CAAG,UAAU,wCAAwC,SAAI,OAAA,EAC1DA,EAAC,MAAI,CAAA,UAAU,uDACZ,SAAA8f,EAAW,MAAM,EAAG,CAAC,EAAE,IAAKxC,GAC3Bvd,EAACge,EAAA,CAEC,MAAK,GACL,UAAU,iCACV,QAAS,IAAMkC,EAAoB3C,EAAS,EAAE,EAE9C,SAAA,CAACtd,EAAA,MAAA,CAAI,UAAU,sFACb,SAAAA,EAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,KAAA,CAAG,UAAU,4BAA6B,WAAS,KAAK,CAAA,CAAA,EARpDsd,EAAS,EAUjB,CAAA,EACH,CAAA,EACF,EAIDsC,GAAmBA,EAAgB,OAAS,KAC1C,UACC,CAAA,SAAA,CAAC7f,EAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,KAAA,CAAG,UAAU,mCAAmC,SAAI,OAAA,EACrDA,EAACR,EAAA,CACC,QAAQ,UACR,QAAS,IAAM+f,EAAS,wBAAwB,EACjD,SAAA,MAAA,CAED,CAAA,EACF,EACAvf,EAACmf,GAAA,CACC,SAAUS,EACV,QAASC,EACT,eAAgBG,CAAA,CAClB,CAAA,EACF,IAID,UACC,CAAA,SAAA,CAACjgB,EAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,KAAA,CAAG,UAAU,mCAAmC,SAAI,OAAA,EACrDA,EAACR,EAAA,CACC,QAAQ,UACR,QAAS,IAAM+f,EAAS,WAAW,EACpC,SAAA,MAAA,CAED,CAAA,EACF,EACAvf,EAACmf,GAAA,CACC,UAAUO,GAAA,YAAAA,EAAc,UAAW,CAAC,EACpC,QAASC,EACT,eAAgBK,CAAA,CAClB,CAAA,EACF,EAGAjgB,EAAC,UAAQ,CAAA,UAAU,4BACjB,SAAA,CAACC,EAAA,KAAA,CAAG,UAAU,oDAAoD,SAElE,UAAA,EACAD,EAAC,MAAI,CAAA,UAAU,4BACb,SAAA,CAACA,EAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,sFACb,SAAAA,EAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,KAAA,CAAG,UAAU,6BAA6B,SAAI,OAAA,EAC9CA,EAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,uBAAA,CAAA,EACF,EACAD,EAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,sFACb,SAAAA,EAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,KAAA,CAAG,UAAU,6BAA6B,SAAK,QAAA,EAC/CA,EAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,sBAAA,CAAA,EACF,EACAD,EAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,sFACb,SAAAA,EAAC,QAAK,UAAU,WAAW,cAAE,CAC/B,CAAA,EACCA,EAAA,KAAA,CAAG,UAAU,6BAA6B,SAAI,OAAA,EAC9CA,EAAA,IAAA,CAAE,UAAU,gBAAgB,SAE7B,oBAAA,CAAA,EACF,CAAA,EACF,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EC/IMkgB,GAA8B,IAAM,CAClC,KAAA,CAAE,GAAAxV,GAAOyV,KACTZ,EAAWC,KACX3tB,EAAY,SAAS6Y,GAAM,GAAG,EAE9B,CAACjZ,EAAU2uB,CAAW,EAAI7f,WAAS,CAAC,EACpC,CAAC8f,EAAeC,CAAgB,EAAI/f,WAAS,CAAC,EAE9C,CAAE,KAAM/O,EAAS,UAAA+uB,EAAW,MAAAjY,GAAUqV,GAAW9rB,CAAS,EAC1D,CAAE,KAAM2uB,CAAA,EAAwB3C,GAAuBhsB,EAAW,CAAC,EACnE,CAAE,QAAA0sB,EAAS,SAAAC,EAAU,gBAAAC,GAAoBltB,GAAa,EAExD,GAAAgvB,EAAW,SAAQvB,GAAY,CAAA,CAAA,EAE/B,GAAA1W,GAAS,CAAC9W,EAEV,OAAAuO,EAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,8BAA8B,SAAE,KAAA,EAC9CA,EAAA,KAAA,CAAG,UAAU,wCAAwC,SAAK,QAAA,EAC1DA,EAAA,IAAA,CAAE,UAAU,qBAAqB,SAAgB,mBAAA,IACjDR,EAAO,CAAA,QAAS,IAAM+f,EAAS,GAAG,EAAG,SAAI,OAAA,CAC5C,CAAA,CAAA,EAIE,MAAAb,EAAWltB,EAAQ,cACrBsN,GAAkBtN,EAAQ,cAAeA,EAAQ,KAAK,EACtD,EAEEmtB,EAAQvf,GAAc5N,EAAQ,MAAM,EACpCotB,EAASJ,EAAShtB,EAAQ,EAAE,EAC5BivB,EAAehC,EAAgBjtB,EAAQ,EAAE,EACzCkvB,EAASlvB,EAAQ,QAAU,CAACA,EAAQ,QAAQ,EAE5CqtB,EAAkB,IAAM,CAC5BN,EAAQ/sB,EAASC,CAAQ,EACzB2uB,EAAY,CAAC,CAAA,EAGTO,EAAwBC,GAAkB,CACxC,MAAAC,EAAc,KAAK,IAAI,EAAG,KAAK,IAAIrvB,EAAQ,MAAOC,EAAWmvB,CAAK,CAAC,EACzER,EAAYS,CAAW,CAAA,EAGnBb,EAAsBc,GAAwB,CACzCvB,EAAA,aAAauB,EAAe,EAAE,EAAE,CAAA,EAIzC,OAAA/gB,EAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAAAA,EAACP,EAAA,CACC,QAAQ,QACR,QAAS,IAAM+f,EAAS,EAAE,EAC1B,UAAU,oBAEV,SAAA,CAACvf,EAAA5T,GAAA,CAAU,UAAU,cAAe,CAAA,EAAE,IAAA,CAAA,CAExC,EAGA2T,EAAC,MAAI,CAAA,UAAU,4BAEb,SAAA,CAACA,EAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,uDACb,SAAAA,EAAC,MAAA,CACC,IAAK0gB,EAAOL,CAAa,EACzB,IAAK7uB,EAAQ,KACb,UAAU,4BAAA,CAAA,EAEd,EAECkvB,EAAO,OAAS,GACd1gB,EAAA,MAAA,CAAI,UAAU,iCACZ,SAAO0gB,EAAA,IAAI,CAACK,EAAOl2B,IAClBmV,EAAC,SAAA,CAEC,QAAS,IAAMsgB,EAAiBz1B,CAAK,EACrC,UAAW,+DACTw1B,IAAkBx1B,EAAQ,qBAAuB,iBACnD,GAEA,SAAAmV,EAAC,MAAA,CACC,IAAK+gB,EACL,IAAK,GAAGvvB,EAAQ,IAAI,IAAI3G,EAAQ,CAAC,GACjC,UAAU,4BAAA,CACZ,CAAA,EAVKA,CAYR,CAAA,EACH,CAAA,EAEJ,EAGAkV,EAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,EAAC,MACC,CAAA,SAAA,CAAAC,EAAC,KAAG,CAAA,UAAU,wCACX,SAAAxO,EAAQ,KACX,EACCwO,EAAA,IAAA,CAAE,UAAU,wBAAyB,WAAQ,MAAM,CAAA,EACtD,EAGAD,EAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAC,EAAC,OAAI,UAAU,eACZ,WAAM,IAAI,CAAC8e,EAAQj0B,IAClBmV,EAACtT,GAAA,CAEC,UAAW,WAAWoyB,EAAS,cAAgB,YAAY,GAC3D,KAAK,cAAA,EAFAj0B,CAIR,CAAA,EACH,EACAkV,EAAC,OAAK,CAAA,UAAU,gBACb,SAAA,CAAQvO,EAAA,OAAO,KAAGA,EAAQ,YAAY,MAAA,EACzC,CAAA,EACF,EAGAuO,EAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,EAAC,QAAK,UAAU,kCACb,SAAYrB,GAAAnN,EAAQ,KAAK,EAC5B,EACCA,EAAQ,eAAiBA,EAAQ,cAAgBA,EAAQ,OAEtDuO,EAAAihB,GAAA,CAAA,SAAA,CAAAhhB,EAAC,QAAK,UAAU,qCACb,SAAYrB,GAAAnN,EAAQ,aAAa,EACpC,EACAuO,EAAC,OAAK,CAAA,UAAU,gEAAgE,SAAA,CAAA,KAC3EpB,GAAYnN,EAAQ,cAAgBA,EAAQ,KAAK,CAAA,EACtD,CAAA,EACF,CAAA,EAEJ,EACCktB,EAAW,GACT3e,EAAA,IAAA,CAAE,UAAU,6BAA6B,SAAA,CAAA,QAClC2e,EAAS,OAAA,EACjB,CAAA,EAEJ,EAGA3e,EAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAA,OAAA,CAAK,UAAU,gBAAgB,SAAG,MAAA,EACnCA,EAAC,QAAK,UAAW,eACfxO,EAAQ,MAAQ,GAAK,iBACrBA,EAAQ,MAAQ,EAAI,kBAAoB,cAC1C,GACG,SAAQA,EAAA,MAAQ,EAAI,GAAGA,EAAQ,KAAK,KAAO,IAC9C,CAAA,CAAA,EACF,EAGCA,EAAQ,MAAQ,GACduO,EAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAA,OAAA,CAAK,UAAU,gBAAgB,SAAG,MAAA,EACnCD,EAAC,MAAI,CAAA,UAAU,sDACb,SAAA,CAAAC,EAACR,EAAA,CACC,QAAQ,QACR,KAAK,KACL,QAAS,IAAMmhB,EAAqB,EAAE,EACtC,SAAUlvB,GAAY,EACtB,UAAU,YAEV,SAAAuO,EAAC1T,GAAM,CAAA,UAAU,SAAU,CAAA,CAAA,CAC7B,EACC0T,EAAA,OAAA,CAAK,UAAU,qCACb,SACHvO,EAAA,EACAuO,EAACR,EAAA,CACC,QAAQ,QACR,KAAK,KACL,QAAS,IAAMmhB,EAAqB,CAAC,EACrC,SAAUlvB,GAAYD,EAAQ,MAC9B,UAAU,YAEV,SAAAwO,EAACzT,GAAK,CAAA,UAAU,SAAU,CAAA,CAAA,CAC5B,CAAA,EACF,CAAA,EACF,EAIFwT,EAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAA,EAACP,EAAA,CACC,QAASqf,EACT,SAAUrtB,EAAQ,QAAU,EAC5B,UAAU,SACV,KAAK,KAEL,SAAA,CAACwO,EAAAvT,GAAA,CAAa,UAAU,cAAe,CAAA,EACtC+E,EAAQ,QAAU,EAAI,KAAO,OAAA,CAAA,CAChC,EACCotB,GACC7e,EAAC,MAAI,CAAA,UAAU,0CAA0C,SAAA,CAAA,UAC/C0gB,EAAa,IAAA,EACvB,CAAA,EAEJ,IAGC1C,EACC,CAAA,SAAA,CAAC/d,EAAA+d,EAAK,OAAL,CACC,SAAA/d,EAAC,MAAG,UAAU,wBAAwB,gBAAI,CAC5C,CAAA,EACAA,EAAC+d,EAAK,QAAL,CACC,SAAA/d,EAAC,KAAE,UAAU,gCACV,SAAQxO,EAAA,WAAA,CACX,CACF,CAAA,CAAA,EACF,EAGCA,EAAQ,gBACPuO,EAACge,EACC,CAAA,SAAA,CAAC/d,EAAA+d,EAAK,OAAL,CACC,SAAA/d,EAAC,MAAG,UAAU,wBAAwB,gBAAI,CAC5C,CAAA,EACAA,EAAC+d,EAAK,QAAL,CACC,WAAC,MAAI,CAAA,UAAU,YACZ,SAAA,OAAO,QAAQvsB,EAAQ,cAAc,EAAE,IAAI,CAAC,CAACsC,EAAKW,CAAK,IACtDsL,EAAC,MAAc,CAAA,UAAU,qEACvB,SAAA,CAACC,EAAA,OAAA,CAAK,UAAU,gBAAiB,SAAIlM,EAAA,EACpCkM,EAAA,OAAA,CAAK,UAAU,cAAe,SAAMvL,EAAA,CAF7B,CAAA,EAAAX,CAGV,CACD,CACH,CAAA,EACF,CAAA,EACF,CAAA,EAEJ,CAAA,EACF,EAGC0sB,GAAuBA,EAAoB,OAAS,KAClD,UACC,CAAA,SAAA,CAACxgB,EAAA,KAAA,CAAG,UAAU,wCAAwC,SAAI,OAAA,EAC1DA,EAACmf,GAAA,CACC,SAAUqB,EACV,eAAgBR,CAAA,CAClB,CAAA,EACF,CAEJ,CAAA,CAAA,CAEJ,ECzPMiB,GAAc,IAAIC,GAAY,CAClC,eAAgB,CACd,QAAS,CACP,MAAO,EACP,qBAAsB,GACtB,UAAW,EAAI,GAAK,GACtB,CACF,CACF,CAAC,EAEKC,GAAgB,IAOlBnhB,EAACohB,IAAoB,OAAQH,GAC3B,WAACI,GACC,CAAA,SAAAthB,EAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACC,EAAAU,GAAA,CAAO,SATML,GAAoB,CAEhC,QAAA,IAAI,SAAUA,CAAO,CAAA,CAOS,CAAA,EAE/BL,EAAA,OAAA,CAAK,UAAU,8CACd,WAACshB,GACC,CAAA,SAAA,CAAAthB,EAACuhB,IAAM,KAAK,IAAI,QAASvhB,EAACsf,IAAS,CAAA,EAAI,IACtCiC,GAAM,CAAA,KAAK,gBAAgB,QAASvhB,EAACkgB,IAAkB,CAAA,EAAI,IAC3DqB,GAAM,CAAA,KAAK,IAAI,QAASvhB,EAACwhB,IAAa,CAAA,EAAI,CAAA,CAAA,CAC7C,CACF,CAAA,IAECC,GAAO,EAAA,CAAA,EACV,EACF,CACF,CAAA,EAKED,GAAyB,IAE3BzhB,EAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACC,EAAA,MAAA,CAAI,UAAU,8BAA8B,SAAE,KAAA,EAC9CA,EAAA,KAAA,CAAG,UAAU,wCAAwC,SAAK,QAAA,EAC1DA,EAAA,IAAA,CAAE,UAAU,qBAAqB,SAAY,eAAA,EAC9CA,EAAC,IAAA,CACC,KAAK,IACL,UAAU,iHACX,SAAA,MAAA,CAED,CACF,CAAA,CAAA,EAKEyhB,GAAmB,MAEpB,SAAO,CAAA,UAAU,0BAChB,SAAC1hB,EAAA,MAAA,CAAI,UAAU,+CACb,SAAA,CAACA,EAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAAAA,EAAC,MACC,CAAA,SAAA,CAACC,EAAA,KAAA,CAAG,UAAU,mCAAmC,SAAI,OAAA,EACpDA,EAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,4CAAA,CAAA,EACF,IACC,MACC,CAAA,SAAA,CAACA,EAAA,KAAA,CAAG,UAAU,mCAAmC,SAAI,OAAA,EACrDD,EAAC,KAAG,CAAA,UAAU,oBACZ,SAAA,CAACC,EAAA,KAAA,CAAG,WAAC,IAAE,CAAA,KAAK,IAAI,UAAU,uCAAuC,cAAE,CAAI,CAAA,EACvEA,EAAC,MAAG,SAACA,EAAA,IAAA,CAAE,KAAK,YAAY,UAAU,uCAAuC,SAAA,IAAA,CAAE,CAAI,CAAA,EAC/EA,EAAC,MAAG,SAACA,EAAA,IAAA,CAAE,KAAK,cAAc,UAAU,uCAAuC,SAAA,IAAA,CAAE,CAAI,CAAA,EACjFA,EAAC,MAAG,SAACA,EAAA,IAAA,CAAE,KAAK,QAAQ,UAAU,uCAAuC,SAAA,KAAA,CAAG,CAAI,CAAA,CAAA,EAC9E,CAAA,EACF,IACC,MACC,CAAA,SAAA,CAACA,EAAA,KAAA,CAAG,UAAU,mCAAmC,SAAG,MAAA,EACpDD,EAAC,KAAG,CAAA,UAAU,kCACZ,SAAA,CAAAC,EAAC,MAAG,SAAqB,uBAAA,CAAA,EACzBA,EAAC,MAAG,SAAmB,qBAAA,CAAA,EACvBA,EAAC,MAAG,SAAqB,uBAAA,CAAA,EACzBA,EAAC,MAAG,SAAkB,oBAAA,CAAA,CAAA,EACxB,CAAA,EACF,IACC,MACC,CAAA,SAAA,CAACA,EAAA,KAAA,CAAG,UAAU,mCAAmC,SAAI,OAAA,EACrDD,EAAC,KAAG,CAAA,UAAU,kCACZ,SAAA,CAAAC,EAAC,MAAG,SAAuB,yBAAA,CAAA,EAC3BA,EAAC,MAAG,SAAqB,uBAAA,CAAA,EACzBA,EAAC,MAAG,SAAU,YAAA,CAAA,CAAA,EAChB,CAAA,EACF,CAAA,EACF,IACC,MAAI,CAAA,UAAU,uDACb,SAACA,EAAA,IAAA,CAAE,kDAA2C,CAChD,CAAA,CAAA,CACF,CAAA,CACF,CAAA,ECpGJ/V,GAAW,SAAS,eAAe,MAAM,CAAE,EAAE,SAC1C8D,GAAM,WAAN,CACC,SAAAiS,EAACmhB,IAAI,CAAA,EACP,CACF", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71]}
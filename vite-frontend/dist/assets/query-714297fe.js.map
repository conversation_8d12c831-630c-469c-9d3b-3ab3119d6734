{"version": 3, "file": "query-714297fe.js", "sources": ["../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../node_modules/react/jsx-runtime.js", "../../node_modules/@tanstack/query-core/build/modern/subscribable.js", "../../node_modules/@tanstack/query-core/build/modern/utils.js", "../../node_modules/@tanstack/query-core/build/modern/focusManager.js", "../../node_modules/@tanstack/query-core/build/modern/onlineManager.js", "../../node_modules/@tanstack/query-core/build/modern/thenable.js", "../../node_modules/@tanstack/query-core/build/modern/retryer.js", "../../node_modules/@tanstack/query-core/build/modern/notifyManager.js", "../../node_modules/@tanstack/query-core/build/modern/removable.js", "../../node_modules/@tanstack/query-core/build/modern/query.js", "../../node_modules/@tanstack/query-core/build/modern/queryCache.js", "../../node_modules/@tanstack/query-core/build/modern/mutation.js", "../../node_modules/@tanstack/query-core/build/modern/mutationCache.js", "../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "../../node_modules/@tanstack/query-core/build/modern/queryClient.js", "../../node_modules/@tanstack/query-core/build/modern/queryObserver.js", "../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js", "../../node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js", "../../node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js", "../../node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js", "../../node_modules/@tanstack/react-query/build/modern/suspense.js", "../../node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "../../node_modules/@tanstack/react-query/build/modern/useQuery.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\nexport {\n  Subscribable\n};\n//# sourceMappingURL=subscribable.js.map", "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    const aItemsSet = new Set(aItems);\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItemsSet.has(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport {\n  addToEnd,\n  addToStart,\n  ensureQueryFn,\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  isPlainArray,\n  isPlainObject,\n  isServer,\n  isValidTimeout,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceData,\n  replaceEqualDeep,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  shouldThrowError,\n  skipToken,\n  sleep,\n  timeUntilStale\n};\n//# sourceMappingURL=utils.js.map", "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport {\n  FocusManager,\n  focusManager\n};\n//# sourceMappingURL=focusManager.js.map", "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport {\n  OnlineManager,\n  onlineManager\n};\n//# sourceMappingURL=onlineManager.js.map", "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  }, noop)?.catch(noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\nexport {\n  pendingThenable,\n  tryResolveSync\n};\n//# sourceMappingURL=thenable.js.map", "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = pendingThenable();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport {\n  CancelledError,\n  canFetch,\n  createRetryer,\n  isCancelledError\n};\n//# sourceMappingURL=retryer.js.map", "// src/notifyManager.ts\nvar defaultScheduler = (cb) => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport {\n  createNotifyManager,\n  defaultScheduler,\n  notifyManager\n};\n//# sourceMappingURL=notifyManager.js.map", "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport {\n  Removable\n};\n//# sourceMappingURL=removable.js.map", "// src/query.ts\nimport {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  skipToken,\n  timeUntilStale\n} from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => resolveStaleTime(observer.options.staleTime, this) === \"static\"\n      );\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          this.#revertState = void 0;\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport {\n  Query,\n  fetchState\n};\n//# sourceMappingURL=query.js.map", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => matchQuery(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport {\n  QueryCache\n};\n//# sourceMappingURL=queryCache.js.map", "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport {\n  Mutation,\n  getDefaultState\n};\n//# sourceMappingURL=mutation.js.map", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => matchMutation(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport {\n  MutationCache\n};\n//# sourceMappingURL=mutationCache.js.map", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const { maxPages } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior\n};\n//# sourceMappingURL=infiniteQueryBehavior.js.map", "// src/queryClient.ts\nimport {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken\n} from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled() && !query.isStatic()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport {\n  QueryClient\n};\n//# sourceMappingURL=queryClient.js.map", "// src/queryObserver.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { fetchState } from \"./query.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale\n} from \"./utils.js\";\nvar QueryObserver = class extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = pendingThenable();\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error(\"experimental_prefetchInRender feature flag is not enabled\")\n      );\n    }\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */ new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    );\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    );\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof resolveEnabled(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\n        \"Expected enabled to be a boolean or a callback that returns a boolean\"\n      );\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(\n      this.#currentQuery,\n      prevQuery,\n      this.options,\n      prevOptions\n    )) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || resolveStaleTime(this.options.staleTime, this.#currentQuery) !== resolveStaleTime(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({ ...options } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(\n      this.options,\n      fetchOptions\n    );\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery\n    );\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return;\n    }\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (isServer || resolveEnabled(this.options.enabled, this.#currentQuery) === false || !isValidTimeout(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const { state } = query;\n    let newState = { ...state };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let { error, errorUpdatedAt, status } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(\n          this.#lastQueryWithDefinedData?.state.data,\n          this.#lastQueryWithDefinedData\n        ) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = replaceData(\n          prevResult?.data,\n          placeholderData,\n          options\n        );\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = replaceData(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable,\n      isEnabled: resolveEnabled(options.enabled, query) !== false\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable) => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = pendingThenable();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const { notifyOnChangeProps } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps\n      );\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({ listeners: shouldNotifyListeners() });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if (resolveEnabled(options.enabled, query) !== false && resolveStaleTime(options.staleTime, query) !== \"static\") {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || resolveEnabled(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.isStaleByTime(resolveStaleTime(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\nexport {\n  QueryObserver\n};\n//# sourceMappingURL=queryObserver.js.map", "\"use client\";\n\n// src/QueryClientProvider.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar QueryClientContext = React.createContext(\n  void 0\n);\nvar useQueryClient = (queryClient) => {\n  const client = React.useContext(QueryClientContext);\n  if (queryClient) {\n    return queryClient;\n  }\n  if (!client) {\n    throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n  }\n  return client;\n};\nvar QueryClientProvider = ({\n  client,\n  children\n}) => {\n  React.useEffect(() => {\n    client.mount();\n    return () => {\n      client.unmount();\n    };\n  }, [client]);\n  return /* @__PURE__ */ jsx(QueryClientContext.Provider, { value: client, children });\n};\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient\n};\n//# sourceMappingURL=QueryClientProvider.js.map", "\"use client\";\n\n// src/IsRestoringProvider.ts\nimport * as React from \"react\";\nvar IsRestoringContext = React.createContext(false);\nvar useIsRestoring = () => React.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\nexport {\n  IsRestoringProvider,\n  useIsRestoring\n};\n//# sourceMappingURL=IsRestoringProvider.js.map", "\"use client\";\n\n// src/QueryErrorResetBoundary.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createValue() {\n  let isReset = false;\n  return {\n    clearReset: () => {\n      isReset = false;\n    },\n    reset: () => {\n      isReset = true;\n    },\n    isReset: () => {\n      return isReset;\n    }\n  };\n}\nvar QueryErrorResetBoundaryContext = React.createContext(createValue());\nvar useQueryErrorResetBoundary = () => React.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({\n  children\n}) => {\n  const [value] = React.useState(() => createValue());\n  return /* @__PURE__ */ jsx(QueryErrorResetBoundaryContext.Provider, { value, children: typeof children === \"function\" ? children(value) : children });\n};\nexport {\n  QueryErrorResetBoundary,\n  useQueryErrorResetBoundary\n};\n//# sourceMappingURL=QueryErrorResetBoundary.js.map", "\"use client\";\n\n// src/errorBoundaryUtils.ts\nimport * as React from \"react\";\nimport { shouldThrowError } from \"@tanstack/query-core\";\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {\n  if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false;\n    }\n  }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset();\n  }, [errorResetBoundary]);\n};\nvar getHasError = ({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense\n}) => {\n  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || shouldThrowError(throwOnError, [result.error, query]));\n};\nexport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n};\n//# sourceMappingURL=errorBoundaryUtils.js.map", "// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  if (defaultedOptions.suspense) {\n    const clamp = (value) => value === \"static\" ? value : Math.max(value ?? 1e3, 1e3);\n    const originalStaleTime = defaultedOptions.staleTime;\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => clamp(originalStaleTime(...args)) : clamp(originalStaleTime);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\nexport {\n  defaultThrowOnError,\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n};\n//# sourceMappingURL=suspense.js.map", "\"use client\";\n\n// src/useBaseQuery.ts\nimport * as React from \"react\";\nimport { isServer, noop, notify<PERSON>anager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nimport { useQueryErrorResetBoundary } from \"./QueryErrorResetBoundary.js\";\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n} from \"./errorBoundaryUtils.js\";\nimport { useIsRestoring } from \"./IsRestoringProvider.js\";\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n} from \"./suspense.js\";\nfunction useBaseQuery(options, Observer, queryClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof options !== \"object\" || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object'\n      );\n    }\n  }\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const client = useQueryClient(queryClient);\n  const defaultedOptions = client.defaultQueryOptions(options);\n  client.getDefaultOptions().queries?._experimental_beforeQuery?.(\n    defaultedOptions\n  );\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`\n      );\n    }\n  }\n  defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n  ensureSuspenseTimers(defaultedOptions);\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary);\n  useClearResetErrorBoundary(errorResetBoundary);\n  const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n  const [observer] = React.useState(\n    () => new Observer(\n      client,\n      defaultedOptions\n    )\n  );\n  const result = observer.getOptimisticResult(defaultedOptions);\n  const shouldSubscribe = !isRestoring && options.subscribed !== false;\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe ? observer.subscribe(notifyManager.batchCalls(onStoreChange)) : noop;\n        observer.updateResult();\n        return unsubscribe;\n      },\n      [observer, shouldSubscribe]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions);\n  }, [defaultedOptions, observer]);\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary);\n  }\n  if (getHasError({\n    result,\n    errorResetBoundary,\n    throwOnError: defaultedOptions.throwOnError,\n    query: client.getQueryCache().get(defaultedOptions.queryHash),\n    suspense: defaultedOptions.suspense\n  })) {\n    throw result.error;\n  }\n  ;\n  client.getDefaultOptions().queries?._experimental_afterQuery?.(\n    defaultedOptions,\n    result\n  );\n  if (defaultedOptions.experimental_prefetchInRender && !isServer && willFetch(result, isRestoring)) {\n    const promise = isNewCacheEntry ? (\n      // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n      fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n    ) : (\n      // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n      client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n    );\n    promise?.catch(noop).finally(() => {\n      observer.updateResult();\n    });\n  }\n  return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\nexport {\n  useBaseQuery\n};\n//# sourceMappingURL=useBaseQuery.js.map", "\"use client\";\n\n// src/useQuery.ts\nimport { QueryObserver } from \"@tanstack/query-core\";\nimport { useBaseQuery } from \"./useBaseQuery.js\";\nfunction useQuery(options, queryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient);\n}\nexport {\n  useQuery\n};\n//# sourceMappingURL=useQuery.js.map"], "names": ["f", "require$$0", "k", "l", "m", "n", "p", "q", "c", "a", "g", "b", "d", "e", "reactJsxRuntime_production_min", "jsxRuntimeModule", "Subscribable", "listener", "isServer", "noop", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "timeUntilStale", "updatedAt", "staleTime", "resolveStaleTime", "query", "resolveEnabled", "enabled", "matchQuery", "filters", "type", "exact", "fetchStatus", "predicate", "query<PERSON><PERSON>", "stale", "hashQueryKeyByOptions", "partialMatchKey", "isActive", "matchMutation", "mutation", "status", "<PERSON><PERSON><PERSON>", "hash<PERSON><PERSON>", "options", "_", "val", "isPlainObject", "result", "key", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aItems", "aSize", "bItems", "bSize", "copy", "aItemsSet", "equalItems", "i", "shallowEqualObjects", "o", "hasObjectPrototype", "ctor", "prot", "sleep", "timeout", "resolve", "replaceData", "prevData", "data", "addToEnd", "items", "item", "max", "newItems", "addToStart", "skipToken", "ensureQueryFn", "fetchOptions", "shouldThrowError", "throwOnError", "params", "FocusManager", "_a", "__privateAdd", "_focused", "_cleanup", "_setup", "__privateSet", "onFocus", "__privateGet", "setup", "focused", "isFocused", "focusManager", "OnlineManager", "_online", "onOnline", "onlineListener", "offlineListener", "online", "onlineManager", "pendingThenable", "reject", "thenable", "_resolve", "_reject", "finalize", "reason", "defaultRetryDelay", "failureCount", "canFetch", "networkMode", "CancelledError", "isCancelledError", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "cancel", "cancelOptions", "cancelRetry", "continueRetry", "canContinue", "canStart", "pause", "continueResolve", "run", "promiseOrValue", "initialPromise", "error", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "defaultScheduler", "cb", "createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "scheduleFn", "schedule", "flush", "originalQueue", "args", "fn", "notify<PERSON><PERSON>ger", "Removable", "_gcTimeout", "newGcTime", "Query", "_dispatch", "_initialState", "_revertState", "_cache", "_client", "_retryer", "_defaultOptions", "_abortSignalConsumed", "getDefaultState", "newData", "__privateMethod", "dispatch_fn", "state", "setStateOptions", "promise", "_b", "observer", "x", "abortController", "addSignalProperty", "object", "fetchFn", "queryFn", "queryFnContext", "queryFnContext2", "context", "context2", "_c", "onError", "_d", "action", "reducer", "fetchState", "hasData", "initialDataUpdatedAt", "Query<PERSON>ache", "_queries", "client", "queryHash", "queryInMap", "defaultedFilters", "queries", "event", "Mutation", "_observers", "_mutationCache", "variables", "onContinue", "restored", "isPaused", "_f", "_e", "_h", "_g", "_j", "_i", "_l", "_k", "_n", "_m", "_p", "_o", "_r", "_q", "_t", "_s", "MutationCache", "_mutations", "_scopes", "_mutationId", "__privateWrapper", "scope", "scopeFor", "scopedMutations", "index", "mutationsWithSameScope", "firstPendingMutation", "foundMutation", "pausedMutations", "infiniteQueryBehavior", "pages", "direction", "oldPages", "oldPageParams", "currentPage", "cancelled", "fetchPage", "param", "previous", "page", "maxPages", "addTo", "pageParamFn", "getPreviousPageParam", "getNextPageParam", "oldData", "remainingPages", "pageParams", "lastIndex", "QueryClient", "_queryCache", "_queryDefaults", "_mutationDefaults", "_mountCount", "_unsubscribeFocus", "_unsubscribeOnline", "defaultedOptions", "cachedData", "queryCache", "defaultedCancelOptions", "promises", "defaults", "query<PERSON><PERSON><PERSON>", "QueryObserver", "_executeFetch", "_updateStaleTimeout", "_computeRefetchInterval", "_updateRefetchInterval", "_updateTimers", "_clearStaleTimeout", "_clearRefetchInterval", "_updateQuery", "_notify", "_current<PERSON><PERSON>y", "_currentQueryInitialState", "_currentResult", "_currentResultState", "_currentResultOptions", "_currentThenable", "_selectError", "_selectFn", "_selectResult", "_lastQueryWithDefinedData", "_staleTimeoutId", "_refetchIntervalId", "_currentRefetchInterval", "_trackedProps", "shouldFetchOnMount", "executeFetch_fn", "updateTimers_fn", "shouldFetchOn", "clearStaleTimeout_fn", "clearRefetchInterval_fn", "prevOptions", "prev<PERSON><PERSON><PERSON>", "updateQuery_fn", "mounted", "shouldFetchOptionally", "updateStaleTimeout_fn", "nextRefetchInterval", "computeRefetchInterval_fn", "updateRefetchInterval_fn", "shouldAssignObserverCurrentProperties", "onPropTracked", "target", "prevResult", "prevResultState", "prevResultOptions", "queryInitialState", "newState", "isPlaceholderData", "fetchOnMount", "fetchOptionally", "errorUpdatedAt", "skipSelect", "placeholderData", "selectError", "isFetching", "isPending", "isError", "isLoading", "nextResult", "isStale", "finalizeThenableIfPossible", "recreateThenable", "pending", "prevThenable", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "<PERSON><PERSON><PERSON>", "notify_fn", "nextInterval", "notifyOptions", "shouldLoadOnMount", "field", "optimisticResult", "QueryClientContext", "React.createContext", "useQueryClient", "queryClient", "React.useContext", "QueryClientProvider", "children", "React.useEffect", "jsx", "IsRestoringContext", "useIsRestoring", "createValue", "isReset", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "ensurePreventErrorBoundaryRetry", "errorResetBoundary", "useClearResetErrorBoundary", "getHasError", "suspense", "ensureSuspenseTimers", "clamp", "originalStaleTime", "<PERSON><PERSON><PERSON><PERSON>", "isRestoring", "shouldSuspend", "fetchOptimistic", "useBaseQuery", "Observer", "isNewCacheEntry", "React.useState", "shouldSubscribe", "React.useSyncExternalStore", "React.useCallback", "onStoreChange", "unsubscribe", "useQuery"], "mappings": ";;;;;;;;GASa,IAAIA,GAAEC,EAAiBC,GAAE,OAAO,IAAI,eAAe,EAAEC,GAAE,OAAO,IAAI,gBAAgB,EAAEC,GAAE,OAAO,UAAU,eAAeC,GAAEL,GAAE,mDAAmD,kBAAkBM,GAAE,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,SAAS,EAAE,EAClP,SAASC,GAAEC,EAAEC,EAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAE,GAAGC,EAAE,KAAK,EAAE,KAAcH,IAAT,SAAaG,EAAE,GAAGH,GAAYD,EAAE,MAAX,SAAiBI,EAAE,GAAGJ,EAAE,KAAcA,EAAE,MAAX,SAAiB,EAAEA,EAAE,KAAK,IAAIE,KAAKF,EAAEL,GAAE,KAAKK,EAAEE,CAAC,GAAG,CAACL,GAAE,eAAeK,CAAC,IAAIC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,GAAGH,GAAGA,EAAE,aAAa,IAAIG,KAAKF,EAAED,EAAE,aAAaC,EAAWG,EAAED,CAAC,IAAZ,SAAgBC,EAAED,CAAC,EAAEF,EAAEE,CAAC,GAAG,MAAM,CAAC,SAAST,GAAE,KAAKM,EAAE,IAAIK,EAAE,IAAI,EAAE,MAAMD,EAAE,OAAOP,GAAE,OAAO,CAAC,aAAkBF,GAAaW,GAAA,IAACP,GAAEO,GAAA,KAAaP,GCPxWQ,GAAA,QAAiBd,+DCFnB,IAAIe,GAAe,KAAM,CACvB,aAAc,CACZ,KAAK,UAA4B,IAAI,IACrC,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,CAC1C,CACD,UAAUC,EAAU,CAClB,YAAK,UAAU,IAAIA,CAAQ,EAC3B,KAAK,YAAW,EACT,IAAM,CACX,KAAK,UAAU,OAAOA,CAAQ,EAC9B,KAAK,cAAa,CACxB,CACG,CACD,cAAe,CACb,OAAO,KAAK,UAAU,KAAO,CAC9B,CACD,aAAc,CACb,CACD,eAAgB,CACf,CACH,ECpBIC,GAAW,OAAO,OAAW,KAAe,SAAU,WAC1D,SAASC,GAAO,CAChB,CACA,SAASC,GAAiBC,EAASC,EAAO,CACxC,OAAO,OAAOD,GAAY,WAAaA,EAAQC,CAAK,EAAID,CAC1D,CACA,SAASE,GAAeC,EAAO,CAC7B,OAAO,OAAOA,GAAU,UAAYA,GAAS,GAAKA,IAAU,GAC9D,CACA,SAASC,GAAeC,EAAWC,EAAW,CAC5C,OAAO,KAAK,IAAID,GAAaC,GAAa,GAAK,KAAK,MAAO,CAAC,CAC9D,CACA,SAASC,GAAiBD,EAAWE,EAAO,CAC1C,OAAO,OAAOF,GAAc,WAAaA,EAAUE,CAAK,EAAIF,CAC9D,CACA,SAASG,EAAeC,EAASF,EAAO,CACtC,OAAO,OAAOE,GAAY,WAAaA,EAAQF,CAAK,EAAIE,CAC1D,CACA,SAASC,GAAWC,EAASJ,EAAO,CAClC,KAAM,CACJ,KAAAK,EAAO,MACP,MAAAC,EACA,YAAAC,EACA,UAAAC,EACA,SAAAC,EACA,MAAAC,CACD,EAAGN,EACJ,GAAIK,GACF,GAAIH,GACF,GAAIN,EAAM,YAAcW,GAAsBF,EAAUT,EAAM,OAAO,EACnE,MAAO,WAEA,CAACY,GAAgBZ,EAAM,SAAUS,CAAQ,EAClD,MAAO,GAGX,GAAIJ,IAAS,MAAO,CAClB,MAAMQ,EAAWb,EAAM,WAIvB,GAHIK,IAAS,UAAY,CAACQ,GAGtBR,IAAS,YAAcQ,EACzB,MAAO,EAEV,CAOD,MANI,SAAOH,GAAU,WAAaV,EAAM,QAAO,IAAOU,GAGlDH,GAAeA,IAAgBP,EAAM,MAAM,aAG3CQ,GAAa,CAACA,EAAUR,CAAK,EAInC,CACA,SAASc,GAAcV,EAASW,EAAU,CACxC,KAAM,CAAE,MAAAT,EAAO,OAAAU,EAAQ,UAAAR,EAAW,YAAAS,CAAW,EAAKb,EAClD,GAAIa,EAAa,CACf,GAAI,CAACF,EAAS,QAAQ,YACpB,MAAO,GAET,GAAIT,GACF,GAAIY,GAAQH,EAAS,QAAQ,WAAW,IAAMG,GAAQD,CAAW,EAC/D,MAAO,WAEA,CAACL,GAAgBG,EAAS,QAAQ,YAAaE,CAAW,EACnE,MAAO,EAEV,CAID,MAHI,EAAAD,GAAUD,EAAS,MAAM,SAAWC,GAGpCR,GAAa,CAACA,EAAUO,CAAQ,EAItC,CACA,SAASJ,GAAsBF,EAAUU,EAAS,CAEhD,QADeA,GAAA,YAAAA,EAAS,iBAAkBD,IAC5BT,CAAQ,CACxB,CACA,SAASS,GAAQT,EAAU,CACzB,OAAO,KAAK,UACVA,EACA,CAACW,EAAGC,IAAQC,GAAcD,CAAG,EAAI,OAAO,KAAKA,CAAG,EAAE,KAAM,EAAC,OAAO,CAACE,EAAQC,KACvED,EAAOC,CAAG,EAAIH,EAAIG,CAAG,EACdD,GACN,CAAA,CAAE,EAAIF,CACb,CACA,CACA,SAAST,GAAgBhC,EAAGE,EAAG,CAC7B,OAAIF,IAAME,EACD,GAEL,OAAOF,GAAM,OAAOE,EACf,GAELF,GAAKE,GAAK,OAAOF,GAAM,UAAY,OAAOE,GAAM,SAC3C,OAAO,KAAKA,CAAC,EAAE,MAAO0C,GAAQZ,GAAgBhC,EAAE4C,CAAG,EAAG1C,EAAE0C,CAAG,CAAC,CAAC,EAE/D,EACT,CACA,SAASC,GAAiB7C,EAAGE,EAAG,CAC9B,GAAIF,IAAME,EACR,OAAOF,EAET,MAAM8C,EAAQC,GAAa/C,CAAC,GAAK+C,GAAa7C,CAAC,EAC/C,GAAI4C,GAASJ,GAAc1C,CAAC,GAAK0C,GAAcxC,CAAC,EAAG,CACjD,MAAM8C,EAASF,EAAQ9C,EAAI,OAAO,KAAKA,CAAC,EAClCiD,EAAQD,EAAO,OACfE,EAASJ,EAAQ5C,EAAI,OAAO,KAAKA,CAAC,EAClCiD,EAAQD,EAAO,OACfE,EAAON,EAAQ,CAAE,EAAG,GACpBO,EAAY,IAAI,IAAIL,CAAM,EAChC,IAAIM,EAAa,EACjB,QAASC,EAAI,EAAGA,EAAIJ,EAAOI,IAAK,CAC9B,MAAMX,EAAME,EAAQS,EAAIL,EAAOK,CAAC,GAC3B,CAACT,GAASO,EAAU,IAAIT,CAAG,GAAKE,IAAU9C,EAAE4C,CAAG,IAAM,QAAU1C,EAAE0C,CAAG,IAAM,QAC7EQ,EAAKR,CAAG,EAAI,OACZU,MAEAF,EAAKR,CAAG,EAAIC,GAAiB7C,EAAE4C,CAAG,EAAG1C,EAAE0C,CAAG,CAAC,EACvCQ,EAAKR,CAAG,IAAM5C,EAAE4C,CAAG,GAAK5C,EAAE4C,CAAG,IAAM,QACrCU,IAGL,CACD,OAAOL,IAAUE,GAASG,IAAeL,EAAQjD,EAAIoD,CACtD,CACD,OAAOlD,CACT,CACA,SAASsD,GAAoBxD,EAAGE,EAAG,CACjC,GAAI,CAACA,GAAK,OAAO,KAAKF,CAAC,EAAE,SAAW,OAAO,KAAKE,CAAC,EAAE,OACjD,MAAO,GAET,UAAW0C,KAAO5C,EAChB,GAAIA,EAAE4C,CAAG,IAAM1C,EAAE0C,CAAG,EAClB,MAAO,GAGX,MAAO,EACT,CACA,SAASG,GAAahC,EAAO,CAC3B,OAAO,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,OAAO,KAAKA,CAAK,EAAE,MACrE,CACA,SAAS2B,GAAce,EAAG,CACxB,GAAI,CAACC,GAAmBD,CAAC,EACvB,MAAO,GAET,MAAME,EAAOF,EAAE,YACf,GAAIE,IAAS,OACX,MAAO,GAET,MAAMC,EAAOD,EAAK,UAOlB,MANI,GAACD,GAAmBE,CAAI,GAGxB,CAACA,EAAK,eAAe,eAAe,GAGpC,OAAO,eAAeH,CAAC,IAAM,OAAO,UAI1C,CACA,SAASC,GAAmBD,EAAG,CAC7B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAC,IAAM,iBAC/C,CACA,SAASI,GAAMC,EAAS,CACtB,OAAO,IAAI,QAASC,GAAY,CAC9B,WAAWA,EAASD,CAAO,CAC/B,CAAG,CACH,CACA,SAASE,GAAYC,EAAUC,EAAM3B,EAAS,CAC5C,OAAI,OAAOA,EAAQ,mBAAsB,WAChCA,EAAQ,kBAAkB0B,EAAUC,CAAI,EACtC3B,EAAQ,oBAAsB,GAWhCM,GAAiBoB,EAAUC,CAAI,EAEjCA,CACT,CAIA,SAASC,GAASC,EAAOC,EAAMC,EAAM,EAAG,CACtC,MAAMC,EAAW,CAAC,GAAGH,EAAOC,CAAI,EAChC,OAAOC,GAAOC,EAAS,OAASD,EAAMC,EAAS,MAAM,CAAC,EAAIA,CAC5D,CACA,SAASC,GAAWJ,EAAOC,EAAMC,EAAM,EAAG,CACxC,MAAMC,EAAW,CAACF,EAAM,GAAGD,CAAK,EAChC,OAAOE,GAAOC,EAAS,OAASD,EAAMC,EAAS,MAAM,EAAG,EAAE,EAAIA,CAChE,CACA,IAAIE,GAAY,OAAM,EACtB,SAASC,GAAcnC,EAASoC,EAAc,CAQ5C,MAAI,CAACpC,EAAQ,UAAWoC,GAAA,MAAAA,EAAc,gBAC7B,IAAMA,EAAa,eAExB,CAACpC,EAAQ,SAAWA,EAAQ,UAAYkC,GACnC,IAAM,QAAQ,OAAO,IAAI,MAAM,qBAAqBlC,EAAQ,SAAS,GAAG,CAAC,EAE3EA,EAAQ,OACjB,CACA,SAASqC,GAAiBC,EAAcC,EAAQ,CAC9C,OAAI,OAAOD,GAAiB,WACnBA,EAAa,GAAGC,CAAM,EAExB,CAAC,CAACD,CACX,gBC/NIE,IAAeC,GAAA,cAAczE,EAAa,CAI5C,aAAc,CACZ,QAJF0E,EAAA,KAAAC,GAAA,QACAD,EAAA,KAAAE,EAAA,QACAF,EAAA,KAAAG,GAAA,QAGEC,EAAA,KAAKD,GAAUE,GAAY,CACzB,GAAI,CAAC7E,IAAY,OAAO,iBAAkB,CACxC,MAAMD,EAAW,IAAM8E,IACvB,cAAO,iBAAiB,mBAAoB9E,EAAU,EAAK,EACpD,IAAM,CACX,OAAO,oBAAoB,mBAAoBA,CAAQ,CACjE,CACO,CAEP,EACG,CACD,aAAc,CACP+E,EAAA,KAAKJ,IACR,KAAK,iBAAiBI,EAAA,KAAKH,GAAM,CAEpC,CACD,eAAgB,OACT,KAAK,kBACRJ,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAW,QAEnB,CACD,iBAAiBK,EAAO,OACtBH,EAAA,KAAKD,GAASI,IACdR,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAWK,EAAOC,GAAY,CAC7B,OAAOA,GAAY,UACrB,KAAK,WAAWA,CAAO,EAEvB,KAAK,QAAO,CAEpB,CAAK,EACF,CACD,WAAWA,EAAS,CACFF,EAAA,KAAKL,MAAaO,IAEhCJ,EAAA,KAAKH,GAAWO,GAChB,KAAK,QAAO,EAEf,CACD,SAAU,CACR,MAAMC,EAAY,KAAK,YACvB,KAAK,UAAU,QAASlF,GAAa,CACnCA,EAASkF,CAAS,CACxB,CAAK,CACF,CACD,WAAY,OACV,OAAI,OAAOH,EAAA,KAAKL,KAAa,UACpBK,EAAA,KAAKL,MAEPF,EAAA,WAAW,WAAX,YAAAA,EAAqB,mBAAoB,QACjD,CACH,EAzDEE,GAAA,YACAC,EAAA,YACAC,GAAA,YAHiBJ,IA2DfW,GAAe,IAAIZ,cC3DnBa,IAAgBZ,GAAA,cAAczE,EAAa,CAI7C,aAAc,CACZ,QAJF0E,EAAA,KAAAY,GAAU,IACVZ,EAAA,KAAAE,EAAA,QACAF,EAAA,KAAAG,GAAA,QAGEC,EAAA,KAAKD,GAAUU,GAAa,CAC1B,GAAI,CAACrF,IAAY,OAAO,iBAAkB,CACxC,MAAMsF,EAAiB,IAAMD,EAAS,EAAI,EACpCE,EAAkB,IAAMF,EAAS,EAAK,EAC5C,cAAO,iBAAiB,SAAUC,EAAgB,EAAK,EACvD,OAAO,iBAAiB,UAAWC,EAAiB,EAAK,EAClD,IAAM,CACX,OAAO,oBAAoB,SAAUD,CAAc,EACnD,OAAO,oBAAoB,UAAWC,CAAe,CAC/D,CACO,CAEP,EACG,CACD,aAAc,CACPT,EAAA,KAAKJ,IACR,KAAK,iBAAiBI,EAAA,KAAKH,GAAM,CAEpC,CACD,eAAgB,OACT,KAAK,kBACRJ,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAW,QAEnB,CACD,iBAAiBK,EAAO,OACtBH,EAAA,KAAKD,GAASI,IACdR,EAAAO,EAAA,KAAKJ,KAAL,MAAAH,EAAA,WACAK,EAAA,KAAKF,EAAWK,EAAM,KAAK,UAAU,KAAK,IAAI,CAAC,EAChD,CACD,UAAUS,EAAQ,CACAV,EAAA,KAAKM,MAAYI,IAE/BZ,EAAA,KAAKQ,GAAUI,GACf,KAAK,UAAU,QAASzF,GAAa,CACnCA,EAASyF,CAAM,CACvB,CAAO,EAEJ,CACD,UAAW,CACT,OAAOV,EAAA,KAAKM,GACb,CACH,EA/CEA,GAAA,YACAV,EAAA,YACAC,GAAA,YAHkBJ,IAiDhBkB,GAAgB,IAAIN,GClDxB,SAASO,IAAkB,CACzB,IAAIpC,EACAqC,EACJ,MAAMC,EAAW,IAAI,QAAQ,CAACC,EAAUC,IAAY,CAClDxC,EAAUuC,EACVF,EAASG,CACb,CAAG,EACDF,EAAS,OAAS,UAClBA,EAAS,MAAM,IAAM,CACvB,CAAG,EACD,SAASG,EAAStC,EAAM,CACtB,OAAO,OAAOmC,EAAUnC,CAAI,EAC5B,OAAOmC,EAAS,QAChB,OAAOA,EAAS,MACjB,CACD,OAAAA,EAAS,QAAWtF,GAAU,CAC5ByF,EAAS,CACP,OAAQ,YACR,MAAAzF,CACN,CAAK,EACDgD,EAAQhD,CAAK,CACjB,EACEsF,EAAS,OAAUI,GAAW,CAC5BD,EAAS,CACP,OAAQ,WACR,OAAAC,CACN,CAAK,EACDL,EAAOK,CAAM,CACjB,EACSJ,CACT,CC3BA,SAASK,GAAkBC,EAAc,CACvC,OAAO,KAAK,IAAI,IAAM,GAAKA,EAAc,GAAG,CAC9C,CACA,SAASC,GAASC,EAAa,CAC7B,OAAQA,GAAe,YAAc,SAAWX,GAAc,SAAU,EAAG,EAC7E,CACA,IAAIY,GAAiB,cAAc,KAAM,CACvC,YAAYvE,EAAS,CACnB,MAAM,gBAAgB,EACtB,KAAK,OAASA,GAAA,YAAAA,EAAS,OACvB,KAAK,OAASA,GAAA,YAAAA,EAAS,MACxB,CACH,EACA,SAASwE,GAAiBhG,EAAO,CAC/B,OAAOA,aAAiB+F,EAC1B,CACA,SAASE,GAAcC,EAAQ,CAC7B,IAAIC,EAAmB,GACnBP,EAAe,EACfQ,EAAa,GACbC,EACJ,MAAMf,EAAWF,KACXkB,EAAUC,GAAkB,OAC3BH,IACHf,EAAO,IAAIU,GAAeQ,CAAa,CAAC,GACxCtC,EAAAiC,EAAO,QAAP,MAAAjC,EAAA,KAAAiC,GAEN,EACQM,EAAc,IAAM,CACxBL,EAAmB,EACvB,EACQM,EAAgB,IAAM,CAC1BN,EAAmB,EACvB,EACQO,EAAc,IAAM9B,GAAa,UAAS,IAAOsB,EAAO,cAAgB,UAAYf,GAAc,SAAQ,IAAOe,EAAO,OAAM,EAC9HS,EAAW,IAAMd,GAASK,EAAO,WAAW,GAAKA,EAAO,SACxDlD,EAAWhD,GAAU,OACpBoG,IACHA,EAAa,IACbnC,EAAAiC,EAAO,YAAP,MAAAjC,EAAA,KAAAiC,EAAmBlG,GACnBqG,GAAA,MAAAA,IACAf,EAAS,QAAQtF,CAAK,EAE5B,EACQqF,EAAUrF,GAAU,OACnBoG,IACHA,EAAa,IACbnC,EAAAiC,EAAO,UAAP,MAAAjC,EAAA,KAAAiC,EAAiBlG,GACjBqG,GAAA,MAAAA,IACAf,EAAS,OAAOtF,CAAK,EAE3B,EACQ4G,EAAQ,IACL,IAAI,QAASC,GAAoB,OACtCR,EAAcrG,GAAU,EAClBoG,GAAcM,MAChBG,EAAgB7G,CAAK,CAE/B,GACMiE,EAAAiC,EAAO,UAAP,MAAAjC,EAAA,KAAAiC,EACN,CAAK,EAAE,KAAK,IAAM,OACZG,EAAa,OACRD,IACHnC,EAAAiC,EAAO,aAAP,MAAAjC,EAAA,KAAAiC,EAER,CAAK,EAEGY,EAAM,IAAM,CAChB,GAAIV,EACF,OAEF,IAAIW,EACJ,MAAMC,EAAiBpB,IAAiB,EAAIM,EAAO,eAAiB,OACpE,GAAI,CACFa,EAAiBC,GAAkBd,EAAO,IAC3C,OAAQe,EAAO,CACdF,EAAiB,QAAQ,OAAOE,CAAK,CACtC,CACD,QAAQ,QAAQF,CAAc,EAAE,KAAK/D,CAAO,EAAE,MAAOiE,GAAU,OAC7D,GAAIb,EACF,OAEF,MAAMc,EAAQhB,EAAO,QAAUxG,GAAW,EAAI,GACxCyH,EAAajB,EAAO,YAAcP,GAClCyB,EAAQ,OAAOD,GAAe,WAAaA,EAAWvB,EAAcqB,CAAK,EAAIE,EAC7EE,EAAcH,IAAU,IAAQ,OAAOA,GAAU,UAAYtB,EAAesB,GAAS,OAAOA,GAAU,YAAcA,EAAMtB,EAAcqB,CAAK,EACnJ,GAAId,GAAoB,CAACkB,EAAa,CACpChC,EAAO4B,CAAK,EACZ,MACD,CACDrB,KACA3B,EAAAiC,EAAO,SAAP,MAAAjC,EAAA,KAAAiC,EAAgBN,EAAcqB,GAC9BnE,GAAMsE,CAAK,EAAE,KAAK,IACTV,EAAa,EAAG,OAASE,EAAK,CACtC,EAAE,KAAK,IAAM,CACRT,EACFd,EAAO4B,CAAK,EAEZH,GAEV,CAAO,CACP,CAAK,CACL,EACE,MAAO,CACL,QAASxB,EACT,OAAAgB,EACA,SAAU,KACRD,GAAA,MAAAA,IACOf,GAET,YAAAkB,EACA,cAAAC,EACA,SAAAE,EACA,MAAO,KACDA,EAAQ,EACVG,IAEAF,EAAO,EAAC,KAAKE,CAAG,EAEXxB,EAEb,CACA,CC9HA,IAAIgC,GAAoBC,GAAO,WAAWA,EAAI,CAAC,EAC/C,SAASC,IAAsB,CAC7B,IAAIC,EAAQ,CAAA,EACRC,EAAe,EACfC,EAAYC,GAAa,CAC3BA,GACJ,EACMC,EAAiBD,GAAa,CAChCA,GACJ,EACME,EAAaR,GACjB,MAAMS,EAAYH,GAAa,CACzBF,EACFD,EAAM,KAAKG,CAAQ,EAEnBE,EAAW,IAAM,CACfH,EAASC,CAAQ,CACzB,CAAO,CAEP,EACQI,EAAQ,IAAM,CAClB,MAAMC,EAAgBR,EACtBA,EAAQ,CAAA,EACJQ,EAAc,QAChBH,EAAW,IAAM,CACfD,EAAc,IAAM,CAClBI,EAAc,QAASL,GAAa,CAClCD,EAASC,CAAQ,CAC7B,CAAW,CACX,CAAS,CACT,CAAO,CAEP,EACE,MAAO,CACL,MAAQA,GAAa,CACnB,IAAIhG,EACJ8F,IACA,GAAI,CACF9F,EAASgG,EAAQ,CACzB,QAAgB,CACRF,IACKA,GACHM,GAEH,CACD,OAAOpG,CACR,EAID,WAAagG,GACJ,IAAIM,IAAS,CAClBH,EAAS,IAAM,CACbH,EAAS,GAAGM,CAAI,CAC1B,CAAS,CACT,EAEI,SAAAH,EAKA,kBAAoBI,GAAO,CACzBR,EAAWQ,CACZ,EAKD,uBAAyBA,GAAO,CAC9BN,EAAgBM,CACjB,EACD,aAAeA,GAAO,CACpBL,EAAaK,CACd,CACL,CACA,CACA,IAAIC,EAAgBZ,GAAqB,QC5ErCa,IAAYpE,GAAA,KAAM,CAAN,cACdC,EAAA,KAAAoE,GAAA,QACA,SAAU,CACR,KAAK,eAAc,CACpB,CACD,YAAa,CACX,KAAK,eAAc,EACfvI,GAAe,KAAK,MAAM,GAC5BuE,EAAA,KAAKgE,GAAa,WAAW,IAAM,CACjC,KAAK,eAAc,CAC3B,EAAS,KAAK,MAAM,EAEjB,CACD,aAAaC,EAAW,CACtB,KAAK,OAAS,KAAK,IACjB,KAAK,QAAU,EACfA,IAAc7I,GAAW,IAAW,EAAI,GAAK,IACnD,CACG,CACD,gBAAiB,CACX8E,EAAA,KAAK8D,MACP,aAAa9D,EAAA,KAAK8D,GAAU,EAC5BhE,EAAA,KAAKgE,GAAa,QAErB,CACH,EAxBEA,GAAA,YADcrE,8BCWZuE,IAAQvE,GAAA,cAAcoE,EAAU,CAQlC,YAAYnC,EAAQ,CAClB,QAkRFhC,EAAA,KAAAuE,GA1RAvE,EAAA,KAAAwE,GAAA,QACAxE,EAAA,KAAAyE,GAAA,QACAzE,EAAA,KAAA0E,EAAA,QACA1E,EAAA,KAAA2E,GAAA,QACA3E,EAAA,KAAA4E,EAAA,QACA5E,EAAA,KAAA6E,GAAA,QACA7E,EAAA,KAAA8E,GAAA,QAGE1E,EAAA,KAAK0E,GAAuB,IAC5B1E,EAAA,KAAKyE,GAAkB7C,EAAO,gBAC9B,KAAK,WAAWA,EAAO,OAAO,EAC9B,KAAK,UAAY,GACjB5B,EAAA,KAAKuE,GAAU3C,EAAO,QACtB5B,EAAA,KAAKsE,EAASpE,EAAA,KAAKqE,IAAQ,cAAa,GACxC,KAAK,SAAW3C,EAAO,SACvB,KAAK,UAAYA,EAAO,UACxB5B,EAAA,KAAKoE,GAAgBO,GAAgB,KAAK,OAAO,GACjD,KAAK,MAAQ/C,EAAO,OAAS1B,EAAA,KAAKkE,IAClC,KAAK,WAAU,CAChB,CACD,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IACrB,CACD,IAAI,SAAU,OACZ,OAAOzE,EAAAO,EAAA,KAAKsE,KAAL,YAAA7E,EAAe,OACvB,CACD,WAAWzC,EAAS,CAClB,KAAK,QAAU,CAAE,GAAGgD,EAAA,KAAKuE,IAAiB,GAAGvH,GAC7C,KAAK,aAAa,KAAK,QAAQ,MAAM,CACtC,CACD,gBAAiB,CACX,CAAC,KAAK,UAAU,QAAU,KAAK,MAAM,cAAgB,QACvDgD,EAAA,KAAKoE,GAAO,OAAO,IAAI,CAE1B,CACD,QAAQM,EAAS1H,EAAS,CACxB,MAAM2B,EAAOF,GAAY,KAAK,MAAM,KAAMiG,EAAS,KAAK,OAAO,EAC/D,OAAAC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CACb,KAAAjG,EACA,KAAM,UACN,cAAe3B,GAAA,YAAAA,EAAS,UACxB,OAAQA,GAAA,YAAAA,EAAS,MACvB,GACW2B,CACR,CACD,SAASkG,EAAOC,EAAiB,CAC/BH,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,WAAY,MAAAC,EAAO,gBAAAC,CAAe,EAC1D,CACD,OAAO9H,EAAS,SACd,MAAM+H,GAAUtF,EAAAO,EAAA,KAAKsE,KAAL,YAAA7E,EAAe,QAC/B,OAAAuF,EAAAhF,EAAA,KAAKsE,KAAL,MAAAU,EAAe,OAAOhI,GACf+H,EAAUA,EAAQ,KAAK5J,CAAI,EAAE,MAAMA,CAAI,EAAI,QAAQ,SAC3D,CACD,SAAU,CACR,MAAM,QAAO,EACb,KAAK,OAAO,CAAE,OAAQ,EAAM,CAAA,CAC7B,CACD,OAAQ,CACN,KAAK,QAAO,EACZ,KAAK,SAAS6E,EAAA,KAAKkE,GAAa,CACjC,CACD,UAAW,CACT,OAAO,KAAK,UAAU,KACnBe,GAAanJ,EAAemJ,EAAS,QAAQ,QAAS,IAAI,IAAM,EACvE,CACG,CACD,YAAa,CACX,OAAI,KAAK,kBAAmB,EAAG,EACtB,CAAC,KAAK,WAER,KAAK,QAAQ,UAAY/F,IAAa,KAAK,MAAM,gBAAkB,KAAK,MAAM,mBAAqB,CAC3G,CACD,UAAW,CACT,OAAI,KAAK,kBAAmB,EAAG,EACtB,KAAK,UAAU,KACnB+F,GAAarJ,GAAiBqJ,EAAS,QAAQ,UAAW,IAAI,IAAM,QAC7E,EAEW,EACR,CACD,SAAU,CACR,OAAI,KAAK,kBAAmB,EAAG,EACtB,KAAK,UAAU,KACnBA,GAAaA,EAAS,iBAAgB,EAAG,OAClD,EAEW,KAAK,MAAM,OAAS,QAAU,KAAK,MAAM,aACjD,CACD,cAActJ,EAAY,EAAG,CAC3B,OAAI,KAAK,MAAM,OAAS,OACf,GAELA,IAAc,SACT,GAEL,KAAK,MAAM,cACN,GAEF,CAACF,GAAe,KAAK,MAAM,cAAeE,CAAS,CAC3D,CACD,SAAU,OACR,MAAMsJ,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,yBAAwB,CAAE,EACxED,GAAA,MAAAA,EAAU,QAAQ,CAAE,cAAe,EAAO,IAC1CxF,EAAAO,EAAA,KAAKsE,KAAL,MAAA7E,EAAe,UAChB,CACD,UAAW,OACT,MAAMwF,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,uBAAsB,CAAE,EACtED,GAAA,MAAAA,EAAU,QAAQ,CAAE,cAAe,EAAO,IAC1CxF,EAAAO,EAAA,KAAKsE,KAAL,MAAA7E,EAAe,UAChB,CACD,YAAYwF,EAAU,CACf,KAAK,UAAU,SAASA,CAAQ,IACnC,KAAK,UAAU,KAAKA,CAAQ,EAC5B,KAAK,eAAc,EACnBjF,EAAA,KAAKoE,GAAO,OAAO,CAAE,KAAM,gBAAiB,MAAO,KAAM,SAAAa,CAAQ,CAAE,EAEtE,CACD,eAAeA,EAAU,CACnB,KAAK,UAAU,SAASA,CAAQ,IAClC,KAAK,UAAY,KAAK,UAAU,OAAQC,GAAMA,IAAMD,CAAQ,EACvD,KAAK,UAAU,SACdjF,EAAA,KAAKsE,KACHtE,EAAA,KAAKwE,IACPxE,EAAA,KAAKsE,GAAS,OAAO,CAAE,OAAQ,EAAM,CAAA,EAErCtE,EAAA,KAAKsE,GAAS,eAGlB,KAAK,WAAU,GAEjBtE,EAAA,KAAKoE,GAAO,OAAO,CAAE,KAAM,kBAAmB,MAAO,KAAM,SAAAa,CAAQ,CAAE,EAExE,CACD,mBAAoB,CAClB,OAAO,KAAK,UAAU,MACvB,CACD,YAAa,CACN,KAAK,MAAM,eACdN,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,YAAc,EAExC,CACD,MAAM5H,EAASoC,EAAc,WAC3B,GAAI,KAAK,MAAM,cAAgB,QAC7B,GAAI,KAAK,MAAM,OAAS,SAAUA,GAAA,MAAAA,EAAc,eAC9C,KAAK,OAAO,CAAE,OAAQ,EAAM,CAAA,UACnBY,EAAA,KAAKsE,GACd,OAAAtE,EAAA,KAAKsE,GAAS,gBACPtE,EAAA,KAAKsE,GAAS,QAMzB,GAHItH,GACF,KAAK,WAAWA,CAAO,EAErB,CAAC,KAAK,QAAQ,QAAS,CACzB,MAAMiI,EAAW,KAAK,UAAU,KAAMC,GAAMA,EAAE,QAAQ,OAAO,EACzDD,GACF,KAAK,WAAWA,EAAS,OAAO,CAEnC,CAQD,MAAME,EAAkB,IAAI,gBACtBC,EAAqBC,GAAW,CACpC,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,KACHvF,EAAA,KAAK0E,GAAuB,IACrBW,EAAgB,OAEjC,CAAO,CACP,EACUG,EAAU,IAAM,CACpB,MAAMC,EAAUpG,GAAc,KAAK,QAASC,CAAY,EAUlDoG,GATuB,IAAM,CACjC,MAAMC,EAAkB,CACtB,OAAQzF,EAAA,KAAKqE,IACb,SAAU,KAAK,SACf,KAAM,KAAK,IACrB,EACQ,OAAAe,EAAkBK,CAAe,EAC1BA,CACf,KAGM,OADA3F,EAAA,KAAK0E,GAAuB,IACxB,KAAK,QAAQ,UACR,KAAK,QAAQ,UAClBe,EACAC,EACA,IACV,EAEaD,EAAQC,CAAc,CACnC,EAaUE,GAZqB,IAAM,CAC/B,MAAMC,EAAW,CACf,aAAAvG,EACA,QAAS,KAAK,QACd,SAAU,KAAK,SACf,OAAQY,EAAA,KAAKqE,IACb,MAAO,KAAK,MACZ,QAAAiB,CACR,EACM,OAAAF,EAAkBO,CAAQ,EACnBA,CACb,MAEIlG,EAAA,KAAK,QAAQ,WAAb,MAAAA,EAAuB,QAAQiG,EAAS,MACxC5F,EAAA,KAAKqE,GAAe,KAAK,QACrB,KAAK,MAAM,cAAgB,QAAU,KAAK,MAAM,cAAca,EAAAU,EAAQ,eAAR,YAAAV,EAAsB,QACtFL,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,QAAS,MAAMgB,EAAAF,EAAQ,eAAR,YAAAE,EAAsB,IAAI,GAElE,MAAMC,EAAWpD,GAAU,aACnBjB,GAAiBiB,CAAK,GAAKA,EAAM,QACrCkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CACb,KAAM,QACN,MAAAnC,CACV,GAEWjB,GAAiBiB,CAAK,KACzBuC,GAAAvF,EAAAO,EAAA,KAAKoE,GAAO,QAAO,UAAnB,MAAAY,EAAA,KAAAvF,EACEgD,EACA,OAEFqD,GAAAF,EAAA5F,EAAA,KAAKoE,GAAO,QAAO,YAAnB,MAAA0B,EAAA,KAAAF,EACE,KAAK,MAAM,KACXnD,EACA,OAGJ,KAAK,WAAU,CACrB,EACI,OAAA3C,EAAA,KAAKwE,EAAW7C,GAAc,CAC5B,eAAgBrC,GAAA,YAAAA,EAAc,eAC9B,GAAIsG,EAAQ,QACZ,MAAOP,EAAgB,MAAM,KAAKA,CAAe,EACjD,UAAYxG,GAAS,aACnB,GAAIA,IAAS,OAAQ,CAMnBkH,EAAQ,IAAI,MAAM,GAAG,KAAK,SAAS,oBAAoB,CAAC,EACxD,MACD,CACD,GAAI,CACF,KAAK,QAAQlH,CAAI,CAClB,OAAQ8D,EAAO,CACdoD,EAAQpD,CAAK,EACb,MACD,EACDuC,GAAAvF,EAAAO,EAAA,KAAKoE,GAAO,QAAO,YAAnB,MAAAY,EAAA,KAAAvF,EAA+Bd,EAAM,OACrCmH,GAAAF,EAAA5F,EAAA,KAAKoE,GAAO,QAAO,YAAnB,MAAA0B,EAAA,KAAAF,EACEjH,EACA,KAAK,MAAM,MACX,MAEF,KAAK,WAAU,CAChB,EACD,QAAAkH,EACA,OAAQ,CAACzE,EAAcqB,IAAU,CAC/BkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,SAAU,aAAAxD,EAAc,MAAAqB,CAAK,EACrD,EACD,QAAS,IAAM,CACbkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,OAAS,EACjC,EACD,WAAY,IAAM,CAChBD,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,UAAY,EACpC,EACD,MAAOc,EAAQ,QAAQ,MACvB,WAAYA,EAAQ,QAAQ,WAC5B,YAAaA,EAAQ,QAAQ,YAC7B,OAAQ,IAAM,EACpB,CAAK,GACM1F,EAAA,KAAKsE,GAAS,OACtB,CA6EH,EAtWEJ,GAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,GAAA,YAoRAP,EAAA,YAAAW,EAAS,SAACmB,EAAQ,CAChB,MAAMC,EAAWnB,GAAU,CACzB,OAAQkB,EAAO,KAAI,CACjB,IAAK,SACH,MAAO,CACL,GAAGlB,EACH,kBAAmBkB,EAAO,aAC1B,mBAAoBA,EAAO,KACvC,EACQ,IAAK,QACH,MAAO,CACL,GAAGlB,EACH,YAAa,QACzB,EACQ,IAAK,WACH,MAAO,CACL,GAAGA,EACH,YAAa,UACzB,EACQ,IAAK,QACH,MAAO,CACL,GAAGA,EACH,GAAGoB,GAAWpB,EAAM,KAAM,KAAK,OAAO,EACtC,UAAWkB,EAAO,MAAQ,IACtC,EACQ,IAAK,UACH,OAAAjG,EAAA,KAAKqE,GAAe,QACb,CACL,GAAGU,EACH,KAAMkB,EAAO,KACb,gBAAiBlB,EAAM,gBAAkB,EACzC,cAAekB,EAAO,eAAiB,KAAK,IAAK,EACjD,MAAO,KACP,cAAe,GACf,OAAQ,UACR,GAAG,CAACA,EAAO,QAAU,CACnB,YAAa,OACb,kBAAmB,EACnB,mBAAoB,IACrB,CACb,EACQ,IAAK,QACH,MAAMtD,EAAQsD,EAAO,MACrB,OAAIvE,GAAiBiB,CAAK,GAAKA,EAAM,QAAUzC,EAAA,KAAKmE,IAC3C,CAAE,GAAGnE,EAAA,KAAKmE,IAAc,YAAa,MAAM,EAE7C,CACL,GAAGU,EACH,MAAApC,EACA,iBAAkBoC,EAAM,iBAAmB,EAC3C,eAAgB,KAAK,IAAK,EAC1B,kBAAmBA,EAAM,kBAAoB,EAC7C,mBAAoBpC,EACpB,YAAa,OACb,OAAQ,OACpB,EACQ,IAAK,aACH,MAAO,CACL,GAAGoC,EACH,cAAe,EAC3B,EACQ,IAAK,WACH,MAAO,CACL,GAAGA,EACH,GAAGkB,EAAO,KACtB,CACO,CACP,EACI,KAAK,MAAQC,EAAQ,KAAK,KAAK,EAC/BpC,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAASqB,GAAa,CACnCA,EAAS,cAAa,CAC9B,CAAO,EACDjF,EAAA,KAAKoE,GAAO,OAAO,CAAE,MAAO,KAAM,KAAM,UAAW,OAAA2B,CAAM,CAAE,CACjE,CAAK,CACF,EAtWStG,IAwWZ,SAASwG,GAAWtH,EAAM3B,EAAS,CACjC,MAAO,CACL,kBAAmB,EACnB,mBAAoB,KACpB,YAAaqE,GAASrE,EAAQ,WAAW,EAAI,WAAa,SAC1D,GAAG2B,IAAS,QAAU,CACpB,MAAO,KACP,OAAQ,SACT,CACL,CACA,CACA,SAAS8F,GAAgBzH,EAAS,CAChC,MAAM2B,EAAO,OAAO3B,EAAQ,aAAgB,WAAaA,EAAQ,YAAW,EAAKA,EAAQ,YACnFkJ,EAAUvH,IAAS,OACnBwH,EAAuBD,EAAU,OAAOlJ,EAAQ,sBAAyB,WAAaA,EAAQ,qBAAsB,EAAGA,EAAQ,qBAAuB,EAC5J,MAAO,CACL,KAAA2B,EACA,gBAAiB,EACjB,cAAeuH,EAAUC,GAAwB,KAAK,IAAK,EAAG,EAC9D,MAAO,KACP,iBAAkB,EAClB,eAAgB,EAChB,kBAAmB,EACnB,mBAAoB,KACpB,UAAW,KACX,cAAe,GACf,OAAQD,EAAU,UAAY,UAC9B,YAAa,MACjB,CACA,UC7YIE,IAAa3G,GAAA,cAAczE,EAAa,CAC1C,YAAY0G,EAAS,GAAI,CACvB,QAIFhC,EAAA,KAAA2G,EAAA,QAHE,KAAK,OAAS3E,EACd5B,EAAA,KAAKuG,EAA2B,IAAI,IACrC,CAED,MAAMC,EAAQtJ,EAAS6H,EAAO,CAC5B,MAAMvI,EAAWU,EAAQ,SACnBuJ,EAAYvJ,EAAQ,WAAaR,GAAsBF,EAAUU,CAAO,EAC9E,IAAInB,EAAQ,KAAK,IAAI0K,CAAS,EAC9B,OAAK1K,IACHA,EAAQ,IAAImI,GAAM,CAChB,OAAAsC,EACA,SAAAhK,EACA,UAAAiK,EACA,QAASD,EAAO,oBAAoBtJ,CAAO,EAC3C,MAAA6H,EACA,eAAgByB,EAAO,iBAAiBhK,CAAQ,CACxD,CAAO,EACD,KAAK,IAAIT,CAAK,GAETA,CACR,CACD,IAAIA,EAAO,CACJmE,EAAA,KAAKqG,GAAS,IAAIxK,EAAM,SAAS,IACpCmE,EAAA,KAAKqG,GAAS,IAAIxK,EAAM,UAAWA,CAAK,EACxC,KAAK,OAAO,CACV,KAAM,QACN,MAAAA,CACR,CAAO,EAEJ,CACD,OAAOA,EAAO,CACZ,MAAM2K,EAAaxG,EAAA,KAAKqG,GAAS,IAAIxK,EAAM,SAAS,EAChD2K,IACF3K,EAAM,QAAO,EACT2K,IAAe3K,GACjBmE,EAAA,KAAKqG,GAAS,OAAOxK,EAAM,SAAS,EAEtC,KAAK,OAAO,CAAE,KAAM,UAAW,MAAAA,CAAO,CAAA,EAEzC,CACD,OAAQ,CACN+H,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS/H,GAAU,CAC/B,KAAK,OAAOA,CAAK,CACzB,CAAO,CACP,CAAK,CACF,CACD,IAAI0K,EAAW,CACb,OAAOvG,EAAA,KAAKqG,GAAS,IAAIE,CAAS,CACnC,CACD,QAAS,CACP,MAAO,CAAC,GAAGvG,EAAA,KAAKqG,GAAS,OAAQ,CAAA,CAClC,CACD,KAAKpK,EAAS,CACZ,MAAMwK,EAAmB,CAAE,MAAO,GAAM,GAAGxK,CAAO,EAClD,OAAO,KAAK,OAAM,EAAG,KAClBJ,GAAUG,GAAWyK,EAAkB5K,CAAK,CACnD,CACG,CACD,QAAQI,EAAU,GAAI,CACpB,MAAMyK,EAAU,KAAK,SACrB,OAAO,OAAO,KAAKzK,CAAO,EAAE,OAAS,EAAIyK,EAAQ,OAAQ7K,GAAUG,GAAWC,EAASJ,CAAK,CAAC,EAAI6K,CAClG,CACD,OAAOC,EAAO,CACZ/C,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAS3I,GAAa,CACnCA,EAAS0L,CAAK,CACtB,CAAO,CACP,CAAK,CACF,CACD,SAAU,CACR/C,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS/H,GAAU,CAC/BA,EAAM,QAAO,CACrB,CAAO,CACP,CAAK,CACF,CACD,UAAW,CACT+H,EAAc,MAAM,IAAM,CACxB,KAAK,OAAM,EAAG,QAAS/H,GAAU,CAC/BA,EAAM,SAAQ,CACtB,CAAO,CACP,CAAK,CACF,CACH,EAjFEwK,EAAA,YANe5G,kBCDbmH,IAAWnH,GAAA,cAAcoE,EAAU,CAIrC,YAAYnC,EAAQ,CAClB,QAgJFhC,EAAA,KAAAuE,GApJAvE,EAAA,KAAAmH,EAAA,QACAnH,EAAA,KAAAoH,EAAA,QACApH,EAAA,KAAA4E,GAAA,QAGE,KAAK,WAAa5C,EAAO,WACzB5B,EAAA,KAAKgH,EAAiBpF,EAAO,eAC7B5B,EAAA,KAAK+G,EAAa,IAClB,KAAK,MAAQnF,EAAO,OAAS+C,GAAe,EAC5C,KAAK,WAAW/C,EAAO,OAAO,EAC9B,KAAK,WAAU,CAChB,CACD,WAAW1E,EAAS,CAClB,KAAK,QAAUA,EACf,KAAK,aAAa,KAAK,QAAQ,MAAM,CACtC,CACD,IAAI,MAAO,CACT,OAAO,KAAK,QAAQ,IACrB,CACD,YAAYiI,EAAU,CACfjF,EAAA,KAAK6G,GAAW,SAAS5B,CAAQ,IACpCjF,EAAA,KAAK6G,GAAW,KAAK5B,CAAQ,EAC7B,KAAK,eAAc,EACnBjF,EAAA,KAAK8G,GAAe,OAAO,CACzB,KAAM,gBACN,SAAU,KACV,SAAA7B,CACR,CAAO,EAEJ,CACD,eAAeA,EAAU,CACvBnF,EAAA,KAAK+G,EAAa7G,EAAA,KAAK6G,GAAW,OAAQ3B,GAAMA,IAAMD,CAAQ,GAC9D,KAAK,WAAU,EACfjF,EAAA,KAAK8G,GAAe,OAAO,CACzB,KAAM,kBACN,SAAU,KACV,SAAA7B,CACN,CAAK,CACF,CACD,gBAAiB,CACVjF,EAAA,KAAK6G,GAAW,SACf,KAAK,MAAM,SAAW,UACxB,KAAK,WAAU,EAEf7G,EAAA,KAAK8G,GAAe,OAAO,IAAI,EAGpC,CACD,UAAW,OACT,QAAOrH,EAAAO,EAAA,KAAKsE,MAAL,YAAA7E,EAAe,aACtB,KAAK,QAAQ,KAAK,MAAM,SAAS,CAClC,CACD,MAAM,QAAQsH,EAAW,8CACvB,MAAMC,EAAa,IAAM,CACvBrC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,UAAY,EACzC,EACI9E,EAAA,KAAKwE,GAAW7C,GAAc,CAC5B,GAAI,IACG,KAAK,QAAQ,WAGX,KAAK,QAAQ,WAAWsF,CAAS,EAF/B,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC,EAI1D,OAAQ,CAAC3F,EAAcqB,IAAU,CAC/BkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,SAAU,aAAAxD,EAAc,MAAAqB,CAAK,EACrD,EACD,QAAS,IAAM,CACbkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,OAAS,EACjC,EACD,WAAAoC,EACA,MAAO,KAAK,QAAQ,OAAS,EAC7B,WAAY,KAAK,QAAQ,WACzB,YAAa,KAAK,QAAQ,YAC1B,OAAQ,IAAMhH,EAAA,KAAK8G,GAAe,OAAO,IAAI,CACnD,CAAK,GACD,MAAMG,EAAW,KAAK,MAAM,SAAW,UACjCC,EAAW,CAAClH,EAAA,KAAKsE,IAAS,SAAQ,EACxC,GAAI,CACF,GAAI2C,EACFD,QACK,CACLrC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,UAAW,UAAAmC,EAAW,SAAAG,CAAQ,GACrD,OAAMlC,GAAAvF,EAAAO,EAAA,KAAK8G,GAAe,QAAO,WAA3B,YAAA9B,EAAA,KAAAvF,EACJsH,EACA,OAEF,MAAMrB,EAAU,OAAMI,GAAAF,EAAA,KAAK,SAAQ,WAAb,YAAAE,EAAA,KAAAF,EAAwBmB,IAC1CrB,IAAY,KAAK,MAAM,SACzBf,EAAA,KAAKV,EAAAW,GAAL,UAAe,CACb,KAAM,UACN,QAAAc,EACA,UAAAqB,EACA,SAAAG,CACZ,EAEO,CACD,MAAMvI,EAAO,MAAMqB,EAAA,KAAKsE,IAAS,MAAK,EACtC,cAAM6C,GAAAC,EAAApH,EAAA,KAAK8G,GAAe,QAAO,YAA3B,YAAAK,EAAA,KAAAC,EACJzI,EACAoI,EACA,KAAK,MAAM,QACX,OAEF,OAAMM,GAAAC,EAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,EAAyB3I,EAAMoI,EAAW,KAAK,MAAM,UAC3D,OAAMQ,GAAAC,EAAAxH,EAAA,KAAK8G,GAAe,QAAO,YAA3B,YAAAS,EAAA,KAAAC,EACJ7I,EACA,KACA,KAAK,MAAM,UACX,KAAK,MAAM,QACX,OAEF,OAAM8I,GAAAC,EAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,EAAyB/I,EAAM,KAAMoI,EAAW,KAAK,MAAM,UACjEpC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,UAAW,KAAAjG,CAAM,GACjCA,CACR,OAAQ8D,EAAO,CACd,GAAI,CACF,aAAMkF,GAAAC,EAAA5H,EAAA,KAAK8G,GAAe,QAAO,UAA3B,YAAAa,EAAA,KAAAC,EACJnF,EACAsE,EACA,KAAK,MAAM,QACX,OAEF,OAAMc,GAAAC,EAAA,KAAK,SAAQ,UAAb,YAAAD,EAAA,KAAAC,EACJrF,EACAsE,EACA,KAAK,MAAM,UAEb,OAAMgB,GAAAC,EAAAhI,EAAA,KAAK8G,GAAe,QAAO,YAA3B,YAAAiB,EAAA,KAAAC,EACJ,OACAvF,EACA,KAAK,MAAM,UACX,KAAK,MAAM,QACX,OAEF,OAAMwF,GAAAC,GAAA,KAAK,SAAQ,YAAb,YAAAD,EAAA,KAAAC,GACJ,OACAzF,EACAsE,EACA,KAAK,MAAM,UAEPtE,CACd,QAAgB,CACRkC,EAAA,KAAKV,EAAAW,GAAL,UAAe,CAAE,KAAM,QAAS,MAAAnC,CAAO,EACxC,CACP,QAAc,CACRzC,EAAA,KAAK8G,GAAe,QAAQ,IAAI,CACjC,CACF,CAmEH,EAtNED,EAAA,YACAC,EAAA,YACAxC,GAAA,YAkJAL,EAAA,YAAAW,EAAS,SAACmB,EAAQ,CAChB,MAAMC,EAAWnB,GAAU,CACzB,OAAQkB,EAAO,KAAI,CACjB,IAAK,SACH,MAAO,CACL,GAAGlB,EACH,aAAckB,EAAO,aACrB,cAAeA,EAAO,KAClC,EACQ,IAAK,QACH,MAAO,CACL,GAAGlB,EACH,SAAU,EACtB,EACQ,IAAK,WACH,MAAO,CACL,GAAGA,EACH,SAAU,EACtB,EACQ,IAAK,UACH,MAAO,CACL,GAAGA,EACH,QAASkB,EAAO,QAChB,KAAM,OACN,aAAc,EACd,cAAe,KACf,MAAO,KACP,SAAUA,EAAO,SACjB,OAAQ,UACR,UAAWA,EAAO,UAClB,YAAa,KAAK,IAAK,CACnC,EACQ,IAAK,UACH,MAAO,CACL,GAAGlB,EACH,KAAMkB,EAAO,KACb,aAAc,EACd,cAAe,KACf,MAAO,KACP,OAAQ,UACR,SAAU,EACtB,EACQ,IAAK,QACH,MAAO,CACL,GAAGlB,EACH,KAAM,OACN,MAAOkB,EAAO,MACd,aAAclB,EAAM,aAAe,EACnC,cAAekB,EAAO,MACtB,SAAU,GACV,OAAQ,OACpB,CACO,CACP,EACI,KAAK,MAAQC,EAAQ,KAAK,KAAK,EAC/BpC,EAAc,MAAM,IAAM,CACxB5D,EAAA,KAAK6G,GAAW,QAAS5B,GAAa,CACpCA,EAAS,iBAAiBc,CAAM,CACxC,CAAO,EACD/F,EAAA,KAAK8G,GAAe,OAAO,CACzB,SAAU,KACV,KAAM,UACN,OAAAf,CACR,CAAO,CACP,CAAK,CACF,EAtNYtG,IAwNf,SAASgF,IAAkB,CACzB,MAAO,CACL,QAAS,OACT,KAAM,OACN,MAAO,KACP,aAAc,EACd,cAAe,KACf,SAAU,GACV,OAAQ,OACR,UAAW,OACX,YAAa,CACjB,CACA,eCnOI0D,IAAgB1I,GAAA,cAAczE,EAAa,CAC7C,YAAY0G,EAAS,GAAI,CACvB,QAMFhC,EAAA,KAAA0I,EAAA,QACA1I,EAAA,KAAA2I,EAAA,QACA3I,EAAA,KAAA4I,GAAA,QAPE,KAAK,OAAS5G,EACd5B,EAAA,KAAKsI,EAA6B,IAAI,KACtCtI,EAAA,KAAKuI,EAA0B,IAAI,KACnCvI,EAAA,KAAKwI,GAAc,EACpB,CAID,MAAMhC,EAAQtJ,EAAS6H,EAAO,CAC5B,MAAMjI,EAAW,IAAIgK,GAAS,CAC5B,cAAe,KACf,WAAmB,EAAL2B,GAAA,KAAKD,IAAL,EACd,QAAShC,EAAO,uBAAuBtJ,CAAO,EAC9C,MAAA6H,CACN,CAAK,EACD,YAAK,IAAIjI,CAAQ,EACVA,CACR,CACD,IAAIA,EAAU,CACZoD,EAAA,KAAKoI,GAAW,IAAIxL,CAAQ,EAC5B,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAME,EAAkB1I,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,EAC1CE,EACFA,EAAgB,KAAK9L,CAAQ,EAE7BoD,EAAA,KAAKqI,GAAQ,IAAIG,EAAO,CAAC5L,CAAQ,CAAC,CAErC,CACD,KAAK,OAAO,CAAE,KAAM,QAAS,SAAAA,CAAU,CAAA,CACxC,CACD,OAAOA,EAAU,CACf,GAAIoD,EAAA,KAAKoI,GAAW,OAAOxL,CAAQ,EAAG,CACpC,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAME,EAAkB1I,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,EAC9C,GAAIE,EACF,GAAIA,EAAgB,OAAS,EAAG,CAC9B,MAAMC,EAAQD,EAAgB,QAAQ9L,CAAQ,EAC1C+L,IAAU,IACZD,EAAgB,OAAOC,EAAO,CAAC,CAElC,MAAUD,EAAgB,CAAC,IAAM9L,GAChCoD,EAAA,KAAKqI,GAAQ,OAAOG,CAAK,CAG9B,CACF,CACD,KAAK,OAAO,CAAE,KAAM,UAAW,SAAA5L,CAAU,CAAA,CAC1C,CACD,OAAOA,EAAU,CACf,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAMI,EAAyB5I,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,EAC/CK,EAAuBD,GAAA,YAAAA,EAAwB,KAClDxO,GAAMA,EAAE,MAAM,SAAW,WAE5B,MAAO,CAACyO,GAAwBA,IAAyBjM,CAC/D,KACM,OAAO,EAEV,CACD,QAAQA,EAAU,OAChB,MAAM4L,EAAQC,GAAS7L,CAAQ,EAC/B,GAAI,OAAO4L,GAAU,SAAU,CAC7B,MAAMM,GAAgBrJ,EAAAO,EAAA,KAAKqI,GAAQ,IAAIG,CAAK,IAAtB,YAAA/I,EAAyB,KAAMrF,GAAMA,IAAMwC,GAAYxC,EAAE,MAAM,UACrF,OAAO0O,GAAA,YAAAA,EAAe,aAAc,QAAQ,QAAO,CACzD,KACM,QAAO,QAAQ,SAElB,CACD,OAAQ,CACNlF,EAAc,MAAM,IAAM,CACxB5D,EAAA,KAAKoI,GAAW,QAASxL,GAAa,CACpC,KAAK,OAAO,CAAE,KAAM,UAAW,SAAAA,CAAU,CAAA,CACjD,CAAO,EACDoD,EAAA,KAAKoI,GAAW,QAChBpI,EAAA,KAAKqI,GAAQ,OACnB,CAAK,CACF,CACD,QAAS,CACP,OAAO,MAAM,KAAKrI,EAAA,KAAKoI,EAAU,CAClC,CACD,KAAKnM,EAAS,CACZ,MAAMwK,EAAmB,CAAE,MAAO,GAAM,GAAGxK,CAAO,EAClD,OAAO,KAAK,OAAM,EAAG,KAClBW,GAAaD,GAAc8J,EAAkB7J,CAAQ,CAC5D,CACG,CACD,QAAQX,EAAU,GAAI,CACpB,OAAO,KAAK,OAAQ,EAAC,OAAQW,GAAaD,GAAcV,EAASW,CAAQ,CAAC,CAC3E,CACD,OAAO+J,EAAO,CACZ/C,EAAc,MAAM,IAAM,CACxB,KAAK,UAAU,QAAS3I,GAAa,CACnCA,EAAS0L,CAAK,CACtB,CAAO,CACP,CAAK,CACF,CACD,uBAAwB,CACtB,MAAMoC,EAAkB,KAAK,SAAS,OAAQ7D,GAAMA,EAAE,MAAM,QAAQ,EACpE,OAAOtB,EAAc,MACnB,IAAM,QAAQ,IACZmF,EAAgB,IAAKnM,GAAaA,EAAS,WAAW,MAAMzB,CAAI,CAAC,CAClE,CACP,CACG,CACH,EAtGEiN,EAAA,YACAC,EAAA,YACAC,GAAA,YAVkB7I,IA+GpB,SAASgJ,GAAS7L,EAAU,OAC1B,OAAO6C,EAAA7C,EAAS,QAAQ,QAAjB,YAAA6C,EAAwB,EACjC,CCpHA,SAASuJ,GAAsBC,EAAO,CACpC,MAAO,CACL,QAAS,CAACvD,EAAS7J,IAAU,eAC3B,MAAMmB,EAAU0I,EAAQ,QAClBwD,GAAYtD,GAAAZ,GAAAvF,EAAAiG,EAAQ,eAAR,YAAAjG,EAAsB,OAAtB,YAAAuF,EAA4B,YAA5B,YAAAY,EAAuC,UACnDuD,IAAWrD,EAAAJ,EAAQ,MAAM,OAAd,YAAAI,EAAoB,QAAS,CAAA,EACxCsD,IAAgBhC,EAAA1B,EAAQ,MAAM,OAAd,YAAA0B,EAAoB,aAAc,CAAA,EACxD,IAAIhK,EAAS,CAAE,MAAO,CAAE,EAAE,WAAY,CAAE,CAAA,EACpCiM,EAAc,EAClB,MAAM/D,EAAU,SAAY,CAC1B,IAAIgE,EAAY,GAChB,MAAMlE,EAAqBC,GAAW,CACpC,OAAO,eAAeA,EAAQ,SAAU,CACtC,WAAY,GACZ,IAAK,KACCK,EAAQ,OAAO,QACjB4D,EAAY,GAEZ5D,EAAQ,OAAO,iBAAiB,QAAS,IAAM,CAC7C4D,EAAY,EAC9B,CAAiB,EAEI5D,EAAQ,OAE7B,CAAW,CACX,EACcH,EAAUpG,GAAcuG,EAAQ,QAASA,EAAQ,YAAY,EAC7D6D,EAAY,MAAO5K,EAAM6K,EAAOC,IAAa,CACjD,GAAIH,EACF,OAAO,QAAQ,SAEjB,GAAIE,GAAS,MAAQ7K,EAAK,MAAM,OAC9B,OAAO,QAAQ,QAAQA,CAAI,EAa7B,MAAM6G,IAXuB,IAAM,CACjC,MAAMC,GAAkB,CACtB,OAAQC,EAAQ,OAChB,SAAUA,EAAQ,SAClB,UAAW8D,EACX,UAAWC,EAAW,WAAa,UACnC,KAAM/D,EAAQ,QAAQ,IACpC,EACY,OAAAN,EAAkBK,EAAe,EAC1BA,EACnB,KAEgBiE,EAAO,MAAMnE,EAAQC,EAAc,EACnC,CAAE,SAAAmE,CAAQ,EAAKjE,EAAQ,QACvBkE,EAAQH,EAAWxK,GAAaL,GACtC,MAAO,CACL,MAAOgL,EAAMjL,EAAK,MAAO+K,EAAMC,CAAQ,EACvC,WAAYC,EAAMjL,EAAK,WAAY6K,EAAOG,CAAQ,CAC9D,CACA,EACQ,GAAIT,GAAaC,EAAS,OAAQ,CAChC,MAAMM,EAAWP,IAAc,WACzBW,EAAcJ,EAAWK,GAAuBC,GAChDC,EAAU,CACd,MAAOb,EACP,WAAYC,CACxB,EACgBI,EAAQK,EAAY7M,EAASgN,CAAO,EAC1C5M,EAAS,MAAMmM,EAAUS,EAASR,EAAOC,CAAQ,CAC3D,KAAe,CACL,MAAMQ,EAAiBhB,GAASE,EAAS,OACzC,EAAG,CACD,MAAMK,EAAQH,IAAgB,EAAID,EAAc,CAAC,GAAKpM,EAAQ,iBAAmB+M,GAAiB/M,EAASI,CAAM,EACjH,GAAIiM,EAAc,GAAKG,GAAS,KAC9B,MAEFpM,EAAS,MAAMmM,EAAUnM,EAAQoM,CAAK,EACtCH,GACZ,OAAmBA,EAAcY,EACxB,CACD,OAAO7M,CACf,EACUsI,EAAQ,QAAQ,UAClBA,EAAQ,QAAU,IAAM,SACtB,OAAOV,GAAAvF,EAAAiG,EAAQ,SAAQ,YAAhB,YAAAV,EAAA,KAAAvF,EACL6F,EACA,CACE,OAAQI,EAAQ,OAChB,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,QAAQ,KACtB,OAAQA,EAAQ,MACjB,EACD7J,EAEZ,EAEQ6J,EAAQ,QAAUJ,CAErB,CACL,CACA,CACA,SAASyE,GAAiB/M,EAAS,CAAE,MAAAiM,EAAO,WAAAiB,CAAU,EAAI,CACxD,MAAMC,EAAYlB,EAAM,OAAS,EACjC,OAAOA,EAAM,OAAS,EAAIjM,EAAQ,iBAChCiM,EAAMkB,CAAS,EACflB,EACAiB,EAAWC,CAAS,EACpBD,CACD,EAAG,MACN,CACA,SAASJ,GAAqB9M,EAAS,CAAE,MAAAiM,EAAO,WAAAiB,CAAU,EAAI,OAC5D,OAAOjB,EAAM,OAAS,GAAIxJ,EAAAzC,EAAQ,uBAAR,YAAAyC,EAAA,KAAAzC,EAA+BiM,EAAM,CAAC,EAAGA,EAAOiB,EAAW,CAAC,EAAGA,GAAc,MACzG,4BC5FIE,IAAc3K,GAAA,KAAM,CAStB,YAAYiC,EAAS,GAAI,CARzBhC,EAAA,KAAA2K,EAAA,QACA3K,EAAA,KAAAoH,EAAA,QACApH,EAAA,KAAA6E,EAAA,QACA7E,EAAA,KAAA4K,GAAA,QACA5K,EAAA,KAAA6K,GAAA,QACA7K,EAAA,KAAA8K,EAAA,QACA9K,EAAA,KAAA+K,GAAA,QACA/K,EAAA,KAAAgL,GAAA,QAEE5K,EAAA,KAAKuK,EAAc3I,EAAO,YAAc,IAAI0E,IAC5CtG,EAAA,KAAKgH,EAAiBpF,EAAO,eAAiB,IAAIyG,IAClDrI,EAAA,KAAKyE,EAAkB7C,EAAO,gBAAkB,CAAA,GAChD5B,EAAA,KAAKwK,GAAiC,IAAI,KAC1CxK,EAAA,KAAKyK,GAAoC,IAAI,KAC7CzK,EAAA,KAAK0K,EAAc,EACpB,CACD,OAAQ,CACNjC,GAAA,KAAKiC,GAAL,IACIxK,EAAA,KAAKwK,KAAgB,IACzB1K,EAAA,KAAK2K,GAAoBrK,GAAa,UAAU,MAAOF,GAAY,CAC7DA,IACF,MAAM,KAAK,wBACXF,EAAA,KAAKqK,GAAY,UAEzB,CAAK,GACDvK,EAAA,KAAK4K,GAAqB/J,GAAc,UAAU,MAAOD,GAAW,CAC9DA,IACF,MAAM,KAAK,wBACXV,EAAA,KAAKqK,GAAY,WAEzB,CAAK,GACF,CACD,SAAU,SACR9B,GAAA,KAAKiC,GAAL,IACIxK,EAAA,KAAKwK,KAAgB,KACzB/K,EAAAO,EAAA,KAAKyK,MAAL,MAAAhL,EAAA,WACAK,EAAA,KAAK2K,GAAoB,SACzBzF,EAAAhF,EAAA,KAAK0K,MAAL,MAAA1F,EAAA,WACAlF,EAAA,KAAK4K,GAAqB,QAC3B,CACD,WAAWzO,EAAS,CAClB,OAAO+D,EAAA,KAAKqK,GAAY,QAAQ,CAAE,GAAGpO,EAAS,YAAa,WAAY,EAAE,MAC1E,CACD,WAAWA,EAAS,CAClB,OAAO+D,EAAA,KAAK8G,GAAe,QAAQ,CAAE,GAAG7K,EAAS,OAAQ,UAAW,EAAE,MACvE,CAQD,aAAaK,EAAU,OACrB,MAAMU,EAAU,KAAK,oBAAoB,CAAE,SAAAV,CAAU,CAAA,EACrD,OAAOmD,EAAAO,EAAA,KAAKqK,GAAY,IAAIrN,EAAQ,SAAS,IAAtC,YAAAyC,EAAyC,MAAM,IACvD,CACD,gBAAgBzC,EAAS,CACvB,MAAM2N,EAAmB,KAAK,oBAAoB3N,CAAO,EACnDnB,EAAQmE,EAAA,KAAKqK,GAAY,MAAM,KAAMM,CAAgB,EACrDC,EAAa/O,EAAM,MAAM,KAC/B,OAAI+O,IAAe,OACV,KAAK,WAAW5N,CAAO,GAE5BA,EAAQ,mBAAqBnB,EAAM,cAAcD,GAAiB+O,EAAiB,UAAW9O,CAAK,CAAC,GACjG,KAAK,cAAc8O,CAAgB,EAEnC,QAAQ,QAAQC,CAAU,EAClC,CACD,eAAe3O,EAAS,CACtB,OAAO+D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,IAAI,CAAC,CAAE,SAAAK,EAAU,MAAAuI,KAAY,CACpE,MAAMlG,EAAOkG,EAAM,KACnB,MAAO,CAACvI,EAAUqC,CAAI,CAC5B,CAAK,CACF,CACD,aAAarC,EAAUjB,EAAS2B,EAAS,CACvC,MAAM2N,EAAmB,KAAK,oBAAoB,CAAE,SAAArO,CAAU,CAAA,EACxDT,EAAQmE,EAAA,KAAKqK,GAAY,IAC7BM,EAAiB,SACvB,EACUjM,EAAW7C,GAAA,YAAAA,EAAO,MAAM,KACxB8C,EAAOvD,GAAiBC,EAASqD,CAAQ,EAC/C,GAAIC,IAAS,OAGb,OAAOqB,EAAA,KAAKqK,GAAY,MAAM,KAAMM,CAAgB,EAAE,QAAQhM,EAAM,CAAE,GAAG3B,EAAS,OAAQ,EAAM,CAAA,CACjG,CACD,eAAef,EAASZ,EAAS2B,EAAS,CACxC,OAAO4G,EAAc,MACnB,IAAM5D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,IAAI,CAAC,CAAE,SAAAK,KAAe,CAC5DA,EACA,KAAK,aAAaA,EAAUjB,EAAS2B,CAAO,CACpD,CAAO,CACP,CACG,CACD,cAAcV,EAAU,OACtB,MAAMU,EAAU,KAAK,oBAAoB,CAAE,SAAAV,CAAU,CAAA,EACrD,OAAOmD,EAAAO,EAAA,KAAKqK,GAAY,IACtBrN,EAAQ,SACT,IAFM,YAAAyC,EAEJ,KACJ,CACD,cAAcxD,EAAS,CACrB,MAAM4O,EAAa7K,EAAA,KAAKqK,GACxBzG,EAAc,MAAM,IAAM,CACxBiH,EAAW,QAAQ5O,CAAO,EAAE,QAASJ,GAAU,CAC7CgP,EAAW,OAAOhP,CAAK,CAC/B,CAAO,CACP,CAAK,CACF,CACD,aAAaI,EAASe,EAAS,CAC7B,MAAM6N,EAAa7K,EAAA,KAAKqK,GACxB,OAAOzG,EAAc,MAAM,KACzBiH,EAAW,QAAQ5O,CAAO,EAAE,QAASJ,GAAU,CAC7CA,EAAM,MAAK,CACnB,CAAO,EACM,KAAK,eACV,CACE,KAAM,SACN,GAAGI,CACJ,EACDe,CACR,EACK,CACF,CACD,cAAcf,EAAS8F,EAAgB,GAAI,CACzC,MAAM+I,EAAyB,CAAE,OAAQ,GAAM,GAAG/I,CAAa,EACzDgJ,EAAWnH,EAAc,MAC7B,IAAM5D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,IAAKJ,GAAUA,EAAM,OAAOiP,CAAsB,CAAC,CACjG,EACI,OAAO,QAAQ,IAAIC,CAAQ,EAAE,KAAK5P,CAAI,EAAE,MAAMA,CAAI,CACnD,CACD,kBAAkBc,EAASe,EAAU,GAAI,CACvC,OAAO4G,EAAc,MAAM,KACzB5D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,QAASJ,GAAU,CACnDA,EAAM,WAAU,CACxB,CAAO,GACGI,GAAA,YAAAA,EAAS,eAAgB,OACpB,QAAQ,UAEV,KAAK,eACV,CACE,GAAGA,EACH,MAAMA,GAAA,YAAAA,EAAS,eAAeA,GAAA,YAAAA,EAAS,OAAQ,QAChD,EACDe,CACR,EACK,CACF,CACD,eAAef,EAASe,EAAU,GAAI,CACpC,MAAMoC,EAAe,CACnB,GAAGpC,EACH,cAAeA,EAAQ,eAAiB,EAC9C,EACU+N,EAAWnH,EAAc,MAC7B,IAAM5D,EAAA,KAAKqK,GAAY,QAAQpO,CAAO,EAAE,OAAQJ,GAAU,CAACA,EAAM,WAAU,GAAM,CAACA,EAAM,SAAU,CAAA,EAAE,IAAKA,GAAU,CACjH,IAAIkJ,EAAUlJ,EAAM,MAAM,OAAQuD,CAAY,EAC9C,OAAKA,EAAa,eAChB2F,EAAUA,EAAQ,MAAM5J,CAAI,GAEvBU,EAAM,MAAM,cAAgB,SAAW,QAAQ,QAAS,EAAGkJ,CAC1E,CAAO,CACP,EACI,OAAO,QAAQ,IAAIgG,CAAQ,EAAE,KAAK5P,CAAI,CACvC,CACD,WAAW6B,EAAS,CAClB,MAAM2N,EAAmB,KAAK,oBAAoB3N,CAAO,EACrD2N,EAAiB,QAAU,SAC7BA,EAAiB,MAAQ,IAE3B,MAAM9O,EAAQmE,EAAA,KAAKqK,GAAY,MAAM,KAAMM,CAAgB,EAC3D,OAAO9O,EAAM,cACXD,GAAiB+O,EAAiB,UAAW9O,CAAK,CACxD,EAAQA,EAAM,MAAM8O,CAAgB,EAAI,QAAQ,QAAQ9O,EAAM,MAAM,IAAI,CACrE,CACD,cAAcmB,EAAS,CACrB,OAAO,KAAK,WAAWA,CAAO,EAAE,KAAK7B,CAAI,EAAE,MAAMA,CAAI,CACtD,CACD,mBAAmB6B,EAAS,CAC1B,OAAAA,EAAQ,SAAWgM,GAAsBhM,EAAQ,KAAK,EAC/C,KAAK,WAAWA,CAAO,CAC/B,CACD,sBAAsBA,EAAS,CAC7B,OAAO,KAAK,mBAAmBA,CAAO,EAAE,KAAK7B,CAAI,EAAE,MAAMA,CAAI,CAC9D,CACD,wBAAwB6B,EAAS,CAC/B,OAAAA,EAAQ,SAAWgM,GAAsBhM,EAAQ,KAAK,EAC/C,KAAK,gBAAgBA,CAAO,CACpC,CACD,uBAAwB,CACtB,OAAI2D,GAAc,WACTX,EAAA,KAAK8G,GAAe,wBAEtB,QAAQ,SAChB,CACD,eAAgB,CACd,OAAO9G,EAAA,KAAKqK,EACb,CACD,kBAAmB,CACjB,OAAOrK,EAAA,KAAK8G,EACb,CACD,mBAAoB,CAClB,OAAO9G,EAAA,KAAKuE,EACb,CACD,kBAAkBvH,EAAS,CACzB8C,EAAA,KAAKyE,EAAkBvH,EACxB,CACD,iBAAiBV,EAAUU,EAAS,CAClCgD,EAAA,KAAKsK,IAAe,IAAIvN,GAAQT,CAAQ,EAAG,CACzC,SAAAA,EACA,eAAgBU,CACtB,CAAK,CACF,CACD,iBAAiBV,EAAU,CACzB,MAAM0O,EAAW,CAAC,GAAGhL,EAAA,KAAKsK,IAAe,OAAQ,CAAA,EAC3ClN,EAAS,CAAA,EACf,OAAA4N,EAAS,QAASC,GAAiB,CAC7BxO,GAAgBH,EAAU2O,EAAa,QAAQ,GACjD,OAAO,OAAO7N,EAAQ6N,EAAa,cAAc,CAEzD,CAAK,EACM7N,CACR,CACD,oBAAoBN,EAAaE,EAAS,CACxCgD,EAAA,KAAKuK,IAAkB,IAAIxN,GAAQD,CAAW,EAAG,CAC/C,YAAAA,EACA,eAAgBE,CACtB,CAAK,CACF,CACD,oBAAoBF,EAAa,CAC/B,MAAMkO,EAAW,CAAC,GAAGhL,EAAA,KAAKuK,IAAkB,OAAQ,CAAA,EAC9CnN,EAAS,CAAA,EACf,OAAA4N,EAAS,QAASC,GAAiB,CAC7BxO,GAAgBK,EAAamO,EAAa,WAAW,GACvD,OAAO,OAAO7N,EAAQ6N,EAAa,cAAc,CAEzD,CAAK,EACM7N,CACR,CACD,oBAAoBJ,EAAS,CAC3B,GAAIA,EAAQ,WACV,OAAOA,EAET,MAAM2N,EAAmB,CACvB,GAAG3K,EAAA,KAAKuE,GAAgB,QACxB,GAAG,KAAK,iBAAiBvH,EAAQ,QAAQ,EACzC,GAAGA,EACH,WAAY,EAClB,EACI,OAAK2N,EAAiB,YACpBA,EAAiB,UAAYnO,GAC3BmO,EAAiB,SACjBA,CACR,GAEQA,EAAiB,qBAAuB,SAC1CA,EAAiB,mBAAqBA,EAAiB,cAAgB,UAErEA,EAAiB,eAAiB,SACpCA,EAAiB,aAAe,CAAC,CAACA,EAAiB,UAEjD,CAACA,EAAiB,aAAeA,EAAiB,YACpDA,EAAiB,YAAc,gBAE7BA,EAAiB,UAAYzL,KAC/ByL,EAAiB,QAAU,IAEtBA,CACR,CACD,uBAAuB3N,EAAS,CAC9B,OAAIA,GAAA,MAAAA,EAAS,WACJA,EAEF,CACL,GAAGgD,EAAA,KAAKuE,GAAgB,UACxB,IAAGvH,GAAA,YAAAA,EAAS,cAAe,KAAK,oBAAoBA,EAAQ,WAAW,EACvE,GAAGA,EACH,WAAY,EAClB,CACG,CACD,OAAQ,CACNgD,EAAA,KAAKqK,GAAY,QACjBrK,EAAA,KAAK8G,GAAe,OACrB,CACH,EA3REuD,EAAA,YACAvD,EAAA,YACAvC,EAAA,YACA+F,GAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,GAAA,YARgBjL,uGCAdyL,IAAgBzL,GAAA,cAAczE,EAAa,CAC7C,YAAYsL,EAAQtJ,EAAS,CAC3B,QAwJF0C,EAAA,KAAAyL,IAWAzL,EAAA,KAAA0L,IAiBA1L,EAAA,KAAA2L,IAGA3L,EAAA,KAAA4L,IAYA5L,EAAA,KAAA6L,IAIA7L,EAAA,KAAA8L,IAMA9L,EAAA,KAAA+L,IAmLA/L,EAAA,KAAAgM,IAmBAhM,EAAA,KAAAiM,IAtYAjM,EAAA,KAAA2E,EAAA,QACA3E,EAAA,KAAAkM,EAAgB,QAChBlM,EAAA,KAAAmM,GAA4B,QAC5BnM,EAAA,KAAAoM,EAAiB,QACjBpM,EAAA,KAAAqM,GAAA,QACArM,EAAA,KAAAsM,GAAA,QACAtM,EAAA,KAAAuM,GAAA,QACAvM,EAAA,KAAAwM,GAAA,QACAxM,EAAA,KAAAyM,GAAA,QACAzM,EAAA,KAAA0M,GAAA,QAGA1M,EAAA,KAAA2M,GAAA,QACA3M,EAAA,KAAA4M,GAAA,QACA5M,EAAA,KAAA6M,GAAA,QACA7M,EAAA,KAAA8M,GAAA,QACA9M,EAAA,KAAA+M,GAAgC,IAAI,KA5BlC,KAAK,QAAUzP,EACf8C,EAAA,KAAKuE,EAAUiC,GACfxG,EAAA,KAAKoM,GAAe,MACpBpM,EAAA,KAAKmM,GAAmBrL,MACnB,KAAK,QAAQ,+BAChBZ,EAAA,KAAKiM,IAAiB,OACpB,IAAI,MAAM,2DAA2D,CAC7E,EAEI,KAAK,YAAW,EAChB,KAAK,WAAWjP,CAAO,CACxB,CAkBD,aAAc,CACZ,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,CACtC,CACD,aAAc,CACR,KAAK,UAAU,OAAS,IAC1BgD,EAAA,KAAK4L,GAAc,YAAY,IAAI,EAC/Bc,GAAmB1M,EAAA,KAAK4L,GAAe,KAAK,OAAO,EACrDjH,EAAA,KAAKwG,GAAAwB,IAAL,WAEA,KAAK,aAAY,EAEnBhI,EAAA,KAAK4G,GAAAqB,IAAL,WAEH,CACD,eAAgB,CACT,KAAK,gBACR,KAAK,QAAO,CAEf,CACD,wBAAyB,CACvB,OAAOC,GACL7M,EAAA,KAAK4L,GACL,KAAK,QACL,KAAK,QAAQ,kBACnB,CACG,CACD,0BAA2B,CACzB,OAAOiB,GACL7M,EAAA,KAAK4L,GACL,KAAK,QACL,KAAK,QAAQ,oBACnB,CACG,CACD,SAAU,CACR,KAAK,UAA4B,IAAI,IACrCjH,EAAA,KAAK6G,GAAAsB,IAAL,WACAnI,EAAA,KAAK8G,GAAAsB,IAAL,WACA/M,EAAA,KAAK4L,GAAc,eAAe,IAAI,CACvC,CACD,WAAW5O,EAAS,CAClB,MAAMgQ,EAAc,KAAK,QACnBC,EAAYjN,EAAA,KAAK4L,GAEvB,GADA,KAAK,QAAU5L,EAAA,KAAKqE,GAAQ,oBAAoBrH,CAAO,EACnD,KAAK,QAAQ,UAAY,QAAU,OAAO,KAAK,QAAQ,SAAY,WAAa,OAAO,KAAK,QAAQ,SAAY,YAAc,OAAOlB,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAK4L,EAAa,GAAM,UACpM,MAAM,IAAI,MACR,uEACR,EAEIjH,EAAA,KAAK+G,GAAAwB,IAAL,WACAlN,EAAA,KAAK4L,GAAc,WAAW,KAAK,OAAO,EACtCoB,EAAY,YAAc,CAAC/O,GAAoB,KAAK,QAAS+O,CAAW,GAC1EhN,EAAA,KAAKqE,GAAQ,cAAe,EAAC,OAAO,CAClC,KAAM,yBACN,MAAOrE,EAAA,KAAK4L,GACZ,SAAU,IAClB,CAAO,EAEH,MAAMuB,EAAU,KAAK,eACjBA,GAAWC,GACbpN,EAAA,KAAK4L,GACLqB,EACA,KAAK,QACLD,CACN,GACMrI,EAAA,KAAKwG,GAAAwB,IAAL,WAEF,KAAK,aAAY,EACbQ,IAAYnN,EAAA,KAAK4L,KAAkBqB,GAAanR,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAK4L,EAAa,IAAM9P,EAAekR,EAAY,QAAShN,EAAA,KAAK4L,EAAa,GAAKhQ,GAAiB,KAAK,QAAQ,UAAWoE,EAAA,KAAK4L,EAAa,IAAMhQ,GAAiBoR,EAAY,UAAWhN,EAAA,KAAK4L,EAAa,IACrSjH,EAAA,KAAKyG,GAAAiC,IAAL,WAEF,MAAMC,EAAsB3I,EAAA,KAAK0G,GAAAkC,IAAL,WACxBJ,IAAYnN,EAAA,KAAK4L,KAAkBqB,GAAanR,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAK4L,EAAa,IAAM9P,EAAekR,EAAY,QAAShN,EAAA,KAAK4L,EAAa,GAAK0B,IAAwBtN,EAAA,KAAKwM,MACvM7H,EAAA,KAAK2G,GAAAkC,IAAL,UAA4BF,EAE/B,CACD,oBAAoBtQ,EAAS,CAC3B,MAAMnB,EAAQmE,EAAA,KAAKqE,GAAQ,cAAa,EAAG,MAAMrE,EAAA,KAAKqE,GAASrH,CAAO,EAChEI,EAAS,KAAK,aAAavB,EAAOmB,CAAO,EAC/C,OAAIyQ,GAAsC,KAAMrQ,CAAM,IACpD0C,EAAA,KAAKgM,EAAiB1O,GACtB0C,EAAA,KAAKkM,GAAwB,KAAK,SAClClM,EAAA,KAAKiM,GAAsB/L,EAAA,KAAK4L,GAAc,QAEzCxO,CACR,CACD,kBAAmB,CACjB,OAAO4C,EAAA,KAAK8L,EACb,CACD,YAAY1O,EAAQsQ,EAAe,CACjC,OAAO,IAAI,MAAMtQ,EAAQ,CACvB,IAAK,CAACuQ,EAAQtQ,KACZ,KAAK,UAAUA,CAAG,EAClBqQ,GAAA,MAAAA,EAAgBrQ,GACT,QAAQ,IAAIsQ,EAAQtQ,CAAG,EAEtC,CAAK,CACF,CACD,UAAUA,EAAK,CACb2C,EAAA,KAAKyM,IAAc,IAAIpP,CAAG,CAC3B,CACD,iBAAkB,CAChB,OAAO2C,EAAA,KAAK4L,EACb,CACD,QAAQ,CAAE,GAAG5O,CAAS,EAAG,GAAI,CAC3B,OAAO,KAAK,MAAM,CAChB,GAAGA,CACT,CAAK,CACF,CACD,gBAAgBA,EAAS,CACvB,MAAM2N,EAAmB3K,EAAA,KAAKqE,GAAQ,oBAAoBrH,CAAO,EAC3DnB,EAAQmE,EAAA,KAAKqE,GAAQ,cAAa,EAAG,MAAMrE,EAAA,KAAKqE,GAASsG,CAAgB,EAC/E,OAAO9O,EAAM,MAAO,EAAC,KAAK,IAAM,KAAK,aAAaA,EAAO8O,CAAgB,CAAC,CAC3E,CACD,MAAMvL,EAAc,CAClB,OAAOuF,EAAA,KAAKwG,GAAAwB,IAAL,UAAmB,CACxB,GAAGvN,EACH,cAAeA,EAAa,eAAiB,EACnD,GAAO,KAAK,KACN,KAAK,aAAY,EACVY,EAAA,KAAK8L,GACb,CACF,CA4DD,aAAajQ,EAAOmB,EAAS,OAC3B,MAAMiQ,EAAYjN,EAAA,KAAK4L,GACjBoB,EAAc,KAAK,QACnBY,EAAa5N,EAAA,KAAK8L,GAClB+B,EAAkB7N,EAAA,KAAK+L,IACvB+B,EAAoB9N,EAAA,KAAKgM,IAEzB+B,EADclS,IAAUoR,EACUpR,EAAM,MAAQmE,EAAA,KAAK6L,IACrD,CAAE,MAAAhH,CAAO,EAAGhJ,EAClB,IAAImS,EAAW,CAAE,GAAGnJ,GAChBoJ,EAAoB,GACpBtP,EACJ,GAAI3B,EAAQ,mBAAoB,CAC9B,MAAMmQ,EAAU,KAAK,eACfe,GAAe,CAACf,GAAWT,GAAmB7Q,EAAOmB,CAAO,EAC5DmR,GAAkBhB,GAAWC,GAAsBvR,EAAOoR,EAAWjQ,EAASgQ,CAAW,GAC3FkB,IAAgBC,MAClBH,EAAW,CACT,GAAGA,EACH,GAAG/H,GAAWpB,EAAM,KAAMhJ,EAAM,OAAO,CACjD,GAEUmB,EAAQ,qBAAuB,gBACjCgR,EAAS,YAAc,OAE1B,CACD,GAAI,CAAE,MAAAvL,EAAO,eAAA2L,EAAgB,OAAAvR,CAAM,EAAKmR,EACxCrP,EAAOqP,EAAS,KAChB,IAAIK,EAAa,GACjB,GAAIrR,EAAQ,kBAAoB,QAAU2B,IAAS,QAAU9B,IAAW,UAAW,CACjF,IAAIyR,EACAV,GAAA,MAAAA,EAAY,mBAAqB5Q,EAAQ,mBAAoB8Q,GAAA,YAAAA,EAAmB,kBAClFQ,EAAkBV,EAAW,KAC7BS,EAAa,IAEbC,EAAkB,OAAOtR,EAAQ,iBAAoB,WAAaA,EAAQ,iBACxEyC,EAAAO,EAAA,KAAKqM,MAAL,YAAA5M,EAAgC,MAAM,KACtCO,EAAA,KAAKqM,GACf,EAAYrP,EAAQ,gBAEVsR,IAAoB,SACtBzR,EAAS,UACT8B,EAAOF,GACLmP,GAAA,YAAAA,EAAY,KACZU,EACAtR,CACV,EACQiR,EAAoB,GAEvB,CACD,GAAIjR,EAAQ,QAAU2B,IAAS,QAAU,CAAC0P,EACxC,GAAIT,GAAcjP,KAASkP,GAAA,YAAAA,EAAiB,OAAQ7Q,EAAQ,SAAWgD,EAAA,KAAKmM,IAC1ExN,EAAOqB,EAAA,KAAKoM,QAEZ,IAAI,CACFtM,EAAA,KAAKqM,GAAYnP,EAAQ,QACzB2B,EAAO3B,EAAQ,OAAO2B,CAAI,EAC1BA,EAAOF,GAAYmP,GAAA,YAAAA,EAAY,KAAMjP,EAAM3B,CAAO,EAClD8C,EAAA,KAAKsM,GAAgBzN,GACrBmB,EAAA,KAAKoM,GAAe,KACrB,OAAQqC,EAAa,CACpBzO,EAAA,KAAKoM,GAAeqC,EACrB,CAGDvO,EAAA,KAAKkM,MACPzJ,EAAQzC,EAAA,KAAKkM,IACbvN,EAAOqB,EAAA,KAAKoM,IACZgC,EAAiB,KAAK,MACtBvR,EAAS,SAEX,MAAM2R,EAAaR,EAAS,cAAgB,WACtCS,EAAY5R,IAAW,UACvB6R,EAAU7R,IAAW,QACrB8R,EAAYF,GAAaD,EACzBtI,EAAUvH,IAAS,OA6BnBiQ,EA5BS,CACb,OAAA/R,EACA,YAAamR,EAAS,YACtB,UAAAS,EACA,UAAW5R,IAAW,UACtB,QAAA6R,EACA,iBAAkBC,EAClB,UAAAA,EACA,KAAAhQ,EACA,cAAeqP,EAAS,cACxB,MAAAvL,EACA,eAAA2L,EACA,aAAcJ,EAAS,kBACvB,cAAeA,EAAS,mBACxB,iBAAkBA,EAAS,iBAC3B,UAAWA,EAAS,gBAAkB,GAAKA,EAAS,iBAAmB,EACvE,oBAAqBA,EAAS,gBAAkBD,EAAkB,iBAAmBC,EAAS,iBAAmBD,EAAkB,iBACnI,WAAAS,EACA,aAAcA,GAAc,CAACC,EAC7B,eAAgBC,GAAW,CAACxI,EAC5B,SAAU8H,EAAS,cAAgB,SACnC,kBAAAC,EACA,eAAgBS,GAAWxI,EAC3B,QAAS2I,GAAQhT,EAAOmB,CAAO,EAC/B,QAAS,KAAK,QACd,QAASgD,EAAA,KAAKiM,IACd,UAAWnQ,EAAekB,EAAQ,QAASnB,CAAK,IAAM,EAC5D,EAEI,GAAI,KAAK,QAAQ,8BAA+B,CAC9C,MAAMiT,EAA8BhO,IAAa,CAC3C8N,EAAW,SAAW,QACxB9N,GAAS,OAAO8N,EAAW,KAAK,EACvBA,EAAW,OAAS,QAC7B9N,GAAS,QAAQ8N,EAAW,IAAI,CAE1C,EACYG,GAAmB,IAAM,CAC7B,MAAMC,GAAUlP,EAAA,KAAKmM,GAAmB2C,EAAW,QAAUhO,MAC7DkO,EAA2BE,EAAO,CAC1C,EACYC,GAAejP,EAAA,KAAKiM,IAC1B,OAAQgD,GAAa,OAAM,CACzB,IAAK,UACCpT,EAAM,YAAcoR,EAAU,WAChC6B,EAA2BG,EAAY,EAEzC,MACF,IAAK,aACCL,EAAW,SAAW,SAAWA,EAAW,OAASK,GAAa,QACpEF,KAEF,MACF,IAAK,YACCH,EAAW,SAAW,SAAWA,EAAW,QAAUK,GAAa,SACrEF,KAEF,KACH,CACF,CACD,OAAOH,CACR,CACD,cAAe,CACb,MAAMhB,EAAa5N,EAAA,KAAK8L,GAClB8C,EAAa,KAAK,aAAa5O,EAAA,KAAK4L,GAAe,KAAK,OAAO,EAMrE,GALA9L,EAAA,KAAKiM,GAAsB/L,EAAA,KAAK4L,GAAc,OAC9C9L,EAAA,KAAKkM,GAAwB,KAAK,SAC9BhM,EAAA,KAAK+L,IAAoB,OAAS,QACpCjM,EAAA,KAAKuM,GAA4BrM,EAAA,KAAK4L,IAEpC3N,GAAoB2Q,EAAYhB,CAAU,EAC5C,OAEF9N,EAAA,KAAKgM,EAAiB8C,GACtB,MAAMM,EAAwB,IAAM,CAClC,GAAI,CAACtB,EACH,MAAO,GAET,KAAM,CAAE,oBAAAuB,CAAmB,EAAK,KAAK,QAC/BC,EAA2B,OAAOD,GAAwB,WAAaA,EAAmB,EAAKA,EACrG,GAAIC,IAA6B,OAAS,CAACA,GAA4B,CAACpP,EAAA,KAAKyM,IAAc,KACzF,MAAO,GAET,MAAM4C,EAAgB,IAAI,IACxBD,GAA4BpP,EAAA,KAAKyM,GACzC,EACM,OAAI,KAAK,QAAQ,cACf4C,EAAc,IAAI,OAAO,EAEpB,OAAO,KAAKrP,EAAA,KAAK8L,EAAc,EAAE,KAAMzO,GAAQ,CACpD,MAAMiS,EAAWjS,EAEjB,OADgB2C,EAAA,KAAK8L,GAAewD,CAAQ,IAAM1B,EAAW0B,CAAQ,GACnDD,EAAc,IAAIC,CAAQ,CACpD,CAAO,CACP,EACI3K,EAAA,KAAKgH,GAAA4D,IAAL,UAAa,CAAE,UAAWL,EAAqB,CAAI,EACpD,CAcD,eAAgB,CACd,KAAK,aAAY,EACb,KAAK,gBACPvK,EAAA,KAAK4G,GAAAqB,IAAL,UAEH,CAcH,EAnZEvI,EAAA,YACAuH,EAAA,YACAC,GAAA,YACAC,EAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YAGAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YACAC,GAAA,YA2HAtB,GAAA,YAAAwB,GAAa,SAACvN,EAAc,CAC1BuF,EAAA,KAAK+G,GAAAwB,IAAL,WACA,IAAInI,EAAU/E,EAAA,KAAK4L,GAAc,MAC/B,KAAK,QACLxM,CACN,EACI,OAAKA,GAAA,MAAAA,EAAc,eACjB2F,EAAUA,EAAQ,MAAM5J,CAAI,GAEvB4J,CACR,EACDqG,GAAA,YAAAiC,GAAmB,UAAG,CACpB1I,EAAA,KAAK6G,GAAAsB,IAAL,WACA,MAAMnR,EAAYC,GAChB,KAAK,QAAQ,UACboE,EAAA,KAAK4L,EACX,EACI,GAAI1Q,IAAY8E,EAAA,KAAK8L,GAAe,SAAW,CAACvQ,GAAeI,CAAS,EACtE,OAGF,MAAM4C,EADO9C,GAAeuE,EAAA,KAAK8L,GAAe,cAAenQ,CAAS,EACjD,EACvBmE,EAAA,KAAKwM,GAAkB,WAAW,IAAM,CACjCtM,EAAA,KAAK8L,GAAe,SACvB,KAAK,aAAY,CAEpB,EAAEvN,CAAO,EACX,EACD8M,GAAA,YAAAkC,GAAuB,UAAG,CACxB,OAAQ,OAAO,KAAK,QAAQ,iBAAoB,WAAa,KAAK,QAAQ,gBAAgBvN,EAAA,KAAK4L,EAAa,EAAI,KAAK,QAAQ,kBAAoB,EAClJ,EACDN,GAAA,YAAAkC,GAAsB,SAACgC,EAAc,CACnC7K,EAAA,KAAK8G,GAAAsB,IAAL,WACAjN,EAAA,KAAK0M,GAA0BgD,GAC3B,EAAAtU,IAAYY,EAAe,KAAK,QAAQ,QAASkE,EAAA,KAAK4L,EAAa,IAAM,IAAS,CAACrQ,GAAeyE,EAAA,KAAKwM,GAAuB,GAAKxM,EAAA,KAAKwM,MAA4B,IAGxK1M,EAAA,KAAKyM,GAAqB,YAAY,IAAM,EACtC,KAAK,QAAQ,6BAA+BnM,GAAa,UAAS,IACpEuE,EAAA,KAAKwG,GAAAwB,IAAL,UAER,EAAO3M,EAAA,KAAKwM,GAAuB,EAChC,EACDjB,GAAA,YAAAqB,GAAa,UAAG,CACdjI,EAAA,KAAKyG,GAAAiC,IAAL,WACA1I,EAAA,KAAK2G,GAAAkC,IAAL,UAA4B7I,EAAA,KAAK0G,GAAAkC,IAAL,WAC7B,EACD/B,GAAA,YAAAsB,GAAkB,UAAG,CACf9M,EAAA,KAAKsM,MACP,aAAatM,EAAA,KAAKsM,GAAe,EACjCxM,EAAA,KAAKwM,GAAkB,QAE1B,EACDb,GAAA,YAAAsB,GAAqB,UAAG,CAClB/M,EAAA,KAAKuM,MACP,cAAcvM,EAAA,KAAKuM,GAAkB,EACrCzM,EAAA,KAAKyM,GAAqB,QAE7B,EA8KDb,GAAA,YAAAwB,GAAY,UAAG,CACb,MAAMrR,EAAQmE,EAAA,KAAKqE,GAAQ,cAAe,EAAC,MAAMrE,EAAA,KAAKqE,GAAS,KAAK,OAAO,EAC3E,GAAIxI,IAAUmE,EAAA,KAAK4L,GACjB,OAEF,MAAMqB,EAAYjN,EAAA,KAAK4L,GACvB9L,EAAA,KAAK8L,EAAgB/P,GACrBiE,EAAA,KAAK+L,GAA4BhQ,EAAM,OACnC,KAAK,iBACPoR,GAAA,MAAAA,EAAW,eAAe,MAC1BpR,EAAM,YAAY,IAAI,EAEzB,EAOD8P,GAAA,YAAA4D,GAAO,SAACE,EAAe,CACrB7L,EAAc,MAAM,IAAM,CACpB6L,EAAc,WAChB,KAAK,UAAU,QAASxU,GAAa,CACnCA,EAAS+E,EAAA,KAAK8L,EAAc,CACtC,CAAS,EAEH9L,EAAA,KAAKqE,GAAQ,cAAe,EAAC,OAAO,CAClC,MAAOrE,EAAA,KAAK4L,GACZ,KAAM,wBACd,CAAO,CACP,CAAK,CACF,EAjaiBnM,IAmapB,SAASiQ,GAAkB7T,EAAOmB,EAAS,CACzC,OAAOlB,EAAekB,EAAQ,QAASnB,CAAK,IAAM,IAASA,EAAM,MAAM,OAAS,QAAU,EAAEA,EAAM,MAAM,SAAW,SAAWmB,EAAQ,eAAiB,GACzJ,CACA,SAAS0P,GAAmB7Q,EAAOmB,EAAS,CAC1C,OAAO0S,GAAkB7T,EAAOmB,CAAO,GAAKnB,EAAM,MAAM,OAAS,QAAUgR,GAAchR,EAAOmB,EAASA,EAAQ,cAAc,CACjI,CACA,SAAS6P,GAAchR,EAAOmB,EAAS2S,EAAO,CAC5C,GAAI7T,EAAekB,EAAQ,QAASnB,CAAK,IAAM,IAASD,GAAiBoB,EAAQ,UAAWnB,CAAK,IAAM,SAAU,CAC/G,MAAML,EAAQ,OAAOmU,GAAU,WAAaA,EAAM9T,CAAK,EAAI8T,EAC3D,OAAOnU,IAAU,UAAYA,IAAU,IAASqT,GAAQhT,EAAOmB,CAAO,CACvE,CACD,MAAO,EACT,CACA,SAASoQ,GAAsBvR,EAAOoR,EAAWjQ,EAASgQ,EAAa,CACrE,OAAQnR,IAAUoR,GAAanR,EAAekR,EAAY,QAASnR,CAAK,IAAM,MAAW,CAACmB,EAAQ,UAAYnB,EAAM,MAAM,SAAW,UAAYgT,GAAQhT,EAAOmB,CAAO,CACzK,CACA,SAAS6R,GAAQhT,EAAOmB,EAAS,CAC/B,OAAOlB,EAAekB,EAAQ,QAASnB,CAAK,IAAM,IAASA,EAAM,cAAcD,GAAiBoB,EAAQ,UAAWnB,CAAK,CAAC,CAC3H,CACA,SAAS4R,GAAsCxI,EAAU2K,EAAkB,CACzE,MAAK,CAAA3R,GAAoBgH,EAAS,iBAAkB,EAAE2K,CAAgB,CAIxE,CCtcA,IAAIC,GAAqBC,EAAmB,cAC1C,MACF,EACIC,GAAkBC,GAAgB,CACpC,MAAM1J,EAAS2J,aAAiBJ,EAAkB,EAClD,GAAIG,EACF,OAAOA,EAET,GAAI,CAAC1J,EACH,MAAM,IAAI,MAAM,wDAAwD,EAE1E,OAAOA,CACT,EACI4J,GAAsB,CAAC,CACzB,OAAA5J,EACA,SAAA6J,CACF,KACEC,EAAAA,UAAgB,KACd9J,EAAO,MAAK,EACL,IAAM,CACXA,EAAO,QAAO,CACpB,GACK,CAACA,CAAM,CAAC,EACY+J,GAAIR,GAAmB,SAAU,CAAE,MAAOvJ,EAAQ,SAAA6J,CAAQ,CAAE,GCxBjFG,GAAqBR,EAAAA,cAAoB,EAAK,EAC9CS,GAAiB,IAAMN,aAAiBK,EAAkB,EACpCA,GAAmB,SCD7C,SAASE,IAAc,CACrB,IAAIC,EAAU,GACd,MAAO,CACL,WAAY,IAAM,CAChBA,EAAU,EACX,EACD,MAAO,IAAM,CACXA,EAAU,EACX,EACD,QAAS,IACAA,CAEb,CACA,CACA,IAAIC,GAAiCZ,EAAAA,cAAoBU,GAAW,CAAE,EAClEG,GAA6B,IAAMV,EAAgB,WAACS,EAA8B,ECflFE,GAAkC,CAAC5T,EAAS6T,IAAuB,EACjE7T,EAAQ,UAAYA,EAAQ,cAAgBA,EAAQ,iCACjD6T,EAAmB,YACtB7T,EAAQ,aAAe,IAG7B,EACI8T,GAA8BD,GAAuB,CACvDT,EAAAA,UAAgB,IAAM,CACpBS,EAAmB,WAAU,CACjC,EAAK,CAACA,CAAkB,CAAC,CACzB,EACIE,GAAc,CAAC,CACjB,OAAA3T,EACA,mBAAAyT,EACA,aAAAvR,EACA,MAAAzD,EACA,SAAAmV,CACF,IACS5T,EAAO,SAAW,CAACyT,EAAmB,QAAS,GAAI,CAACzT,EAAO,YAAcvB,IAAUmV,GAAY5T,EAAO,OAAS,QAAUiC,GAAiBC,EAAc,CAAClC,EAAO,MAAOvB,CAAK,CAAC,GCtBlLoV,GAAwBtG,GAAqB,CAC/C,GAAIA,EAAiB,SAAU,CAC7B,MAAMuG,EAAS1V,GAAUA,IAAU,SAAWA,EAAQ,KAAK,IAAIA,GAAS,IAAK,GAAG,EAC1E2V,EAAoBxG,EAAiB,UAC3CA,EAAiB,UAAY,OAAOwG,GAAsB,WAAa,IAAIzN,IAASwN,EAAMC,EAAkB,GAAGzN,CAAI,CAAC,EAAIwN,EAAMC,CAAiB,EAC3I,OAAOxG,EAAiB,QAAW,WACrCA,EAAiB,OAAS,KAAK,IAAIA,EAAiB,OAAQ,GAAG,EAElE,CACH,EACIyG,GAAY,CAAChU,EAAQiU,IAAgBjU,EAAO,WAAaA,EAAO,YAAc,CAACiU,EAC/EC,GAAgB,CAAC3G,EAAkBvN,KAAWuN,GAAA,YAAAA,EAAkB,WAAYvN,EAAO,UACnFmU,GAAkB,CAAC5G,EAAkB1F,EAAU4L,IAAuB5L,EAAS,gBAAgB0F,CAAgB,EAAE,MAAM,IAAM,CAC/HkG,EAAmB,WAAU,CAC/B,CAAC,ECGD,SAASW,GAAaxU,EAASyU,EAAUzB,EAAa,eAQpD,MAAMqB,EAAcd,KACdM,EAAqBF,KACrBrK,EAASyJ,GAAeC,CAAW,EACnCrF,EAAmBrE,EAAO,oBAAoBtJ,CAAO,GAC3DgI,GAAAvF,EAAA6G,EAAO,oBAAoB,UAA3B,YAAA7G,EAAoC,4BAApC,MAAAuF,EAAA,KAAAvF,EACEkL,GASFA,EAAiB,mBAAqB0G,EAAc,cAAgB,aACpEJ,GAAqBtG,CAAgB,EACrCiG,GAAgCjG,EAAkBkG,CAAkB,EACpEC,GAA2BD,CAAkB,EAC7C,MAAMa,EAAkB,CAACpL,EAAO,cAAe,EAAC,IAAIqE,EAAiB,SAAS,EACxE,CAAC1F,CAAQ,EAAI0M,EAAc,SAC/B,IAAM,IAAIF,EACRnL,EACAqE,CACD,CACL,EACQvN,EAAS6H,EAAS,oBAAoB0F,CAAgB,EACtDiH,EAAkB,CAACP,GAAerU,EAAQ,aAAe,GAgB/D,GAfA6U,EAA0B,qBACxBC,EAAiB,YACdC,GAAkB,CACjB,MAAMC,EAAcJ,EAAkB3M,EAAS,UAAUrB,EAAc,WAAWmO,CAAa,CAAC,EAAI5W,EACpG,OAAA8J,EAAS,aAAY,EACd+M,CACR,EACD,CAAC/M,EAAU2M,CAAe,CAC3B,EACD,IAAM3M,EAAS,iBAAkB,EACjC,IAAMA,EAAS,iBAAkB,CACrC,EACEmL,EAAAA,UAAgB,IAAM,CACpBnL,EAAS,WAAW0F,CAAgB,CACxC,EAAK,CAACA,EAAkB1F,CAAQ,CAAC,EAC3BqM,GAAc3G,EAAkBvN,CAAM,EACxC,MAAMmU,GAAgB5G,EAAkB1F,EAAU4L,CAAkB,EAEtE,GAAIE,GAAY,CACd,OAAA3T,EACA,mBAAAyT,EACA,aAAclG,EAAiB,aAC/B,MAAOrE,EAAO,cAAa,EAAG,IAAIqE,EAAiB,SAAS,EAC5D,SAAUA,EAAiB,QAC/B,CAAG,EACC,MAAMvN,EAAO,MAOf,IAJA0I,GAAAF,EAAAU,EAAO,oBAAoB,UAA3B,YAAAV,EAAoC,2BAApC,MAAAE,EAAA,KAAAF,EACE+E,EACAvN,GAEEuN,EAAiB,+BAAiC,CAACzP,IAAYkW,GAAUhU,EAAQiU,CAAW,EAAG,CACjG,MAAMtM,EAAU2M,EAEdH,GAAgB5G,EAAkB1F,EAAU4L,CAAkB,GAG9DzJ,EAAAd,EAAO,cAAa,EAAG,IAAIqE,EAAiB,SAAS,IAArD,YAAAvD,EAAwD,QAE1DrC,GAAA,MAAAA,EAAS,MAAM5J,GAAM,QAAQ,IAAM,CACjC8J,EAAS,aAAY,CAC3B,EACG,CACD,OAAQ0F,EAAiB,oBAAqDvN,EAA/B6H,EAAS,YAAY7H,CAAM,CAC5E,CC9FA,SAAS6U,GAASjV,EAASgT,EAAa,CACtC,OAAOwB,GAAaxU,EAASkO,GAAe8E,CAAW,CACzD", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}
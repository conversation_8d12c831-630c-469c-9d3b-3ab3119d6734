var Yt=(e,t,s)=>{if(!t.has(e))throw TypeError("Cannot "+s)};var i=(e,t,s)=>(Yt(e,t,"read from private field"),s?s.call(e):t.get(e)),o=(e,t,s)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,s)},u=(e,t,s,r)=>(Yt(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s);var zt=(e,t,s,r)=>({set _(n){u(e,t,n,s)},get _(){return i(e,t,r)}}),v=(e,t,s)=>(Yt(e,t,"access private method"),s);import{r as q}from"./vendor-b69f2a9f.js";var je={exports:{}},Jt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ve=q,We=Symbol.for("react.element"),Je=Symbol.for("react.fragment"),Ye=Object.prototype.hasOwnProperty,Xe=Ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ze={key:!0,ref:!0,__self:!0,__source:!0};function Ue(e,t,s){var r,n={},a=null,h=null;s!==void 0&&(a=""+s),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(h=t.ref);for(r in t)Ye.call(t,r)&&!Ze.hasOwnProperty(r)&&(n[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)n[r]===void 0&&(n[r]=t[r]);return{$$typeof:We,type:e,key:a,ref:h,props:n,_owner:Xe.current}}Jt.Fragment=Je;Jt.jsx=Ue;Jt.jsxs=Ue;je.exports=Jt;var de=je.exports;const js=de.Fragment,ts=de.jsx,Us=de.jsxs;var Nt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},pt=typeof window>"u"||"Deno"in globalThis;function j(){}function es(e,t){return typeof e=="function"?e(t):e}function Zt(e){return typeof e=="number"&&e>=0&&e!==1/0}function qe(e,t){return Math.max(e+(t||0)-Date.now(),0)}function rt(e,t){return typeof e=="function"?e(t):e}function k(e,t){return typeof e=="function"?e(t):e}function ve(e,t){const{type:s="all",exact:r,fetchStatus:n,predicate:a,queryKey:h,stale:c}=e;if(h){if(r){if(t.queryHash!==fe(h,t.options))return!1}else if(!Tt(t.queryKey,h))return!1}if(s!=="all"){const l=t.isActive();if(s==="active"&&!l||s==="inactive"&&l)return!1}return!(typeof c=="boolean"&&t.isStale()!==c||n&&n!==t.state.fetchStatus||a&&!a(t))}function be(e,t){const{exact:s,status:r,predicate:n,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(Mt(t.options.mutationKey)!==Mt(a))return!1}else if(!Tt(t.options.mutationKey,a))return!1}return!(r&&t.state.status!==r||n&&!n(t))}function fe(e,t){return((t==null?void 0:t.queryKeyHashFn)||Mt)(e)}function Mt(e){return JSON.stringify(e,(t,s)=>ee(s)?Object.keys(s).sort().reduce((r,n)=>(r[n]=s[n],r),{}):s)}function Tt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(s=>Tt(e[s],t[s])):!1}function _e(e,t){if(e===t)return e;const s=ge(e)&&ge(t);if(s||ee(e)&&ee(t)){const r=s?e:Object.keys(e),n=r.length,a=s?t:Object.keys(t),h=a.length,c=s?[]:{},l=new Set(r);let g=0;for(let C=0;C<h;C++){const d=s?C:a[C];(!s&&l.has(d)||s)&&e[d]===void 0&&t[d]===void 0?(c[d]=void 0,g++):(c[d]=_e(e[d],t[d]),c[d]===e[d]&&e[d]!==void 0&&g++)}return n===h&&g===n?e:c}return t}function te(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function ge(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function ee(e){if(!we(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!we(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function we(e){return Object.prototype.toString.call(e)==="[object Object]"}function ss(e){return new Promise(t=>{setTimeout(t,e)})}function se(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?_e(e,t):t}function rs(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function is(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var ye=Symbol();function ke(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===ye?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function ns(e,t){return typeof e=="function"?e(...t):!!e}var nt,W,vt,Fe,as=(Fe=class extends Nt{constructor(){super();o(this,nt,void 0);o(this,W,void 0);o(this,vt,void 0);u(this,vt,t=>{if(!pt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){i(this,W)||this.setEventListener(i(this,vt))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,W))==null||t.call(this),u(this,W,void 0))}setEventListener(t){var s;u(this,vt,t),(s=i(this,W))==null||s.call(this),u(this,W,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){i(this,nt)!==t&&(u(this,nt,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof i(this,nt)=="boolean"?i(this,nt):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},nt=new WeakMap,W=new WeakMap,vt=new WeakMap,Fe),pe=new as,bt,J,gt,Ee,us=(Ee=class extends Nt{constructor(){super();o(this,bt,!0);o(this,J,void 0);o(this,gt,void 0);u(this,gt,t=>{if(!pt&&window.addEventListener){const s=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){i(this,J)||this.setEventListener(i(this,gt))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,J))==null||t.call(this),u(this,J,void 0))}setEventListener(t){var s;u(this,gt,t),(s=i(this,J))==null||s.call(this),u(this,J,t(this.setOnline.bind(this)))}setOnline(t){i(this,bt)!==t&&(u(this,bt,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return i(this,bt)}},bt=new WeakMap,J=new WeakMap,gt=new WeakMap,Ee),Vt=new us;function re(){let e,t;const s=new Promise((n,a)=>{e=n,t=a});s.status="pending",s.catch(()=>{});function r(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{r({status:"fulfilled",value:n}),e(n)},s.reject=n=>{r({status:"rejected",reason:n}),t(n)},s}function os(e){return Math.min(1e3*2**e,3e4)}function Le(e){return(e??"online")==="online"?Vt.isOnline():!0}var Ke=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Xt(e){return e instanceof Ke}function He(e){let t=!1,s=0,r=!1,n;const a=re(),h=f=>{var y;r||(p(new Ke(f)),(y=e.abort)==null||y.call(e))},c=()=>{t=!0},l=()=>{t=!1},g=()=>pe.isFocused()&&(e.networkMode==="always"||Vt.isOnline())&&e.canRun(),C=()=>Le(e.networkMode)&&e.canRun(),d=f=>{var y;r||(r=!0,(y=e.onSuccess)==null||y.call(e,f),n==null||n(),a.resolve(f))},p=f=>{var y;r||(r=!0,(y=e.onError)==null||y.call(e,f),n==null||n(),a.reject(f))},m=()=>new Promise(f=>{var y;n=S=>{(r||g())&&f(S)},(y=e.onPause)==null||y.call(e)}).then(()=>{var f;n=void 0,r||(f=e.onContinue)==null||f.call(e)}),w=()=>{if(r)return;let f;const y=s===0?e.initialPromise:void 0;try{f=y??e.fn()}catch(S){f=Promise.reject(S)}Promise.resolve(f).then(d).catch(S=>{var L;if(r)return;const D=e.retry??(pt?0:3),O=e.retryDelay??os,E=typeof O=="function"?O(s,S):O,U=D===!0||typeof D=="number"&&s<D||typeof D=="function"&&D(s,S);if(t||!U){p(S);return}s++,(L=e.onFail)==null||L.call(e,s,S),ss(E).then(()=>g()?void 0:m()).then(()=>{t?p(S):w()})})};return{promise:a,cancel:h,continue:()=>(n==null||n(),a),cancelRetry:c,continueRetry:l,canStart:C,start:()=>(C()?w():m().then(w),a)}}var hs=e=>setTimeout(e,0);function cs(){let e=[],t=0,s=c=>{c()},r=c=>{c()},n=hs;const a=c=>{t?e.push(c):n(()=>{s(c)})},h=()=>{const c=e;e=[],c.length&&n(()=>{r(()=>{c.forEach(l=>{s(l)})})})};return{batch:c=>{let l;t++;try{l=c()}finally{t--,t||h()}return l},batchCalls:c=>(...l)=>{a(()=>{c(...l)})},schedule:a,setNotifyFunction:c=>{s=c},setBatchNotifyFunction:c=>{r=c},setScheduler:c=>{n=c}}}var Q=cs(),at,Qe,Ge=(Qe=class{constructor(){o(this,at,void 0)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Zt(this.gcTime)&&u(this,at,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(pt?1/0:5*60*1e3))}clearGcTimeout(){i(this,at)&&(clearTimeout(i(this,at)),u(this,at,void 0))}},at=new WeakMap,Qe),wt,ut,_,ot,x,It,ht,K,z,xe,ls=(xe=class extends Ge{constructor(t){super();o(this,K);o(this,wt,void 0);o(this,ut,void 0);o(this,_,void 0);o(this,ot,void 0);o(this,x,void 0);o(this,It,void 0);o(this,ht,void 0);u(this,ht,!1),u(this,It,t.defaultOptions),this.setOptions(t.options),this.observers=[],u(this,ot,t.client),u(this,_,i(this,ot).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,u(this,wt,ds(this.options)),this.state=t.state??i(this,wt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=i(this,x))==null?void 0:t.promise}setOptions(t){this.options={...i(this,It),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&i(this,_).remove(this)}setData(t,s){const r=se(this.state.data,t,this.options);return v(this,K,z).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(t,s){v(this,K,z).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var r,n;const s=(r=i(this,x))==null?void 0:r.promise;return(n=i(this,x))==null||n.cancel(t),s?s.then(j).catch(j):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(i(this,wt))}isActive(){return this.observers.some(t=>k(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ye||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>rt(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!qe(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,x))==null||s.continue()}onOnline(){var s;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,x))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),i(this,_).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(i(this,x)&&(i(this,ht)?i(this,x).cancel({revert:!0}):i(this,x).cancelRetry()),this.scheduleGc()),i(this,_).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||v(this,K,z).call(this,{type:"invalidate"})}fetch(t,s){var g,C,d;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(i(this,x))return i(this,x).continueRetry(),i(this,x).promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(m=>m.options.queryFn);p&&this.setOptions(p.options)}const r=new AbortController,n=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(u(this,ht,!0),r.signal)})},a=()=>{const p=ke(this.options,s),w=(()=>{const f={client:i(this,ot),queryKey:this.queryKey,meta:this.meta};return n(f),f})();return u(this,ht,!1),this.options.persister?this.options.persister(p,w,this):p(w)},c=(()=>{const p={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:i(this,ot),state:this.state,fetchFn:a};return n(p),p})();(g=this.options.behavior)==null||g.onFetch(c,this),u(this,ut,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((C=c.fetchOptions)==null?void 0:C.meta))&&v(this,K,z).call(this,{type:"fetch",meta:(d=c.fetchOptions)==null?void 0:d.meta});const l=p=>{var m,w,f,y;Xt(p)&&p.silent||v(this,K,z).call(this,{type:"error",error:p}),Xt(p)||((w=(m=i(this,_).config).onError)==null||w.call(m,p,this),(y=(f=i(this,_).config).onSettled)==null||y.call(f,this.state.data,p,this)),this.scheduleGc()};return u(this,x,He({initialPromise:s==null?void 0:s.initialPromise,fn:c.fetchFn,abort:r.abort.bind(r),onSuccess:p=>{var m,w,f,y;if(p===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(S){l(S);return}(w=(m=i(this,_).config).onSuccess)==null||w.call(m,p,this),(y=(f=i(this,_).config).onSettled)==null||y.call(f,p,this.state.error,this),this.scheduleGc()},onError:l,onFail:(p,m)=>{v(this,K,z).call(this,{type:"failed",failureCount:p,error:m})},onPause:()=>{v(this,K,z).call(this,{type:"pause"})},onContinue:()=>{v(this,K,z).call(this,{type:"continue"})},retry:c.options.retry,retryDelay:c.options.retryDelay,networkMode:c.options.networkMode,canRun:()=>!0})),i(this,x).start()}},wt=new WeakMap,ut=new WeakMap,_=new WeakMap,ot=new WeakMap,x=new WeakMap,It=new WeakMap,ht=new WeakMap,K=new WeakSet,z=function(t){const s=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Ne(r.data,this.options),fetchMeta:t.meta??null};case"success":return u(this,ut,void 0),{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return Xt(n)&&n.revert&&i(this,ut)?{...i(this,ut),fetchStatus:"idle"}:{...r,error:n,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=s(this.state),Q.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),i(this,_).notify({query:this,type:"updated",action:t})})},xe);function Ne(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Le(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ds(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,r=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var G,De,fs=(De=class extends Nt{constructor(t={}){super();o(this,G,void 0);this.config=t,u(this,G,new Map)}build(t,s,r){const n=s.queryKey,a=s.queryHash??fe(n,s);let h=this.get(a);return h||(h=new ls({client:t,queryKey:n,queryHash:a,options:t.defaultQueryOptions(s),state:r,defaultOptions:t.getQueryDefaults(n)}),this.add(h)),h}add(t){i(this,G).has(t.queryHash)||(i(this,G).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=i(this,G).get(t.queryHash);s&&(t.destroy(),s===t&&i(this,G).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Q.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return i(this,G).get(t)}getAll(){return[...i(this,G).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(r=>ve(s,r))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(r=>ve(t,r)):s}notify(t){Q.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){Q.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Q.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},G=new WeakMap,De),N,T,ct,B,V,Me,ys=(Me=class extends Ge{constructor(t){super();o(this,B);o(this,N,void 0);o(this,T,void 0);o(this,ct,void 0);this.mutationId=t.mutationId,u(this,T,t.mutationCache),u(this,N,[]),this.state=t.state||ps(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){i(this,N).includes(t)||(i(this,N).push(t),this.clearGcTimeout(),i(this,T).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){u(this,N,i(this,N).filter(s=>s!==t)),this.scheduleGc(),i(this,T).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){i(this,N).length||(this.state.status==="pending"?this.scheduleGc():i(this,T).remove(this))}continue(){var t;return((t=i(this,ct))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var a,h,c,l,g,C,d,p,m,w,f,y,S,D,O,E,U,L,xt,M;const s=()=>{v(this,B,V).call(this,{type:"continue"})};u(this,ct,He({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(P,F)=>{v(this,B,V).call(this,{type:"failed",failureCount:P,error:F})},onPause:()=>{v(this,B,V).call(this,{type:"pause"})},onContinue:s,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>i(this,T).canRun(this)}));const r=this.state.status==="pending",n=!i(this,ct).canStart();try{if(r)s();else{v(this,B,V).call(this,{type:"pending",variables:t,isPaused:n}),await((h=(a=i(this,T).config).onMutate)==null?void 0:h.call(a,t,this));const F=await((l=(c=this.options).onMutate)==null?void 0:l.call(c,t));F!==this.state.context&&v(this,B,V).call(this,{type:"pending",context:F,variables:t,isPaused:n})}const P=await i(this,ct).start();return await((C=(g=i(this,T).config).onSuccess)==null?void 0:C.call(g,P,t,this.state.context,this)),await((p=(d=this.options).onSuccess)==null?void 0:p.call(d,P,t,this.state.context)),await((w=(m=i(this,T).config).onSettled)==null?void 0:w.call(m,P,null,this.state.variables,this.state.context,this)),await((y=(f=this.options).onSettled)==null?void 0:y.call(f,P,null,t,this.state.context)),v(this,B,V).call(this,{type:"success",data:P}),P}catch(P){try{throw await((D=(S=i(this,T).config).onError)==null?void 0:D.call(S,P,t,this.state.context,this)),await((E=(O=this.options).onError)==null?void 0:E.call(O,P,t,this.state.context)),await((L=(U=i(this,T).config).onSettled)==null?void 0:L.call(U,void 0,P,this.state.variables,this.state.context,this)),await((M=(xt=this.options).onSettled)==null?void 0:M.call(xt,void 0,P,t,this.state.context)),P}finally{v(this,B,V).call(this,{type:"error",error:P})}}finally{i(this,T).runNext(this)}}},N=new WeakMap,T=new WeakMap,ct=new WeakMap,B=new WeakSet,V=function(t){const s=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),Q.batch(()=>{i(this,N).forEach(r=>{r.onMutationUpdate(t)}),i(this,T).notify({mutation:this,type:"updated",action:t})})},Me);function ps(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var $,H,At,Te,ms=(Te=class extends Nt{constructor(t={}){super();o(this,$,void 0);o(this,H,void 0);o(this,At,void 0);this.config=t,u(this,$,new Set),u(this,H,new Map),u(this,At,0)}build(t,s,r){const n=new ys({mutationCache:this,mutationId:++zt(this,At)._,options:t.defaultMutationOptions(s),state:r});return this.add(n),n}add(t){i(this,$).add(t);const s=$t(t);if(typeof s=="string"){const r=i(this,H).get(s);r?r.push(t):i(this,H).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(i(this,$).delete(t)){const s=$t(t);if(typeof s=="string"){const r=i(this,H).get(s);if(r)if(r.length>1){const n=r.indexOf(t);n!==-1&&r.splice(n,1)}else r[0]===t&&i(this,H).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=$t(t);if(typeof s=="string"){const r=i(this,H).get(s),n=r==null?void 0:r.find(a=>a.state.status==="pending");return!n||n===t}else return!0}runNext(t){var r;const s=$t(t);if(typeof s=="string"){const n=(r=i(this,H).get(s))==null?void 0:r.find(a=>a!==t&&a.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Q.batch(()=>{i(this,$).forEach(t=>{this.notify({type:"removed",mutation:t})}),i(this,$).clear(),i(this,H).clear()})}getAll(){return Array.from(i(this,$))}find(t){const s={exact:!0,...t};return this.getAll().find(r=>be(s,r))}findAll(t={}){return this.getAll().filter(s=>be(t,s))}notify(t){Q.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return Q.batch(()=>Promise.all(t.map(s=>s.continue().catch(j))))}},$=new WeakMap,H=new WeakMap,At=new WeakMap,Te);function $t(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Ce(e){return{onFetch:(t,s)=>{var C,d,p,m,w;const r=t.options,n=(p=(d=(C=t.fetchOptions)==null?void 0:C.meta)==null?void 0:d.fetchMore)==null?void 0:p.direction,a=((m=t.state.data)==null?void 0:m.pages)||[],h=((w=t.state.data)==null?void 0:w.pageParams)||[];let c={pages:[],pageParams:[]},l=0;const g=async()=>{let f=!1;const y=O=>{Object.defineProperty(O,"signal",{enumerable:!0,get:()=>(t.signal.aborted?f=!0:t.signal.addEventListener("abort",()=>{f=!0}),t.signal)})},S=ke(t.options,t.fetchOptions),D=async(O,E,U)=>{if(f)return Promise.reject();if(E==null&&O.pages.length)return Promise.resolve(O);const xt=(()=>{const it={client:t.client,queryKey:t.queryKey,pageParam:E,direction:U?"backward":"forward",meta:t.options.meta};return y(it),it})(),M=await S(xt),{maxPages:P}=t.options,F=U?is:rs;return{pages:F(O.pages,M,P),pageParams:F(O.pageParams,E,P)}};if(n&&a.length){const O=n==="backward",E=O?vs:Oe,U={pages:a,pageParams:h},L=E(r,U);c=await D(U,L,O)}else{const O=e??a.length;do{const E=l===0?h[0]??r.initialPageParam:Oe(r,c);if(l>0&&E==null)break;c=await D(c,E),l++}while(l<O)}return c};t.options.persister?t.fetchFn=()=>{var f,y;return(y=(f=t.options).persister)==null?void 0:y.call(f,g,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=g}}}function Oe(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function vs(e,{pages:t,pageParams:s}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,s[0],s):void 0}var R,Y,X,Ct,Ot,Z,Rt,St,Ie,qs=(Ie=class{constructor(e={}){o(this,R,void 0);o(this,Y,void 0);o(this,X,void 0);o(this,Ct,void 0);o(this,Ot,void 0);o(this,Z,void 0);o(this,Rt,void 0);o(this,St,void 0);u(this,R,e.queryCache||new fs),u(this,Y,e.mutationCache||new ms),u(this,X,e.defaultOptions||{}),u(this,Ct,new Map),u(this,Ot,new Map),u(this,Z,0)}mount(){zt(this,Z)._++,i(this,Z)===1&&(u(this,Rt,pe.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,R).onFocus())})),u(this,St,Vt.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,R).onOnline())})))}unmount(){var e,t;zt(this,Z)._--,i(this,Z)===0&&((e=i(this,Rt))==null||e.call(this),u(this,Rt,void 0),(t=i(this,St))==null||t.call(this),u(this,St,void 0))}isFetching(e){return i(this,R).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return i(this,Y).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,R).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=i(this,R).build(this,t),r=s.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(rt(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return i(this,R).findAll(e).map(({queryKey:t,state:s})=>{const r=s.data;return[t,r]})}setQueryData(e,t,s){const r=this.defaultQueryOptions({queryKey:e}),n=i(this,R).get(r.queryHash),a=n==null?void 0:n.state.data,h=es(t,a);if(h!==void 0)return i(this,R).build(this,r).setData(h,{...s,manual:!0})}setQueriesData(e,t,s){return Q.batch(()=>i(this,R).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,R).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=i(this,R);Q.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=i(this,R);return Q.batch(()=>(s.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},r=Q.batch(()=>i(this,R).findAll(e).map(n=>n.cancel(s)));return Promise.all(r).then(j).catch(j)}invalidateQueries(e,t={}){return Q.batch(()=>(i(this,R).findAll(e).forEach(s=>{s.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},r=Q.batch(()=>i(this,R).findAll(e).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let a=n.fetch(void 0,s);return s.throwOnError||(a=a.catch(j)),n.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(r).then(j)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=i(this,R).build(this,t);return s.isStaleByTime(rt(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(j).catch(j)}fetchInfiniteQuery(e){return e.behavior=Ce(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(j).catch(j)}ensureInfiniteQueryData(e){return e.behavior=Ce(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Vt.isOnline()?i(this,Y).resumePausedMutations():Promise.resolve()}getQueryCache(){return i(this,R)}getMutationCache(){return i(this,Y)}getDefaultOptions(){return i(this,X)}setDefaultOptions(e){u(this,X,e)}setQueryDefaults(e,t){i(this,Ct).set(Mt(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...i(this,Ct).values()],s={};return t.forEach(r=>{Tt(e,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(e,t){i(this,Ot).set(Mt(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...i(this,Ot).values()],s={};return t.forEach(r=>{Tt(e,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...i(this,X).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=fe(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===ye&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...i(this,X).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){i(this,R).clear(),i(this,Y).clear()}},R=new WeakMap,Y=new WeakMap,X=new WeakMap,Ct=new WeakMap,Ot=new WeakMap,Z=new WeakMap,Rt=new WeakMap,St=new WeakMap,Ie),A,b,jt,I,lt,Pt,tt,et,Ut,Ft,Et,dt,ft,st,Qt,yt,Dt,qt,ie,_t,ne,kt,ae,Lt,ue,Kt,oe,Ht,he,Gt,ce,Wt,Be,Ae,bs=(Ae=class extends Nt{constructor(t,s){super();o(this,yt);o(this,qt);o(this,_t);o(this,kt);o(this,Lt);o(this,Kt);o(this,Ht);o(this,Gt);o(this,Wt);o(this,A,void 0);o(this,b,void 0);o(this,jt,void 0);o(this,I,void 0);o(this,lt,void 0);o(this,Pt,void 0);o(this,tt,void 0);o(this,et,void 0);o(this,Ut,void 0);o(this,Ft,void 0);o(this,Et,void 0);o(this,dt,void 0);o(this,ft,void 0);o(this,st,void 0);o(this,Qt,new Set);this.options=s,u(this,A,t),u(this,et,null),u(this,tt,re()),this.options.experimental_prefetchInRender||i(this,tt).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,b).addObserver(this),Re(i(this,b),this.options)?v(this,yt,Dt).call(this):this.updateResult(),v(this,Lt,ue).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return le(i(this,b),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return le(i(this,b),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,v(this,Kt,oe).call(this),v(this,Ht,he).call(this),i(this,b).removeObserver(this)}setOptions(t){const s=this.options,r=i(this,b);if(this.options=i(this,A).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof k(this.options.enabled,i(this,b))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");v(this,Gt,ce).call(this),i(this,b).setOptions(this.options),s._defaulted&&!te(this.options,s)&&i(this,A).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,b),observer:this});const n=this.hasListeners();n&&Se(i(this,b),r,this.options,s)&&v(this,yt,Dt).call(this),this.updateResult(),n&&(i(this,b)!==r||k(this.options.enabled,i(this,b))!==k(s.enabled,i(this,b))||rt(this.options.staleTime,i(this,b))!==rt(s.staleTime,i(this,b)))&&v(this,qt,ie).call(this);const a=v(this,_t,ne).call(this);n&&(i(this,b)!==r||k(this.options.enabled,i(this,b))!==k(s.enabled,i(this,b))||a!==i(this,st))&&v(this,kt,ae).call(this,a)}getOptimisticResult(t){const s=i(this,A).getQueryCache().build(i(this,A),t),r=this.createResult(s,t);return ws(this,r)&&(u(this,I,r),u(this,Pt,this.options),u(this,lt,i(this,b).state)),r}getCurrentResult(){return i(this,I)}trackResult(t,s){return new Proxy(t,{get:(r,n)=>(this.trackProp(n),s==null||s(n),Reflect.get(r,n))})}trackProp(t){i(this,Qt).add(t)}getCurrentQuery(){return i(this,b)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,A).defaultQueryOptions(t),r=i(this,A).getQueryCache().build(i(this,A),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return v(this,yt,Dt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,I)))}createResult(t,s){var P;const r=i(this,b),n=this.options,a=i(this,I),h=i(this,lt),c=i(this,Pt),g=t!==r?t.state:i(this,jt),{state:C}=t;let d={...C},p=!1,m;if(s._optimisticResults){const F=this.hasListeners(),it=!F&&Re(t,s),mt=F&&Se(t,r,s,n);(it||mt)&&(d={...d,...Ne(C.data,t.options)}),s._optimisticResults==="isRestoring"&&(d.fetchStatus="idle")}let{error:w,errorUpdatedAt:f,status:y}=d;m=d.data;let S=!1;if(s.placeholderData!==void 0&&m===void 0&&y==="pending"){let F;a!=null&&a.isPlaceholderData&&s.placeholderData===(c==null?void 0:c.placeholderData)?(F=a.data,S=!0):F=typeof s.placeholderData=="function"?s.placeholderData((P=i(this,Et))==null?void 0:P.state.data,i(this,Et)):s.placeholderData,F!==void 0&&(y="success",m=se(a==null?void 0:a.data,F,s),p=!0)}if(s.select&&m!==void 0&&!S)if(a&&m===(h==null?void 0:h.data)&&s.select===i(this,Ut))m=i(this,Ft);else try{u(this,Ut,s.select),m=s.select(m),m=se(a==null?void 0:a.data,m,s),u(this,Ft,m),u(this,et,null)}catch(F){u(this,et,F)}i(this,et)&&(w=i(this,et),m=i(this,Ft),f=Date.now(),y="error");const D=d.fetchStatus==="fetching",O=y==="pending",E=y==="error",U=O&&D,L=m!==void 0,M={status:y,fetchStatus:d.fetchStatus,isPending:O,isSuccess:y==="success",isError:E,isInitialLoading:U,isLoading:U,data:m,dataUpdatedAt:d.dataUpdatedAt,error:w,errorUpdatedAt:f,failureCount:d.fetchFailureCount,failureReason:d.fetchFailureReason,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>g.dataUpdateCount||d.errorUpdateCount>g.errorUpdateCount,isFetching:D,isRefetching:D&&!O,isLoadingError:E&&!L,isPaused:d.fetchStatus==="paused",isPlaceholderData:p,isRefetchError:E&&L,isStale:me(t,s),refetch:this.refetch,promise:i(this,tt),isEnabled:k(s.enabled,t)!==!1};if(this.options.experimental_prefetchInRender){const F=Bt=>{M.status==="error"?Bt.reject(M.error):M.data!==void 0&&Bt.resolve(M.data)},it=()=>{const Bt=u(this,tt,M.promise=re());F(Bt)},mt=i(this,tt);switch(mt.status){case"pending":t.queryHash===r.queryHash&&F(mt);break;case"fulfilled":(M.status==="error"||M.data!==mt.value)&&it();break;case"rejected":(M.status!=="error"||M.error!==mt.reason)&&it();break}}return M}updateResult(){const t=i(this,I),s=this.createResult(i(this,b),this.options);if(u(this,lt,i(this,b).state),u(this,Pt,this.options),i(this,lt).data!==void 0&&u(this,Et,i(this,b)),te(s,t))return;u(this,I,s);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:n}=this.options,a=typeof n=="function"?n():n;if(a==="all"||!a&&!i(this,Qt).size)return!0;const h=new Set(a??i(this,Qt));return this.options.throwOnError&&h.add("error"),Object.keys(i(this,I)).some(c=>{const l=c;return i(this,I)[l]!==t[l]&&h.has(l)})};v(this,Wt,Be).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&v(this,Lt,ue).call(this)}},A=new WeakMap,b=new WeakMap,jt=new WeakMap,I=new WeakMap,lt=new WeakMap,Pt=new WeakMap,tt=new WeakMap,et=new WeakMap,Ut=new WeakMap,Ft=new WeakMap,Et=new WeakMap,dt=new WeakMap,ft=new WeakMap,st=new WeakMap,Qt=new WeakMap,yt=new WeakSet,Dt=function(t){v(this,Gt,ce).call(this);let s=i(this,b).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(j)),s},qt=new WeakSet,ie=function(){v(this,Kt,oe).call(this);const t=rt(this.options.staleTime,i(this,b));if(pt||i(this,I).isStale||!Zt(t))return;const r=qe(i(this,I).dataUpdatedAt,t)+1;u(this,dt,setTimeout(()=>{i(this,I).isStale||this.updateResult()},r))},_t=new WeakSet,ne=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,b)):this.options.refetchInterval)??!1},kt=new WeakSet,ae=function(t){v(this,Ht,he).call(this),u(this,st,t),!(pt||k(this.options.enabled,i(this,b))===!1||!Zt(i(this,st))||i(this,st)===0)&&u(this,ft,setInterval(()=>{(this.options.refetchIntervalInBackground||pe.isFocused())&&v(this,yt,Dt).call(this)},i(this,st)))},Lt=new WeakSet,ue=function(){v(this,qt,ie).call(this),v(this,kt,ae).call(this,v(this,_t,ne).call(this))},Kt=new WeakSet,oe=function(){i(this,dt)&&(clearTimeout(i(this,dt)),u(this,dt,void 0))},Ht=new WeakSet,he=function(){i(this,ft)&&(clearInterval(i(this,ft)),u(this,ft,void 0))},Gt=new WeakSet,ce=function(){const t=i(this,A).getQueryCache().build(i(this,A),this.options);if(t===i(this,b))return;const s=i(this,b);u(this,b,t),u(this,jt,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},Wt=new WeakSet,Be=function(t){Q.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,I))}),i(this,A).getQueryCache().notify({query:i(this,b),type:"observerResultsUpdated"})})},Ae);function gs(e,t){return k(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Re(e,t){return gs(e,t)||e.state.data!==void 0&&le(e,t,t.refetchOnMount)}function le(e,t,s){if(k(t.enabled,e)!==!1&&rt(t.staleTime,e)!=="static"){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&me(e,t)}return!1}function Se(e,t,s,r){return(e!==t||k(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&me(e,s)}function me(e,t){return k(t.enabled,e)!==!1&&e.isStaleByTime(rt(t.staleTime,e))}function ws(e,t){return!te(e.getCurrentResult(),t)}var ze=q.createContext(void 0),Cs=e=>{const t=q.useContext(ze);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},_s=({client:e,children:t})=>(q.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),ts(ze.Provider,{value:e,children:t})),$e=q.createContext(!1),Os=()=>q.useContext($e);$e.Provider;function Rs(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var Ss=q.createContext(Rs()),Ps=()=>q.useContext(Ss),Fs=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Es=e=>{q.useEffect(()=>{e.clearReset()},[e])},Qs=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(n&&e.data===void 0||ns(s,[e.error,r])),xs=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...r)=>t(s(...r)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},Ds=(e,t)=>e.isLoading&&e.isFetching&&!t,Ms=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Pe=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function Ts(e,t,s){var d,p,m,w,f;const r=Os(),n=Ps(),a=Cs(s),h=a.defaultQueryOptions(e);(p=(d=a.getDefaultOptions().queries)==null?void 0:d._experimental_beforeQuery)==null||p.call(d,h),h._optimisticResults=r?"isRestoring":"optimistic",xs(h),Fs(h,n),Es(n);const c=!a.getQueryCache().get(h.queryHash),[l]=q.useState(()=>new t(a,h)),g=l.getOptimisticResult(h),C=!r&&e.subscribed!==!1;if(q.useSyncExternalStore(q.useCallback(y=>{const S=C?l.subscribe(Q.batchCalls(y)):j;return l.updateResult(),S},[l,C]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),q.useEffect(()=>{l.setOptions(h)},[h,l]),Ms(h,g))throw Pe(h,l,n);if(Qs({result:g,errorResetBoundary:n,throwOnError:h.throwOnError,query:a.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw g.error;if((w=(m=a.getDefaultOptions().queries)==null?void 0:m._experimental_afterQuery)==null||w.call(m,h,g),h.experimental_prefetchInRender&&!pt&&Ds(g,r)){const y=c?Pe(h,l,n):(f=a.getQueryCache().get(h.queryHash))==null?void 0:f.promise;y==null||y.catch(j).finally(()=>{l.updateResult()})}return h.notifyOnChangeProps?g:l.trackResult(g)}function ks(e,t){return Ts(e,bs,t)}export{js as F,qs as Q,ts as a,_s as b,Us as j,ks as u};
//# sourceMappingURL=query-714297fe.js.map

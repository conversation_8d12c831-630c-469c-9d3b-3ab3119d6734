import{j as h,a as l,u as he,F as Mr,Q as zr,b as Br}from"./query-714297fe.js";import{a as Ur,r as $,b as Qe}from"./vendor-b69f2a9f.js";import{L as de,u as Wt,a as qr,B as Dr,R as $r,b as $e}from"./router-23a7c6cb.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(s){if(s.ep)return;s.ep=!0;const o=r(s);fetch(s.href,o)}})();var Kt,Rt=Ur;Kt=Rt.createRoot,Rt.hydrateRoot;/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hr=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Gr=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),Et=e=>{const t=Gr(e);return t.charAt(0).toUpperCase()+t.slice(1)},Qt=(...e)=>e.filter((t,r,n)=>!!t&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),Vr=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Jr={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wr=$.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:s="",children:o,iconNode:a,...i},m)=>$.createElement("svg",{ref:m,...Jr,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:Qt("lucide",s),...!o&&!Vr(i)&&{"aria-hidden":"true"},...i},[...a.map(([d,u])=>$.createElement(d,u)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=(e,t)=>{const r=$.forwardRef(({className:n,...s},o)=>$.createElement(Wr,{ref:o,iconNode:t,className:Qt(`lucide-${Hr(Et(e))}`,`lucide-${e}`,n),...s}));return r.displayName=Et(e),r};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kr=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],Qr=X("arrow-left",Kr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Zr=X("menu",Xr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yr=[["path",{d:"M5 12h14",key:"1ays0h"}]],en=X("minus",Yr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tn=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],rn=X("plus",tn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nn=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],sn=X("search",nn);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const on=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],ct=X("shopping-cart",on);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const an=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Xt=X("star",an);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ln=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Zt=X("x",ln),At=e=>{let t;const r=new Set,n=(d,u)=>{const f=typeof d=="function"?d(t):d;if(!Object.is(f,t)){const v=t;t=u??(typeof f!="object"||f===null)?f:Object.assign({},t,f),r.forEach(R=>R(t,v))}},s=()=>t,i={setState:n,getState:s,getInitialState:()=>m,subscribe:d=>(r.add(d),()=>r.delete(d))},m=t=e(n,s,i);return i},cn=e=>e?At(e):At,dn=e=>e;function un(e,t=dn){const r=Qe.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return Qe.useDebugValue(r),r}const Pt=e=>{const t=cn(e),r=n=>un(t,n);return Object.assign(r,t),r},mn=e=>e?Pt(e):Pt;function fn(e,t){let r;try{r=e()}catch{return}return{getItem:s=>{var o;const a=m=>m===null?null:JSON.parse(m,t==null?void 0:t.reviver),i=(o=r.getItem(s))!=null?o:null;return i instanceof Promise?i.then(a):a(i)},setItem:(s,o)=>r.setItem(s,JSON.stringify(o,t==null?void 0:t.replacer)),removeItem:s=>r.removeItem(s)}}const Xe=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(n){return Xe(n)(r)},catch(n){return this}}}catch(r){return{then(n){return this},catch(n){return Xe(n)(r)}}}},pn=(e,t)=>(r,n,s)=>{let o={storage:fn(()=>localStorage),partialize:p=>p,version:0,merge:(p,b)=>({...b,...p}),...t},a=!1;const i=new Set,m=new Set;let d=o.storage;if(!d)return e((...p)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...p)},n,s);const u=()=>{const p=o.partialize({...n()});return d.setItem(o.name,{state:p,version:o.version})},f=s.setState;s.setState=(p,b)=>{f(p,b),u()};const v=e((...p)=>{r(...p),u()},n,s);s.getInitialState=()=>v;let R;const g=()=>{var p,b;if(!d)return;a=!1,i.forEach(k=>{var A;return k((A=n())!=null?A:v)});const E=((b=o.onRehydrateStorage)==null?void 0:b.call(o,(p=n())!=null?p:v))||void 0;return Xe(d.getItem.bind(d))(o.name).then(k=>{if(k)if(typeof k.version=="number"&&k.version!==o.version){if(o.migrate){const A=o.migrate(k.state,k.version);return A instanceof Promise?A.then(I=>[!0,I]):[!0,A]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,k.state];return[!1,void 0]}).then(k=>{var A;const[I,T]=k;if(R=o.merge(T,(A=n())!=null?A:v),r(R,!0),I)return u()}).then(()=>{E==null||E(R,void 0),R=n(),a=!0,m.forEach(k=>k(R))}).catch(k=>{E==null||E(void 0,k)})};return s.persist={setOptions:p=>{o={...o,...p},p.storage&&(d=p.storage)},clearStorage:()=>{d==null||d.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>g(),hasHydrated:()=>a,onHydrate:p=>(i.add(p),()=>{i.delete(p)}),onFinishHydration:p=>(m.add(p),()=>{m.delete(p)})},o.skipHydration||g(),R||v},hn=pn,He=e=>{const t=e.reduce((n,s)=>n+s.quantity,0),r=e.reduce((n,s)=>n+s.product.price*s.quantity,0);return{totalItems:t,totalPrice:r}},dt=mn()(hn((e,t)=>({items:[],totalItems:0,totalPrice:0,addItem:(r,n=1)=>{e(s=>{const o=s.items.find(d=>d.id===r.id);let a;if(o)a=s.items.map(d=>d.id===r.id?{...d,quantity:d.quantity+n}:d);else{const d={id:r.id,product:r,quantity:n,selectedAt:new Date().toISOString()};a=[...s.items,d]}const{totalItems:i,totalPrice:m}=He(a);return{items:a,totalItems:i,totalPrice:m}})},removeItem:r=>{e(n=>{const s=n.items.filter(i=>i.id!==r),{totalItems:o,totalPrice:a}=He(s);return{items:s,totalItems:o,totalPrice:a}})},updateQuantity:(r,n)=>{if(n<=0){t().removeItem(r);return}e(s=>{const o=s.items.map(m=>m.id===r?{...m,quantity:n}:m),{totalItems:a,totalPrice:i}=He(o);return{items:o,totalItems:a,totalPrice:i}})},clearCart:()=>{e({items:[],totalItems:0,totalPrice:0})},getItemQuantity:r=>{const n=t().items.find(s=>s.id===r);return n?n.quantity:0},isInCart:r=>t().items.some(n=>n.id===r)}),{name:"cart-storage",partialize:e=>({items:e.items,totalItems:e.totalItems,totalPrice:e.totalPrice})}));function Yt(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=Yt(e[t]))&&(n&&(n+=" "),n+=r);else for(t in e)e[t]&&(n&&(n+=" "),n+=t);return n}function gn(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=Yt(e))&&(n&&(n+=" "),n+=t);return n}const ut="-",bn=e=>{const t=xn(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:a=>{const i=a.split(ut);return i[0]===""&&i.length!==1&&i.shift(),er(i,t)||yn(a)},getConflictingClassGroupIds:(a,i)=>{const m=r[a]||[];return i&&n[a]?[...m,...n[a]]:m}}},er=(e,t)=>{var a;if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),s=n?er(e.slice(1),n):void 0;if(s)return s;if(t.validators.length===0)return;const o=e.join(ut);return(a=t.validators.find(({validator:i})=>i(o)))==null?void 0:a.classGroupId},Tt=/^\[(.+)\]$/,yn=e=>{if(Tt.test(e)){const t=Tt.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},xn=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const s in r)Ze(r[s],n,s,t);return n},Ze=(e,t,r,n)=>{e.forEach(s=>{if(typeof s=="string"){const o=s===""?t:Ot(t,s);o.classGroupId=r;return}if(typeof s=="function"){if(wn(s)){Ze(s(n),t,r,n);return}t.validators.push({validator:s,classGroupId:r});return}Object.entries(s).forEach(([o,a])=>{Ze(a,Ot(t,o),r,n)})})},Ot=(e,t)=>{let r=e;return t.split(ut).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},wn=e=>e.isThemeGetter,Nn=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const s=(o,a)=>{r.set(o,a),t++,t>e&&(t=0,n=r,r=new Map)};return{get(o){let a=r.get(o);if(a!==void 0)return a;if((a=n.get(o))!==void 0)return s(o,a),a},set(o,a){r.has(o)?r.set(o,a):s(o,a)}}},Ye="!",et=":",vn=et.length,kn=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=s=>{const o=[];let a=0,i=0,m=0,d;for(let g=0;g<s.length;g++){let p=s[g];if(a===0&&i===0){if(p===et){o.push(s.slice(m,g)),m=g+vn;continue}if(p==="/"){d=g;continue}}p==="["?a++:p==="]"?a--:p==="("?i++:p===")"&&i--}const u=o.length===0?s:s.substring(m),f=Sn(u),v=f!==u,R=d&&d>m?d-m:void 0;return{modifiers:o,hasImportantModifier:v,baseClassName:f,maybePostfixModifierPosition:R}};if(t){const s=t+et,o=n;n=a=>a.startsWith(s)?o(a.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(r){const s=n;n=o=>r({className:o,parseClassName:s})}return n},Sn=e=>e.endsWith(Ye)?e.substring(0,e.length-1):e.startsWith(Ye)?e.substring(1):e,Cn=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const s=[];let o=[];return n.forEach(a=>{a[0]==="["||t[a]?(s.push(...o.sort(),a),o=[]):o.push(a)}),s.push(...o.sort()),s}},Rn=e=>({cache:Nn(e.cacheSize),parseClassName:kn(e),sortModifiers:Cn(e),...bn(e)}),En=/\s+/,An=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:s,sortModifiers:o}=t,a=[],i=e.trim().split(En);let m="";for(let d=i.length-1;d>=0;d-=1){const u=i[d],{isExternal:f,modifiers:v,hasImportantModifier:R,baseClassName:g,maybePostfixModifierPosition:p}=r(u);if(f){m=u+(m.length>0?" "+m:m);continue}let b=!!p,E=n(b?g.substring(0,p):g);if(!E){if(!b){m=u+(m.length>0?" "+m:m);continue}if(E=n(g),!E){m=u+(m.length>0?" "+m:m);continue}b=!1}const k=o(v).join(":"),A=R?k+Ye:k,I=A+E;if(a.includes(I))continue;a.push(I);const T=s(E,b);for(let P=0;P<T.length;++P){const O=T[P];a.push(A+O)}m=u+(m.length>0?" "+m:m)}return m};function Pn(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=tr(t))&&(n&&(n+=" "),n+=r);return n}const tr=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=tr(e[n]))&&(r&&(r+=" "),r+=t);return r};function Tn(e,...t){let r,n,s,o=a;function a(m){const d=t.reduce((u,f)=>f(u),e());return r=Rn(d),n=r.cache.get,s=r.cache.set,o=i,i(m)}function i(m){const d=n(m);if(d)return d;const u=An(m,r);return s(m,u),u}return function(){return o(Pn.apply(null,arguments))}}const _=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},rr=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,nr=/^\((?:(\w[\w-]*):)?(.+)\)$/i,On=/^\d+\/\d+$/,In=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ln=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,_n=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Fn=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,jn=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,se=e=>On.test(e),C=e=>!!e&&!Number.isNaN(Number(e)),Q=e=>!!e&&Number.isInteger(Number(e)),Ge=e=>e.endsWith("%")&&C(e.slice(0,-1)),W=e=>In.test(e),Mn=()=>!0,zn=e=>Ln.test(e)&&!_n.test(e),sr=()=>!1,Bn=e=>Fn.test(e),Un=e=>jn.test(e),qn=e=>!y(e)&&!x(e),Dn=e=>ae(e,ir,sr),y=e=>rr.test(e),Y=e=>ae(e,lr,zn),Ve=e=>ae(e,Jn,C),It=e=>ae(e,or,sr),$n=e=>ae(e,ar,Un),ve=e=>ae(e,cr,Bn),x=e=>nr.test(e),ue=e=>ie(e,lr),Hn=e=>ie(e,Wn),Lt=e=>ie(e,or),Gn=e=>ie(e,ir),Vn=e=>ie(e,ar),ke=e=>ie(e,cr,!0),ae=(e,t,r)=>{const n=rr.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},ie=(e,t,r=!1)=>{const n=nr.exec(e);return n?n[1]?t(n[1]):r:!1},or=e=>e==="position"||e==="percentage",ar=e=>e==="image"||e==="url",ir=e=>e==="length"||e==="size"||e==="bg-size",lr=e=>e==="length",Jn=e=>e==="number",Wn=e=>e==="family-name",cr=e=>e==="shadow",Kn=()=>{const e=_("color"),t=_("font"),r=_("text"),n=_("font-weight"),s=_("tracking"),o=_("leading"),a=_("breakpoint"),i=_("container"),m=_("spacing"),d=_("radius"),u=_("shadow"),f=_("inset-shadow"),v=_("text-shadow"),R=_("drop-shadow"),g=_("blur"),p=_("perspective"),b=_("aspect"),E=_("ease"),k=_("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],I=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],T=()=>[...I(),x,y],P=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto","contain","none"],w=()=>[x,y,m],q=()=>[se,"full","auto",...w()],yt=()=>[Q,"none","subgrid",x,y],xt=()=>["auto",{span:["full",Q,x,y]},Q,x,y],be=()=>[Q,"auto",x,y],wt=()=>["auto","min","max","fr",x,y],Ue=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ne=()=>["start","end","center","stretch","center-safe","end-safe"],J=()=>["auto",...w()],Z=()=>[se,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...w()],N=()=>[e,x,y],Nt=()=>[...I(),Lt,It,{position:[x,y]}],vt=()=>["no-repeat",{repeat:["","x","y","space","round"]}],kt=()=>["auto","cover","contain",Gn,Dn,{size:[x,y]}],qe=()=>[Ge,ue,Y],M=()=>["","none","full",d,x,y],B=()=>["",C,ue,Y],ye=()=>["solid","dashed","dotted","double"],St=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],F=()=>[C,Ge,Lt,It],Ct=()=>["","none",g,x,y],xe=()=>["none",C,x,y],we=()=>["none",C,x,y],De=()=>[C,x,y],Ne=()=>[se,"full",...w()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[W],breakpoint:[W],color:[Mn],container:[W],"drop-shadow":[W],ease:["in","out","in-out"],font:[qn],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[W],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[W],shadow:[W],spacing:["px",C],text:[W],"text-shadow":[W],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",se,y,x,b]}],container:["container"],columns:[{columns:[C,y,x,i]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:T()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:q()}],"inset-x":[{"inset-x":q()}],"inset-y":[{"inset-y":q()}],start:[{start:q()}],end:[{end:q()}],top:[{top:q()}],right:[{right:q()}],bottom:[{bottom:q()}],left:[{left:q()}],visibility:["visible","invisible","collapse"],z:[{z:[Q,"auto",x,y]}],basis:[{basis:[se,"full","auto",i,...w()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,se,"auto","initial","none",y]}],grow:[{grow:["",C,x,y]}],shrink:[{shrink:["",C,x,y]}],order:[{order:[Q,"first","last","none",x,y]}],"grid-cols":[{"grid-cols":yt()}],"col-start-end":[{col:xt()}],"col-start":[{"col-start":be()}],"col-end":[{"col-end":be()}],"grid-rows":[{"grid-rows":yt()}],"row-start-end":[{row:xt()}],"row-start":[{"row-start":be()}],"row-end":[{"row-end":be()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":wt()}],"auto-rows":[{"auto-rows":wt()}],gap:[{gap:w()}],"gap-x":[{"gap-x":w()}],"gap-y":[{"gap-y":w()}],"justify-content":[{justify:[...Ue(),"normal"]}],"justify-items":[{"justify-items":[...ne(),"normal"]}],"justify-self":[{"justify-self":["auto",...ne()]}],"align-content":[{content:["normal",...Ue()]}],"align-items":[{items:[...ne(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ne(),{baseline:["","last"]}]}],"place-content":[{"place-content":Ue()}],"place-items":[{"place-items":[...ne(),"baseline"]}],"place-self":[{"place-self":["auto",...ne()]}],p:[{p:w()}],px:[{px:w()}],py:[{py:w()}],ps:[{ps:w()}],pe:[{pe:w()}],pt:[{pt:w()}],pr:[{pr:w()}],pb:[{pb:w()}],pl:[{pl:w()}],m:[{m:J()}],mx:[{mx:J()}],my:[{my:J()}],ms:[{ms:J()}],me:[{me:J()}],mt:[{mt:J()}],mr:[{mr:J()}],mb:[{mb:J()}],ml:[{ml:J()}],"space-x":[{"space-x":w()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":w()}],"space-y-reverse":["space-y-reverse"],size:[{size:Z()}],w:[{w:[i,"screen",...Z()]}],"min-w":[{"min-w":[i,"screen","none",...Z()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[a]},...Z()]}],h:[{h:["screen","lh",...Z()]}],"min-h":[{"min-h":["screen","lh","none",...Z()]}],"max-h":[{"max-h":["screen","lh",...Z()]}],"font-size":[{text:["base",r,ue,Y]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,x,Ve]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Ge,y]}],"font-family":[{font:[Hn,y,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,x,y]}],"line-clamp":[{"line-clamp":[C,"none",x,Ve]}],leading:[{leading:[o,...w()]}],"list-image":[{"list-image":["none",x,y]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",x,y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:N()}],"text-color":[{text:N()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ye(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",x,Y]}],"text-decoration-color":[{decoration:N()}],"underline-offset":[{"underline-offset":[C,"auto",x,y]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:w()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",x,y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",x,y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Nt()}],"bg-repeat":[{bg:vt()}],"bg-size":[{bg:kt()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Q,x,y],radial:["",x,y],conic:[Q,x,y]},Vn,$n]}],"bg-color":[{bg:N()}],"gradient-from-pos":[{from:qe()}],"gradient-via-pos":[{via:qe()}],"gradient-to-pos":[{to:qe()}],"gradient-from":[{from:N()}],"gradient-via":[{via:N()}],"gradient-to":[{to:N()}],rounded:[{rounded:M()}],"rounded-s":[{"rounded-s":M()}],"rounded-e":[{"rounded-e":M()}],"rounded-t":[{"rounded-t":M()}],"rounded-r":[{"rounded-r":M()}],"rounded-b":[{"rounded-b":M()}],"rounded-l":[{"rounded-l":M()}],"rounded-ss":[{"rounded-ss":M()}],"rounded-se":[{"rounded-se":M()}],"rounded-ee":[{"rounded-ee":M()}],"rounded-es":[{"rounded-es":M()}],"rounded-tl":[{"rounded-tl":M()}],"rounded-tr":[{"rounded-tr":M()}],"rounded-br":[{"rounded-br":M()}],"rounded-bl":[{"rounded-bl":M()}],"border-w":[{border:B()}],"border-w-x":[{"border-x":B()}],"border-w-y":[{"border-y":B()}],"border-w-s":[{"border-s":B()}],"border-w-e":[{"border-e":B()}],"border-w-t":[{"border-t":B()}],"border-w-r":[{"border-r":B()}],"border-w-b":[{"border-b":B()}],"border-w-l":[{"border-l":B()}],"divide-x":[{"divide-x":B()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":B()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ye(),"hidden","none"]}],"divide-style":[{divide:[...ye(),"hidden","none"]}],"border-color":[{border:N()}],"border-color-x":[{"border-x":N()}],"border-color-y":[{"border-y":N()}],"border-color-s":[{"border-s":N()}],"border-color-e":[{"border-e":N()}],"border-color-t":[{"border-t":N()}],"border-color-r":[{"border-r":N()}],"border-color-b":[{"border-b":N()}],"border-color-l":[{"border-l":N()}],"divide-color":[{divide:N()}],"outline-style":[{outline:[...ye(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,x,y]}],"outline-w":[{outline:["",C,ue,Y]}],"outline-color":[{outline:N()}],shadow:[{shadow:["","none",u,ke,ve]}],"shadow-color":[{shadow:N()}],"inset-shadow":[{"inset-shadow":["none",f,ke,ve]}],"inset-shadow-color":[{"inset-shadow":N()}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:N()}],"ring-offset-w":[{"ring-offset":[C,Y]}],"ring-offset-color":[{"ring-offset":N()}],"inset-ring-w":[{"inset-ring":B()}],"inset-ring-color":[{"inset-ring":N()}],"text-shadow":[{"text-shadow":["none",v,ke,ve]}],"text-shadow-color":[{"text-shadow":N()}],opacity:[{opacity:[C,x,y]}],"mix-blend":[{"mix-blend":[...St(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":St()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":F()}],"mask-image-linear-to-pos":[{"mask-linear-to":F()}],"mask-image-linear-from-color":[{"mask-linear-from":N()}],"mask-image-linear-to-color":[{"mask-linear-to":N()}],"mask-image-t-from-pos":[{"mask-t-from":F()}],"mask-image-t-to-pos":[{"mask-t-to":F()}],"mask-image-t-from-color":[{"mask-t-from":N()}],"mask-image-t-to-color":[{"mask-t-to":N()}],"mask-image-r-from-pos":[{"mask-r-from":F()}],"mask-image-r-to-pos":[{"mask-r-to":F()}],"mask-image-r-from-color":[{"mask-r-from":N()}],"mask-image-r-to-color":[{"mask-r-to":N()}],"mask-image-b-from-pos":[{"mask-b-from":F()}],"mask-image-b-to-pos":[{"mask-b-to":F()}],"mask-image-b-from-color":[{"mask-b-from":N()}],"mask-image-b-to-color":[{"mask-b-to":N()}],"mask-image-l-from-pos":[{"mask-l-from":F()}],"mask-image-l-to-pos":[{"mask-l-to":F()}],"mask-image-l-from-color":[{"mask-l-from":N()}],"mask-image-l-to-color":[{"mask-l-to":N()}],"mask-image-x-from-pos":[{"mask-x-from":F()}],"mask-image-x-to-pos":[{"mask-x-to":F()}],"mask-image-x-from-color":[{"mask-x-from":N()}],"mask-image-x-to-color":[{"mask-x-to":N()}],"mask-image-y-from-pos":[{"mask-y-from":F()}],"mask-image-y-to-pos":[{"mask-y-to":F()}],"mask-image-y-from-color":[{"mask-y-from":N()}],"mask-image-y-to-color":[{"mask-y-to":N()}],"mask-image-radial":[{"mask-radial":[x,y]}],"mask-image-radial-from-pos":[{"mask-radial-from":F()}],"mask-image-radial-to-pos":[{"mask-radial-to":F()}],"mask-image-radial-from-color":[{"mask-radial-from":N()}],"mask-image-radial-to-color":[{"mask-radial-to":N()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":I()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":F()}],"mask-image-conic-to-pos":[{"mask-conic-to":F()}],"mask-image-conic-from-color":[{"mask-conic-from":N()}],"mask-image-conic-to-color":[{"mask-conic-to":N()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Nt()}],"mask-repeat":[{mask:vt()}],"mask-size":[{mask:kt()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",x,y]}],filter:[{filter:["","none",x,y]}],blur:[{blur:Ct()}],brightness:[{brightness:[C,x,y]}],contrast:[{contrast:[C,x,y]}],"drop-shadow":[{"drop-shadow":["","none",R,ke,ve]}],"drop-shadow-color":[{"drop-shadow":N()}],grayscale:[{grayscale:["",C,x,y]}],"hue-rotate":[{"hue-rotate":[C,x,y]}],invert:[{invert:["",C,x,y]}],saturate:[{saturate:[C,x,y]}],sepia:[{sepia:["",C,x,y]}],"backdrop-filter":[{"backdrop-filter":["","none",x,y]}],"backdrop-blur":[{"backdrop-blur":Ct()}],"backdrop-brightness":[{"backdrop-brightness":[C,x,y]}],"backdrop-contrast":[{"backdrop-contrast":[C,x,y]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,x,y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,x,y]}],"backdrop-invert":[{"backdrop-invert":["",C,x,y]}],"backdrop-opacity":[{"backdrop-opacity":[C,x,y]}],"backdrop-saturate":[{"backdrop-saturate":[C,x,y]}],"backdrop-sepia":[{"backdrop-sepia":["",C,x,y]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":w()}],"border-spacing-x":[{"border-spacing-x":w()}],"border-spacing-y":[{"border-spacing-y":w()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",x,y]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",x,y]}],ease:[{ease:["linear","initial",E,x,y]}],delay:[{delay:[C,x,y]}],animate:[{animate:["none",k,x,y]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,x,y]}],"perspective-origin":[{"perspective-origin":T()}],rotate:[{rotate:xe()}],"rotate-x":[{"rotate-x":xe()}],"rotate-y":[{"rotate-y":xe()}],"rotate-z":[{"rotate-z":xe()}],scale:[{scale:we()}],"scale-x":[{"scale-x":we()}],"scale-y":[{"scale-y":we()}],"scale-z":[{"scale-z":we()}],"scale-3d":["scale-3d"],skew:[{skew:De()}],"skew-x":[{"skew-x":De()}],"skew-y":[{"skew-y":De()}],transform:[{transform:[x,y,"","none","gpu","cpu"]}],"transform-origin":[{origin:T()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ne()}],"translate-x":[{"translate-x":Ne()}],"translate-y":[{"translate-y":Ne()}],"translate-z":[{"translate-z":Ne()}],"translate-none":["translate-none"],accent:[{accent:N()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:N()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",x,y]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":w()}],"scroll-mx":[{"scroll-mx":w()}],"scroll-my":[{"scroll-my":w()}],"scroll-ms":[{"scroll-ms":w()}],"scroll-me":[{"scroll-me":w()}],"scroll-mt":[{"scroll-mt":w()}],"scroll-mr":[{"scroll-mr":w()}],"scroll-mb":[{"scroll-mb":w()}],"scroll-ml":[{"scroll-ml":w()}],"scroll-p":[{"scroll-p":w()}],"scroll-px":[{"scroll-px":w()}],"scroll-py":[{"scroll-py":w()}],"scroll-ps":[{"scroll-ps":w()}],"scroll-pe":[{"scroll-pe":w()}],"scroll-pt":[{"scroll-pt":w()}],"scroll-pr":[{"scroll-pr":w()}],"scroll-pb":[{"scroll-pb":w()}],"scroll-pl":[{"scroll-pl":w()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",x,y]}],fill:[{fill:["none",...N()]}],"stroke-w":[{stroke:[C,ue,Y,Ve]}],stroke:[{stroke:["none",...N()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Qn=Tn(Kn);function re(...e){return Qn(gn(e))}function oe(e,t="¥"){return`${t}${e.toFixed(2)}`}function dr(e,t){return e<=t?0:Math.round((e-t)/e*100)}function Xn(e,t){let r;return(...n)=>{clearTimeout(r),r=setTimeout(()=>e(...n),t)}}function ur(e,t=5){return Array.from({length:t},(r,n)=>n<Math.floor(e))}const D=({variant:e="primary",size:t="md",loading:r=!1,disabled:n,className:s,children:o,...a})=>{const i="btn",m={primary:"btn-primary",secondary:"btn-secondary",outline:"btn-outline",ghost:"btn-ghost",danger:"btn-danger"},d={sm:"btn-sm",md:"btn-md",lg:"btn-lg"};return h("button",{className:re(i,m[e],d[t],r&&"opacity-50 cursor-not-allowed",s),disabled:n||r,...a,children:[r&&l("div",{className:"loading-spinner w-4 h-4 mr-2"}),o]})},_t=({onSearch:e,placeholder:t="搜索商品...",defaultValue:r="",className:n})=>{const[s,o]=$.useState(r),a=$.useCallback(Xn(u=>{e(u.trim())},300),[e]);return l("form",{onSubmit:u=>{u.preventDefault(),e(s.trim())},className:n,children:h("div",{className:"relative",children:[l("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l(sn,{className:"h-5 w-5 text-gray-400"})}),l("input",{type:"text",value:s,onChange:u=>{const f=u.target.value;o(f),a(f)},placeholder:t,className:"input pl-10 pr-10 w-full"}),s&&l("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:l(D,{type:"button",variant:"ghost",size:"sm",onClick:()=>{o(""),e("")},className:"p-1 hover:bg-gray-100 rounded-full",children:l(Zt,{className:"h-4 w-4 text-gray-400"})})})]})})},Zn=({onSearch:e})=>{const[t,r]=$.useState(!1),{totalItems:n,totalPrice:s}=dt(),o=()=>{r(!t)};return h("header",{className:"bg-white shadow-sm border-b sticky top-0 z-40",children:[h("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[h("div",{className:"flex items-center justify-between h-16",children:[l("div",{className:"flex items-center",children:h(de,{to:"/",className:"flex items-center space-x-2",children:[l("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:l("span",{className:"text-white font-bold text-lg",children:"🛍️"})}),l("span",{className:"font-bold text-xl text-gray-900 hidden sm:block",children:"商品展示"})]})}),l("div",{className:"hidden md:block flex-1 max-w-lg mx-8",children:l(_t,{onSearch:e})}),h("div",{className:"flex items-center space-x-4",children:[l(de,{to:"/cart",className:"relative",children:h(D,{variant:"ghost",size:"sm",className:"p-2",children:[l(ct,{className:"w-6 h-6"}),n>0&&l("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:n>99?"99+":n})]})}),l(D,{variant:"ghost",size:"sm",className:"md:hidden p-2",onClick:o,children:t?l(Zt,{className:"w-6 h-6"}):l(Zr,{className:"w-6 h-6"})})]})]}),l("div",{className:"md:hidden pb-4",children:l(_t,{onSearch:e})})]}),t&&l("div",{className:"md:hidden bg-white border-t",children:h("div",{className:"px-4 py-4 space-y-4",children:[l(de,{to:"/",className:"block text-gray-700 hover:text-primary-600 font-medium",onClick:()=>r(!1),children:"首页"}),l(de,{to:"/categories",className:"block text-gray-700 hover:text-primary-600 font-medium",onClick:()=>r(!1),children:"分类"}),h(de,{to:"/cart",className:"flex items-center justify-between text-gray-700 hover:text-primary-600 font-medium",onClick:()=>r(!1),children:[l("span",{children:"购物车"}),h("div",{className:"flex items-center space-x-2",children:[h("span",{className:"text-sm text-gray-500",children:[n," 件商品"]}),l("span",{className:"text-sm font-medium text-primary-600",children:oe(s)})]})]})]})})]})};function mr(e,t){return function(){return e.apply(t,arguments)}}const{toString:Yn}=Object.prototype,{getPrototypeOf:mt}=Object,{iterator:Oe,toStringTag:fr}=Symbol,Ie=(e=>t=>{const r=Yn.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),G=e=>(e=e.toLowerCase(),t=>Ie(t)===e),Le=e=>t=>typeof t===e,{isArray:le}=Array,pe=Le("undefined");function es(e){return e!==null&&!pe(e)&&e.constructor!==null&&!pe(e.constructor)&&z(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const pr=G("ArrayBuffer");function ts(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&pr(e.buffer),t}const rs=Le("string"),z=Le("function"),hr=Le("number"),_e=e=>e!==null&&typeof e=="object",ns=e=>e===!0||e===!1,Se=e=>{if(Ie(e)!=="object")return!1;const t=mt(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(fr in e)&&!(Oe in e)},ss=G("Date"),os=G("File"),as=G("Blob"),is=G("FileList"),ls=e=>_e(e)&&z(e.pipe),cs=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||z(e.append)&&((t=Ie(e))==="formdata"||t==="object"&&z(e.toString)&&e.toString()==="[object FormData]"))},ds=G("URLSearchParams"),[us,ms,fs,ps]=["ReadableStream","Request","Response","Headers"].map(G),hs=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ge(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),le(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),a=o.length;let i;for(n=0;n<a;n++)i=o[n],t.call(null,e[i],i,e)}}function gr(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const ee=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),br=e=>!pe(e)&&e!==ee;function tt(){const{caseless:e}=br(this)&&this||{},t={},r=(n,s)=>{const o=e&&gr(t,s)||s;Se(t[o])&&Se(n)?t[o]=tt(t[o],n):Se(n)?t[o]=tt({},n):le(n)?t[o]=n.slice():t[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&ge(arguments[n],r);return t}const gs=(e,t,r,{allOwnKeys:n}={})=>(ge(t,(s,o)=>{r&&z(s)?e[o]=mr(s,r):e[o]=s},{allOwnKeys:n}),e),bs=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),ys=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},xs=(e,t,r,n)=>{let s,o,a;const i={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)a=s[o],(!n||n(a,e,t))&&!i[a]&&(t[a]=e[a],i[a]=!0);e=r!==!1&&mt(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},ws=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Ns=e=>{if(!e)return null;if(le(e))return e;let t=e.length;if(!hr(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},vs=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&mt(Uint8Array)),ks=(e,t)=>{const n=(e&&e[Oe]).call(e);let s;for(;(s=n.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Ss=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},Cs=G("HTMLFormElement"),Rs=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),Ft=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Es=G("RegExp"),yr=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};ge(r,(s,o)=>{let a;(a=t(s,o,e))!==!1&&(n[o]=a||s)}),Object.defineProperties(e,n)},As=e=>{yr(e,(t,r)=>{if(z(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(z(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Ps=(e,t)=>{const r={},n=s=>{s.forEach(o=>{r[o]=!0})};return le(e)?n(e):n(String(e).split(t)),r},Ts=()=>{},Os=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Is(e){return!!(e&&z(e.append)&&e[fr]==="FormData"&&e[Oe])}const Ls=e=>{const t=new Array(10),r=(n,s)=>{if(_e(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[s]=n;const o=le(n)?[]:{};return ge(n,(a,i)=>{const m=r(a,s+1);!pe(m)&&(o[i]=m)}),t[s]=void 0,o}}return n};return r(e,0)},_s=G("AsyncFunction"),Fs=e=>e&&(_e(e)||z(e))&&z(e.then)&&z(e.catch),xr=((e,t)=>e?setImmediate:t?((r,n)=>(ee.addEventListener("message",({source:s,data:o})=>{s===ee&&o===r&&n.length&&n.shift()()},!1),s=>{n.push(s),ee.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",z(ee.postMessage)),js=typeof queueMicrotask<"u"?queueMicrotask.bind(ee):typeof process<"u"&&process.nextTick||xr,Ms=e=>e!=null&&z(e[Oe]),c={isArray:le,isArrayBuffer:pr,isBuffer:es,isFormData:cs,isArrayBufferView:ts,isString:rs,isNumber:hr,isBoolean:ns,isObject:_e,isPlainObject:Se,isReadableStream:us,isRequest:ms,isResponse:fs,isHeaders:ps,isUndefined:pe,isDate:ss,isFile:os,isBlob:as,isRegExp:Es,isFunction:z,isStream:ls,isURLSearchParams:ds,isTypedArray:vs,isFileList:is,forEach:ge,merge:tt,extend:gs,trim:hs,stripBOM:bs,inherits:ys,toFlatObject:xs,kindOf:Ie,kindOfTest:G,endsWith:ws,toArray:Ns,forEachEntry:ks,matchAll:Ss,isHTMLForm:Cs,hasOwnProperty:Ft,hasOwnProp:Ft,reduceDescriptors:yr,freezeMethods:As,toObjectSet:Ps,toCamelCase:Rs,noop:Ts,toFiniteNumber:Os,findKey:gr,global:ee,isContextDefined:br,isSpecCompliantForm:Is,toJSONObject:Ls,isAsyncFn:_s,isThenable:Fs,setImmediate:xr,asap:js,isIterable:Ms};function S(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}c.inherits(S,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:c.toJSONObject(this.config),code:this.code,status:this.status}}});const wr=S.prototype,Nr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Nr[e]={value:e}});Object.defineProperties(S,Nr);Object.defineProperty(wr,"isAxiosError",{value:!0});S.from=(e,t,r,n,s,o)=>{const a=Object.create(wr);return c.toFlatObject(e,a,function(m){return m!==Error.prototype},i=>i!=="isAxiosError"),S.call(a,e.message,t,r,n,s),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};const zs=null;function rt(e){return c.isPlainObject(e)||c.isArray(e)}function vr(e){return c.endsWith(e,"[]")?e.slice(0,-2):e}function jt(e,t,r){return e?e.concat(t).map(function(s,o){return s=vr(s),!r&&o?"["+s+"]":s}).join(r?".":""):t}function Bs(e){return c.isArray(e)&&!e.some(rt)}const Us=c.toFlatObject(c,{},null,function(t){return/^is[A-Z]/.test(t)});function Fe(e,t,r){if(!c.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=c.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,b){return!c.isUndefined(b[p])});const n=r.metaTokens,s=r.visitor||u,o=r.dots,a=r.indexes,m=(r.Blob||typeof Blob<"u"&&Blob)&&c.isSpecCompliantForm(t);if(!c.isFunction(s))throw new TypeError("visitor must be a function");function d(g){if(g===null)return"";if(c.isDate(g))return g.toISOString();if(c.isBoolean(g))return g.toString();if(!m&&c.isBlob(g))throw new S("Blob is not supported. Use a Buffer instead.");return c.isArrayBuffer(g)||c.isTypedArray(g)?m&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function u(g,p,b){let E=g;if(g&&!b&&typeof g=="object"){if(c.endsWith(p,"{}"))p=n?p:p.slice(0,-2),g=JSON.stringify(g);else if(c.isArray(g)&&Bs(g)||(c.isFileList(g)||c.endsWith(p,"[]"))&&(E=c.toArray(g)))return p=vr(p),E.forEach(function(A,I){!(c.isUndefined(A)||A===null)&&t.append(a===!0?jt([p],I,o):a===null?p:p+"[]",d(A))}),!1}return rt(g)?!0:(t.append(jt(b,p,o),d(g)),!1)}const f=[],v=Object.assign(Us,{defaultVisitor:u,convertValue:d,isVisitable:rt});function R(g,p){if(!c.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+p.join("."));f.push(g),c.forEach(g,function(E,k){(!(c.isUndefined(E)||E===null)&&s.call(t,E,c.isString(k)?k.trim():k,p,v))===!0&&R(E,p?p.concat(k):[k])}),f.pop()}}if(!c.isObject(e))throw new TypeError("data must be an object");return R(e),t}function Mt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function ft(e,t){this._pairs=[],e&&Fe(e,this,t)}const kr=ft.prototype;kr.append=function(t,r){this._pairs.push([t,r])};kr.toString=function(t){const r=t?function(n){return t.call(this,n,Mt)}:Mt;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function qs(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Sr(e,t,r){if(!t)return e;const n=r&&r.encode||qs;c.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let o;if(s?o=s(t,r):o=c.isURLSearchParams(t)?t.toString():new ft(t,r).toString(n),o){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Ds{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){c.forEach(this.handlers,function(n){n!==null&&t(n)})}}const zt=Ds,Cr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},$s=typeof URLSearchParams<"u"?URLSearchParams:ft,Hs=typeof FormData<"u"?FormData:null,Gs=typeof Blob<"u"?Blob:null,Vs={isBrowser:!0,classes:{URLSearchParams:$s,FormData:Hs,Blob:Gs},protocols:["http","https","file","blob","url","data"]},pt=typeof window<"u"&&typeof document<"u",nt=typeof navigator=="object"&&navigator||void 0,Js=pt&&(!nt||["ReactNative","NativeScript","NS"].indexOf(nt.product)<0),Ws=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Ks=pt&&window.location.href||"http://localhost",Qs=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:pt,hasStandardBrowserEnv:Js,hasStandardBrowserWebWorkerEnv:Ws,navigator:nt,origin:Ks},Symbol.toStringTag,{value:"Module"})),j={...Qs,...Vs};function Xs(e,t){return Fe(e,new j.classes.URLSearchParams,Object.assign({visitor:function(r,n,s,o){return j.isNode&&c.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Zs(e){return c.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Ys(e){const t={},r=Object.keys(e);let n;const s=r.length;let o;for(n=0;n<s;n++)o=r[n],t[o]=e[o];return t}function Rr(e){function t(r,n,s,o){let a=r[o++];if(a==="__proto__")return!0;const i=Number.isFinite(+a),m=o>=r.length;return a=!a&&c.isArray(s)?s.length:a,m?(c.hasOwnProp(s,a)?s[a]=[s[a],n]:s[a]=n,!i):((!s[a]||!c.isObject(s[a]))&&(s[a]=[]),t(r,n,s[a],o)&&c.isArray(s[a])&&(s[a]=Ys(s[a])),!i)}if(c.isFormData(e)&&c.isFunction(e.entries)){const r={};return c.forEachEntry(e,(n,s)=>{t(Zs(n),s,r,0)}),r}return null}function eo(e,t,r){if(c.isString(e))try{return(t||JSON.parse)(e),c.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const ht={transitional:Cr,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,o=c.isObject(t);if(o&&c.isHTMLForm(t)&&(t=new FormData(t)),c.isFormData(t))return s?JSON.stringify(Rr(t)):t;if(c.isArrayBuffer(t)||c.isBuffer(t)||c.isStream(t)||c.isFile(t)||c.isBlob(t)||c.isReadableStream(t))return t;if(c.isArrayBufferView(t))return t.buffer;if(c.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let i;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Xs(t,this.formSerializer).toString();if((i=c.isFileList(t))||n.indexOf("multipart/form-data")>-1){const m=this.env&&this.env.FormData;return Fe(i?{"files[]":t}:t,m&&new m,this.formSerializer)}}return o||s?(r.setContentType("application/json",!1),eo(t)):t}],transformResponse:[function(t){const r=this.transitional||ht.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(c.isResponse(t)||c.isReadableStream(t))return t;if(t&&c.isString(t)&&(n&&!this.responseType||s)){const a=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(i){if(a)throw i.name==="SyntaxError"?S.from(i,S.ERR_BAD_RESPONSE,this,null,this.response):i}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:j.classes.FormData,Blob:j.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};c.forEach(["delete","get","head","post","put","patch"],e=>{ht.headers[e]={}});const gt=ht,to=c.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ro=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(a){s=a.indexOf(":"),r=a.substring(0,s).trim().toLowerCase(),n=a.substring(s+1).trim(),!(!r||t[r]&&to[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Bt=Symbol("internals");function me(e){return e&&String(e).trim().toLowerCase()}function Ce(e){return e===!1||e==null?e:c.isArray(e)?e.map(Ce):String(e)}function no(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const so=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Je(e,t,r,n,s){if(c.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!c.isString(t)){if(c.isString(n))return t.indexOf(n)!==-1;if(c.isRegExp(n))return n.test(t)}}function oo(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function ao(e,t){const r=c.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,o,a){return this[n].call(this,t,s,o,a)},configurable:!0})})}class je{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function o(i,m,d){const u=me(m);if(!u)throw new Error("header name must be a non-empty string");const f=c.findKey(s,u);(!f||s[f]===void 0||d===!0||d===void 0&&s[f]!==!1)&&(s[f||m]=Ce(i))}const a=(i,m)=>c.forEach(i,(d,u)=>o(d,u,m));if(c.isPlainObject(t)||t instanceof this.constructor)a(t,r);else if(c.isString(t)&&(t=t.trim())&&!so(t))a(ro(t),r);else if(c.isObject(t)&&c.isIterable(t)){let i={},m,d;for(const u of t){if(!c.isArray(u))throw TypeError("Object iterator must return a key-value pair");i[d=u[0]]=(m=i[d])?c.isArray(m)?[...m,u[1]]:[m,u[1]]:u[1]}a(i,r)}else t!=null&&o(r,t,n);return this}get(t,r){if(t=me(t),t){const n=c.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return no(s);if(c.isFunction(r))return r.call(this,s,n);if(c.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=me(t),t){const n=c.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||Je(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function o(a){if(a=me(a),a){const i=c.findKey(n,a);i&&(!r||Je(n,n[i],i,r))&&(delete n[i],s=!0)}}return c.isArray(t)?t.forEach(o):o(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const o=r[n];(!t||Je(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const r=this,n={};return c.forEach(this,(s,o)=>{const a=c.findKey(n,o);if(a){r[a]=Ce(s),delete r[o];return}const i=t?oo(o):String(o).trim();i!==o&&delete r[o],r[i]=Ce(s),n[i]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return c.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&c.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[Bt]=this[Bt]={accessors:{}}).accessors,s=this.prototype;function o(a){const i=me(a);n[i]||(ao(s,a),n[i]=!0)}return c.isArray(t)?t.forEach(o):o(t),this}}je.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);c.reduceDescriptors(je.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});c.freezeMethods(je);const H=je;function We(e,t){const r=this||gt,n=t||r,s=H.from(n.headers);let o=n.data;return c.forEach(e,function(i){o=i.call(r,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Er(e){return!!(e&&e.__CANCEL__)}function ce(e,t,r){S.call(this,e??"canceled",S.ERR_CANCELED,t,r),this.name="CanceledError"}c.inherits(ce,S,{__CANCEL__:!0});function Ar(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new S("Request failed with status code "+r.status,[S.ERR_BAD_REQUEST,S.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function io(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function lo(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,o=0,a;return t=t!==void 0?t:1e3,function(m){const d=Date.now(),u=n[o];a||(a=d),r[s]=m,n[s]=d;let f=o,v=0;for(;f!==s;)v+=r[f++],f=f%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),d-a<t)return;const R=u&&d-u;return R?Math.round(v*1e3/R):void 0}}function co(e,t){let r=0,n=1e3/t,s,o;const a=(d,u=Date.now())=>{r=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,d)};return[(...d)=>{const u=Date.now(),f=u-r;f>=n?a(d,u):(s=d,o||(o=setTimeout(()=>{o=null,a(s)},n-f)))},()=>s&&a(s)]}const Ae=(e,t,r=3)=>{let n=0;const s=lo(50,250);return co(o=>{const a=o.loaded,i=o.lengthComputable?o.total:void 0,m=a-n,d=s(m),u=a<=i;n=a;const f={loaded:a,total:i,progress:i?a/i:void 0,bytes:m,rate:d||void 0,estimated:d&&i&&u?(i-a)/d:void 0,event:o,lengthComputable:i!=null,[t?"download":"upload"]:!0};e(f)},r)},Ut=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},qt=e=>(...t)=>c.asap(()=>e(...t)),uo=j.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,j.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(j.origin),j.navigator&&/(msie|trident)/i.test(j.navigator.userAgent)):()=>!0,mo=j.hasStandardBrowserEnv?{write(e,t,r,n,s,o){const a=[e+"="+encodeURIComponent(t)];c.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),c.isString(n)&&a.push("path="+n),c.isString(s)&&a.push("domain="+s),o===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function fo(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function po(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Pr(e,t,r){let n=!fo(t);return e&&(n||r==!1)?po(e,t):t}const Dt=e=>e instanceof H?{...e}:e;function te(e,t){t=t||{};const r={};function n(d,u,f,v){return c.isPlainObject(d)&&c.isPlainObject(u)?c.merge.call({caseless:v},d,u):c.isPlainObject(u)?c.merge({},u):c.isArray(u)?u.slice():u}function s(d,u,f,v){if(c.isUndefined(u)){if(!c.isUndefined(d))return n(void 0,d,f,v)}else return n(d,u,f,v)}function o(d,u){if(!c.isUndefined(u))return n(void 0,u)}function a(d,u){if(c.isUndefined(u)){if(!c.isUndefined(d))return n(void 0,d)}else return n(void 0,u)}function i(d,u,f){if(f in t)return n(d,u);if(f in e)return n(void 0,d)}const m={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:i,headers:(d,u,f)=>s(Dt(d),Dt(u),f,!0)};return c.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=m[u]||s,v=f(e[u],t[u],u);c.isUndefined(v)&&f!==i||(r[u]=v)}),r}const Tr=e=>{const t=te({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:i}=t;t.headers=a=H.from(a),t.url=Sr(Pr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),i&&a.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):"")));let m;if(c.isFormData(r)){if(j.hasStandardBrowserEnv||j.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((m=a.getContentType())!==!1){const[d,...u]=m?m.split(";").map(f=>f.trim()).filter(Boolean):[];a.setContentType([d||"multipart/form-data",...u].join("; "))}}if(j.hasStandardBrowserEnv&&(n&&c.isFunction(n)&&(n=n(t)),n||n!==!1&&uo(t.url))){const d=s&&o&&mo.read(o);d&&a.set(s,d)}return t},ho=typeof XMLHttpRequest<"u",go=ho&&function(e){return new Promise(function(r,n){const s=Tr(e);let o=s.data;const a=H.from(s.headers).normalize();let{responseType:i,onUploadProgress:m,onDownloadProgress:d}=s,u,f,v,R,g;function p(){R&&R(),g&&g(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let b=new XMLHttpRequest;b.open(s.method.toUpperCase(),s.url,!0),b.timeout=s.timeout;function E(){if(!b)return;const A=H.from("getAllResponseHeaders"in b&&b.getAllResponseHeaders()),T={data:!i||i==="text"||i==="json"?b.responseText:b.response,status:b.status,statusText:b.statusText,headers:A,config:e,request:b};Ar(function(O){r(O),p()},function(O){n(O),p()},T),b=null}"onloadend"in b?b.onloadend=E:b.onreadystatechange=function(){!b||b.readyState!==4||b.status===0&&!(b.responseURL&&b.responseURL.indexOf("file:")===0)||setTimeout(E)},b.onabort=function(){b&&(n(new S("Request aborted",S.ECONNABORTED,e,b)),b=null)},b.onerror=function(){n(new S("Network Error",S.ERR_NETWORK,e,b)),b=null},b.ontimeout=function(){let I=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const T=s.transitional||Cr;s.timeoutErrorMessage&&(I=s.timeoutErrorMessage),n(new S(I,T.clarifyTimeoutError?S.ETIMEDOUT:S.ECONNABORTED,e,b)),b=null},o===void 0&&a.setContentType(null),"setRequestHeader"in b&&c.forEach(a.toJSON(),function(I,T){b.setRequestHeader(T,I)}),c.isUndefined(s.withCredentials)||(b.withCredentials=!!s.withCredentials),i&&i!=="json"&&(b.responseType=s.responseType),d&&([v,g]=Ae(d,!0),b.addEventListener("progress",v)),m&&b.upload&&([f,R]=Ae(m),b.upload.addEventListener("progress",f),b.upload.addEventListener("loadend",R)),(s.cancelToken||s.signal)&&(u=A=>{b&&(n(!A||A.type?new ce(null,e,b):A),b.abort(),b=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const k=io(s.url);if(k&&j.protocols.indexOf(k)===-1){n(new S("Unsupported protocol "+k+":",S.ERR_BAD_REQUEST,e));return}b.send(o||null)})},bo=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const o=function(d){if(!s){s=!0,i();const u=d instanceof Error?d:this.reason;n.abort(u instanceof S?u:new ce(u instanceof Error?u.message:u))}};let a=t&&setTimeout(()=>{a=null,o(new S(`timeout ${t} of ms exceeded`,S.ETIMEDOUT))},t);const i=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(o):d.removeEventListener("abort",o)}),e=null)};e.forEach(d=>d.addEventListener("abort",o));const{signal:m}=n;return m.unsubscribe=()=>c.asap(i),m}},yo=bo,xo=function*(e,t){let r=e.byteLength;if(!t||r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},wo=async function*(e,t){for await(const r of No(e))yield*xo(r,t)},No=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},$t=(e,t,r,n)=>{const s=wo(e,t);let o=0,a,i=m=>{a||(a=!0,n&&n(m))};return new ReadableStream({async pull(m){try{const{done:d,value:u}=await s.next();if(d){i(),m.close();return}let f=u.byteLength;if(r){let v=o+=f;r(v)}m.enqueue(new Uint8Array(u))}catch(d){throw i(d),d}},cancel(m){return i(m),s.return()}},{highWaterMark:2})},Me=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Or=Me&&typeof ReadableStream=="function",vo=Me&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ir=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ko=Or&&Ir(()=>{let e=!1;const t=new Request(j.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ht=64*1024,st=Or&&Ir(()=>c.isReadableStream(new Response("").body)),Pe={stream:st&&(e=>e.body)};Me&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Pe[t]&&(Pe[t]=c.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new S(`Response type '${t}' is not supported`,S.ERR_NOT_SUPPORT,n)})})})(new Response);const So=async e=>{if(e==null)return 0;if(c.isBlob(e))return e.size;if(c.isSpecCompliantForm(e))return(await new Request(j.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(c.isArrayBufferView(e)||c.isArrayBuffer(e))return e.byteLength;if(c.isURLSearchParams(e)&&(e=e+""),c.isString(e))return(await vo(e)).byteLength},Co=async(e,t)=>{const r=c.toFiniteNumber(e.getContentLength());return r??So(t)},Ro=Me&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:o,timeout:a,onDownloadProgress:i,onUploadProgress:m,responseType:d,headers:u,withCredentials:f="same-origin",fetchOptions:v}=Tr(e);d=d?(d+"").toLowerCase():"text";let R=yo([s,o&&o.toAbortSignal()],a),g;const p=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let b;try{if(m&&ko&&r!=="get"&&r!=="head"&&(b=await Co(u,n))!==0){let T=new Request(t,{method:"POST",body:n,duplex:"half"}),P;if(c.isFormData(n)&&(P=T.headers.get("content-type"))&&u.setContentType(P),T.body){const[O,w]=Ut(b,Ae(qt(m)));n=$t(T.body,Ht,O,w)}}c.isString(f)||(f=f?"include":"omit");const E="credentials"in Request.prototype;g=new Request(t,{...v,signal:R,method:r.toUpperCase(),headers:u.normalize().toJSON(),body:n,duplex:"half",credentials:E?f:void 0});let k=await fetch(g,v);const A=st&&(d==="stream"||d==="response");if(st&&(i||A&&p)){const T={};["status","statusText","headers"].forEach(q=>{T[q]=k[q]});const P=c.toFiniteNumber(k.headers.get("content-length")),[O,w]=i&&Ut(P,Ae(qt(i),!0))||[];k=new Response($t(k.body,Ht,O,()=>{w&&w(),p&&p()}),T)}d=d||"text";let I=await Pe[c.findKey(Pe,d)||"text"](k,e);return!A&&p&&p(),await new Promise((T,P)=>{Ar(T,P,{data:I,headers:H.from(k.headers),status:k.status,statusText:k.statusText,config:e,request:g})})}catch(E){throw p&&p(),E&&E.name==="TypeError"&&/Load failed|fetch/i.test(E.message)?Object.assign(new S("Network Error",S.ERR_NETWORK,e,g),{cause:E.cause||E}):S.from(E,E&&E.code,e,g)}}),ot={http:zs,xhr:go,fetch:Ro};c.forEach(ot,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Gt=e=>`- ${e}`,Eo=e=>c.isFunction(e)||e===null||e===!1,Lr={getAdapter:e=>{e=c.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let o=0;o<t;o++){r=e[o];let a;if(n=r,!Eo(r)&&(n=ot[(a=String(r)).toLowerCase()],n===void 0))throw new S(`Unknown adapter '${a}'`);if(n)break;s[a||"#"+o]=n}if(!n){const o=Object.entries(s).map(([i,m])=>`adapter ${i} `+(m===!1?"is not supported by the environment":"is not available in the build"));let a=t?o.length>1?`since :
`+o.map(Gt).join(`
`):" "+Gt(o[0]):"as no adapter specified";throw new S("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return n},adapters:ot};function Ke(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ce(null,e)}function Vt(e){return Ke(e),e.headers=H.from(e.headers),e.data=We.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Lr.getAdapter(e.adapter||gt.adapter)(e).then(function(n){return Ke(e),n.data=We.call(e,e.transformResponse,n),n.headers=H.from(n.headers),n},function(n){return Er(n)||(Ke(e),n&&n.response&&(n.response.data=We.call(e,e.transformResponse,n.response),n.response.headers=H.from(n.response.headers))),Promise.reject(n)})}const _r="1.10.0",ze={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ze[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Jt={};ze.transitional=function(t,r,n){function s(o,a){return"[Axios v"+_r+"] Transitional option '"+o+"'"+a+(n?". "+n:"")}return(o,a,i)=>{if(t===!1)throw new S(s(a," has been removed"+(r?" in "+r:"")),S.ERR_DEPRECATED);return r&&!Jt[a]&&(Jt[a]=!0,console.warn(s(a," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,a,i):!0}};ze.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function Ao(e,t,r){if(typeof e!="object")throw new S("options must be an object",S.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const o=n[s],a=t[o];if(a){const i=e[o],m=i===void 0||a(i,o,e);if(m!==!0)throw new S("option "+o+" must be "+m,S.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new S("Unknown option "+o,S.ERR_BAD_OPTION)}}const Re={assertOptions:Ao,validators:ze},V=Re.validators;class Te{constructor(t){this.defaults=t||{},this.interceptors={request:new zt,response:new zt}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=te(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:o}=r;n!==void 0&&Re.assertOptions(n,{silentJSONParsing:V.transitional(V.boolean),forcedJSONParsing:V.transitional(V.boolean),clarifyTimeoutError:V.transitional(V.boolean)},!1),s!=null&&(c.isFunction(s)?r.paramsSerializer={serialize:s}:Re.assertOptions(s,{encode:V.function,serialize:V.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Re.assertOptions(r,{baseUrl:V.spelling("baseURL"),withXsrfToken:V.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let a=o&&c.merge(o.common,o[r.method]);o&&c.forEach(["delete","get","head","post","put","patch","common"],g=>{delete o[g]}),r.headers=H.concat(a,o);const i=[];let m=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(r)===!1||(m=m&&p.synchronous,i.unshift(p.fulfilled,p.rejected))});const d=[];this.interceptors.response.forEach(function(p){d.push(p.fulfilled,p.rejected)});let u,f=0,v;if(!m){const g=[Vt.bind(this),void 0];for(g.unshift.apply(g,i),g.push.apply(g,d),v=g.length,u=Promise.resolve(r);f<v;)u=u.then(g[f++],g[f++]);return u}v=i.length;let R=r;for(f=0;f<v;){const g=i[f++],p=i[f++];try{R=g(R)}catch(b){p.call(this,b);break}}try{u=Vt.call(this,R)}catch(g){return Promise.reject(g)}for(f=0,v=d.length;f<v;)u=u.then(d[f++],d[f++]);return u}getUri(t){t=te(this.defaults,t);const r=Pr(t.baseURL,t.url,t.allowAbsoluteUrls);return Sr(r,t.params,t.paramsSerializer)}}c.forEach(["delete","get","head","options"],function(t){Te.prototype[t]=function(r,n){return this.request(te(n||{},{method:t,url:r,data:(n||{}).data}))}});c.forEach(["post","put","patch"],function(t){function r(n){return function(o,a,i){return this.request(te(i||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:a}))}}Te.prototype[t]=r(),Te.prototype[t+"Form"]=r(!0)});const Ee=Te;class bt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o;const a=new Promise(i=>{n.subscribe(i),o=i}).then(s);return a.cancel=function(){n.unsubscribe(o)},a},t(function(o,a,i){n.reason||(n.reason=new ce(o,a,i),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new bt(function(s){t=s}),cancel:t}}}const Po=bt;function To(e){return function(r){return e.apply(null,r)}}function Oo(e){return c.isObject(e)&&e.isAxiosError===!0}const at={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(at).forEach(([e,t])=>{at[t]=e});const Io=at;function Fr(e){const t=new Ee(e),r=mr(Ee.prototype.request,t);return c.extend(r,Ee.prototype,t,{allOwnKeys:!0}),c.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return Fr(te(e,s))},r}const L=Fr(gt);L.Axios=Ee;L.CanceledError=ce;L.CancelToken=Po;L.isCancel=Er;L.VERSION=_r;L.toFormData=Fe;L.AxiosError=S;L.Cancel=L.CanceledError;L.all=function(t){return Promise.all(t)};L.spread=To;L.isAxiosError=Oo;L.mergeConfig=te;L.AxiosHeaders=H;L.formToJSON=e=>Rr(c.isHTMLForm(e)?new FormData(e):e);L.getAdapter=Lr.getAdapter;L.HttpStatusCode=Io;L.default=L;const Lo=L,K=Lo.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}});K.interceptors.request.use(e=>{var t;return console.log("API Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url),e},e=>Promise.reject(e));K.interceptors.response.use(e=>(console.log("API Response:",e.status,e.config.url),e),e=>{var t,r;return console.error("API Error:",(t=e.response)==null?void 0:t.status,(r=e.response)==null?void 0:r.data),Promise.reject(e)});const Be={getProducts:async e=>(await K.get("/products",{params:e})).data,getProduct:async e=>(await K.get(`/products/${e}`)).data,searchProducts:async(e,t)=>(await K.get("/products/search",{params:{keyword:e,...t}})).data,getPopularProducts:async(e=10)=>(await K.get("/products/popular",{params:{limit:e}})).data,getRecommendedProducts:async(e,t=10)=>(await K.get("/products/recommended",{params:{productId:e,limit:t}})).data},it={getCategories:async()=>(await K.get("/products/categories")).data.map((r,n)=>({id:n+1,name:r,slug:r.toLowerCase().replace(/\s+/g,"-"),description:`${r}相关商品`})),getCategory:async e=>{const r=(await it.getCategories()).find(n=>n.id===e);if(!r)throw new Error(`Category with id ${e} not found`);return r},getCategoryProducts:async(e,t)=>{const n=(await it.getCategories()).find(o=>o.id===e);if(!n)throw new Error(`Category with id ${e} not found`);return(await K.get(`/products/category/${encodeURIComponent(n.name)}`,{params:t})).data}},_o=e=>he({queryKey:["products",e],queryFn:()=>Be.getProducts(e),staleTime:5*60*1e3,gcTime:10*60*1e3}),Fo=e=>he({queryKey:["product",e],queryFn:()=>Be.getProduct(e),enabled:!!e,staleTime:5*60*1e3}),jo=(e=10)=>he({queryKey:["popularProducts",e],queryFn:()=>Be.getPopularProducts(e),staleTime:10*60*1e3}),Mo=(e,t=10)=>he({queryKey:["recommendedProducts",e,t],queryFn:()=>Be.getRecommendedProducts(e,t),enabled:!!e,staleTime:10*60*1e3}),zo=()=>he({queryKey:["categories"],queryFn:it.getCategories,staleTime:30*60*1e3,gcTime:60*60*1e3}),Bo=({children:e,className:t,hover:r=!1,onClick:n})=>l("div",{className:re("card",r&&"hover:shadow-md transition-shadow duration-200",n&&"cursor-pointer",t),onClick:n,children:e}),Uo=({children:e,className:t})=>l("div",{className:re("card-header",t),children:e}),qo=({children:e,className:t})=>l("div",{className:re("card-content",t),children:e}),Do=({children:e,className:t})=>l("div",{className:re("card-footer",t),children:e}),U=Bo;U.Header=Uo;U.Content=qo;U.Footer=Do;const $o=({product:e,onClick:t})=>{const{addItem:r,isInCart:n,getItemQuantity:s}=dt(),o=e.originalPrice?dr(e.originalPrice,e.price):0,a=ur(e.rating),i=n(e.id),m=s(e.id),d=f=>{f.stopPropagation(),r(e)};return h(U,{className:"product-card",hover:!0,children:[h("div",{onClick:()=>{t==null||t(e)},children:[h("div",{className:"relative overflow-hidden rounded-t-lg",children:[l("img",{src:e.imageUrl,alt:e.name,className:"w-full h-48 object-cover transition-transform duration-300 hover:scale-105",loading:"lazy"}),o>0&&h("div",{className:"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium",children:["-",o,"%"]}),e.stock===0&&l("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:l("span",{className:"text-white font-medium",children:"缺货"})})]}),h(U.Content,{className:"p-4",children:[l("h3",{className:"font-medium text-gray-900 mb-2 line-clamp-2 hover:text-primary-600 transition-colors",children:e.name}),l("p",{className:"text-sm text-gray-500 mb-2",children:e.brand}),h("div",{className:"flex items-center mb-3",children:[l("div",{className:"rating-stars",children:a.map((f,v)=>l(Xt,{className:`w-4 h-4 ${f?"star-filled":"star-empty"}`,fill:"currentColor"},v))}),h("span",{className:"text-sm text-gray-500 ml-2",children:["(",e.reviewCount,")"]})]}),l("div",{className:"flex items-center justify-between mb-3",children:h("div",{className:"flex items-center space-x-2",children:[l("span",{className:"price-current",children:oe(e.price)}),e.originalPrice&&e.originalPrice>e.price&&l("span",{className:"price-original",children:oe(e.originalPrice)})]})})]})]}),l(U.Footer,{className:"p-4 pt-0",children:h(D,{onClick:d,disabled:e.stock===0,className:"w-full",variant:i?"secondary":"primary",children:[l(ct,{className:"w-4 h-4 mr-2"}),e.stock===0?"缺货":i?`已添加 (${m})`:"加入购物车"]})})]})},Ho=({size:e="md",className:t})=>l("div",{className:re("loading-spinner",{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e],t)}),jr=()=>l("div",{className:"flex items-center justify-center min-h-[400px]",children:h("div",{className:"text-center",children:[l(Ho,{size:"lg"}),l("p",{className:"mt-4 text-gray-600",children:"加载中..."})]})}),fe=({className:e})=>l("div",{className:re("skeleton",e)}),Go=()=>h("div",{className:"card",children:[l(fe,{className:"h-48 w-full rounded-t-lg"}),h("div",{className:"p-4 space-y-3",children:[l(fe,{className:"h-4 w-3/4"}),l(fe,{className:"h-4 w-1/2"}),h("div",{className:"flex justify-between items-center",children:[l(fe,{className:"h-6 w-20"}),l(fe,{className:"h-8 w-16"})]})]})]}),lt=({products:e,loading:t=!1,onProductClick:r})=>t?l("div",{className:"product-grid",children:Array.from({length:12}).map((n,s)=>l(Go,{},s))}):e.length===0?h("div",{className:"text-center py-12",children:[l("div",{className:"text-gray-400 text-6xl mb-4",children:"📦"}),l("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无商品"}),l("p",{className:"text-gray-500",children:"请尝试调整筛选条件或搜索关键词"})]}):l("div",{className:"product-grid",children:e.map(n=>l($o,{product:n,onClick:r},n.id))}),Vo=()=>{const e=Wt(),[t]=$.useState({page:0,size:20,sortBy:"createdAt",sortOrder:"desc"}),{data:r,isLoading:n}=_o(t),{data:s,isLoading:o}=jo(8),{data:a,isLoading:i}=zo(),m=u=>{e(`/products/${u.id}`)},d=u=>{e(`/categories/${u}`)};return n&&o&&i?l(jr,{}):h("div",{className:"space-y-8",children:[l("section",{className:"gradient-bg text-white py-16 rounded-lg",children:h("div",{className:"text-center",children:[l("h1",{className:"text-4xl md:text-6xl font-bold mb-4",children:"现代化商品展示"}),l("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"发现优质商品，享受购物乐趣"}),l(D,{size:"lg",variant:"secondary",onClick:()=>e("/products"),className:"text-primary-600",children:"开始购物"})]})}),a&&a.length>0&&h("section",{children:[l("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"热门分类"}),l("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:a.slice(0,6).map(u=>h(U,{hover:!0,className:"cursor-pointer text-center p-4",onClick:()=>d(u.id),children:[l("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l("span",{className:"text-2xl",children:"📦"})}),l("h3",{className:"font-medium text-gray-900",children:u.name})]},u.id))})]}),s&&s.length>0&&h("section",{children:[h("div",{className:"flex items-center justify-between mb-6",children:[l("h2",{className:"text-2xl font-bold text-gray-900",children:"热门商品"}),l(D,{variant:"outline",onClick:()=>e("/products?sort=popular"),children:"查看更多"})]}),l(lt,{products:s,loading:o,onProductClick:m})]}),h("section",{children:[h("div",{className:"flex items-center justify-between mb-6",children:[l("h2",{className:"text-2xl font-bold text-gray-900",children:"最新商品"}),l(D,{variant:"outline",onClick:()=>e("/products"),children:"查看全部"})]}),l(lt,{products:(r==null?void 0:r.content)||[],loading:n,onProductClick:m})]}),h("section",{className:"bg-gray-50 rounded-lg p-8",children:[l("h2",{className:"text-2xl font-bold text-gray-900 text-center mb-8",children:"为什么选择我们"}),h("div",{className:"grid md:grid-cols-3 gap-8",children:[h("div",{className:"text-center",children:[l("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l("span",{className:"text-3xl",children:"🚀"})}),l("h3",{className:"text-lg font-semibold mb-2",children:"极速加载"}),l("p",{className:"text-gray-600",children:"基于现代化技术栈，提供极速的页面加载体验"})]}),h("div",{className:"text-center",children:[l("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l("span",{className:"text-3xl",children:"📱"})}),l("h3",{className:"text-lg font-semibold mb-2",children:"响应式设计"}),l("p",{className:"text-gray-600",children:"完美适配各种设备，随时随地享受购物乐趣"})]}),h("div",{className:"text-center",children:[l("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l("span",{className:"text-3xl",children:"🔒"})}),l("h3",{className:"text-lg font-semibold mb-2",children:"安全可靠"}),l("p",{className:"text-gray-600",children:"企业级安全保障，让您的购物更加安心"})]})]})]})]})},Jo=()=>{const{id:e}=qr(),t=Wt(),r=parseInt(e||"0"),[n,s]=$.useState(1),[o,a]=$.useState(0),{data:i,isLoading:m,error:d}=Fo(r),{data:u}=Mo(r,8),{addItem:f,isInCart:v,getItemQuantity:R}=dt();if(m)return l(jr,{});if(d||!i)return h("div",{className:"text-center py-12",children:[l("div",{className:"text-gray-400 text-6xl mb-4",children:"😕"}),l("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"商品未找到"}),l("p",{className:"text-gray-600 mb-6",children:"抱歉，您查找的商品不存在或已下架"}),l(D,{onClick:()=>t("/"),children:"返回首页"})]});const g=i.originalPrice?dr(i.originalPrice,i.price):0,p=ur(i.rating),b=v(i.id),E=R(i.id),k=i.images||[i.imageUrl],A=()=>{f(i,n),s(1)},I=P=>{const O=Math.max(1,Math.min(i.stock,n+P));s(O)},T=P=>{t(`/products/${P.id}`)};return h("div",{className:"space-y-8",children:[h(D,{variant:"ghost",onClick:()=>t(-1),className:"flex items-center",children:[l(Qr,{className:"w-4 h-4 mr-2"}),"返回"]}),h("div",{className:"grid lg:grid-cols-2 gap-8",children:[h("div",{className:"space-y-4",children:[l("div",{className:"aspect-square overflow-hidden rounded-lg bg-gray-100",children:l("img",{src:k[o],alt:i.name,className:"w-full h-full object-cover"})}),k.length>1&&l("div",{className:"flex space-x-2 overflow-x-auto",children:k.map((P,O)=>l("button",{onClick:()=>a(O),className:`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${o===O?"border-primary-500":"border-gray-200"}`,children:l("img",{src:P,alt:`${i.name} ${O+1}`,className:"w-full h-full object-cover"})},O))})]}),h("div",{className:"space-y-6",children:[h("div",{children:[l("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:i.name}),l("p",{className:"text-lg text-gray-600",children:i.brand})]}),h("div",{className:"flex items-center space-x-4",children:[l("div",{className:"rating-stars",children:p.map((P,O)=>l(Xt,{className:`w-5 h-5 ${P?"star-filled":"star-empty"}`,fill:"currentColor"},O))}),h("span",{className:"text-gray-600",children:[i.rating," (",i.reviewCount," 评价)"]})]}),h("div",{className:"space-y-2",children:[h("div",{className:"flex items-center space-x-4",children:[l("span",{className:"text-3xl font-bold text-red-600",children:oe(i.price)}),i.originalPrice&&i.originalPrice>i.price&&h(Mr,{children:[l("span",{className:"text-xl text-gray-500 line-through",children:oe(i.originalPrice)}),h("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium",children:["省 ",oe(i.originalPrice-i.price)]})]})]}),g>0&&h("p",{className:"text-green-600 font-medium",children:["限时优惠 ",g,"% OFF"]})]}),h("div",{className:"flex items-center space-x-4",children:[l("span",{className:"text-gray-700",children:"库存:"}),l("span",{className:`font-medium ${i.stock>10?"text-green-600":i.stock>0?"text-yellow-600":"text-red-600"}`,children:i.stock>0?`${i.stock} 件`:"缺货"})]}),i.stock>0&&h("div",{className:"flex items-center space-x-4",children:[l("span",{className:"text-gray-700",children:"数量:"}),h("div",{className:"flex items-center border border-gray-300 rounded-lg",children:[l(D,{variant:"ghost",size:"sm",onClick:()=>I(-1),disabled:n<=1,className:"px-3 py-2",children:l(en,{className:"w-4 h-4"})}),l("span",{className:"px-4 py-2 min-w-[3rem] text-center",children:n}),l(D,{variant:"ghost",size:"sm",onClick:()=>I(1),disabled:n>=i.stock,className:"px-3 py-2",children:l(rn,{className:"w-4 h-4"})})]})]}),h("div",{className:"flex space-x-4",children:[h(D,{onClick:A,disabled:i.stock===0,className:"flex-1",size:"lg",children:[l(ct,{className:"w-5 h-5 mr-2"}),i.stock===0?"缺货":"加入购物车"]}),b&&h("div",{className:"flex items-center text-sm text-gray-600",children:["购物车中已有 ",E," 件"]})]}),h(U,{children:[l(U.Header,{children:l("h3",{className:"text-lg font-semibold",children:"商品描述"})}),l(U.Content,{children:l("p",{className:"text-gray-700 leading-relaxed",children:i.description})})]}),i.specifications&&h(U,{children:[l(U.Header,{children:l("h3",{className:"text-lg font-semibold",children:"规格参数"})}),l(U.Content,{children:l("div",{className:"space-y-2",children:Object.entries(i.specifications).map(([P,O])=>h("div",{className:"flex justify-between py-2 border-b border-gray-100 last:border-b-0",children:[l("span",{className:"text-gray-600",children:P}),l("span",{className:"font-medium",children:O})]},P))})})]})]})]}),u&&u.length>0&&h("section",{children:[l("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"相关推荐"}),l(lt,{products:u,onProductClick:T})]})]})},Wo=new zr({defaultOptions:{queries:{retry:1,refetchOnWindowFocus:!1,staleTime:5*60*1e3}}}),Ko=()=>l(Br,{client:Wo,children:l(Dr,{children:h("div",{className:"min-h-screen bg-gray-50",children:[l(Zn,{onSearch:t=>{console.log("搜索关键词:",t)}}),l("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:h($r,{children:[l($e,{path:"/",element:l(Vo,{})}),l($e,{path:"/products/:id",element:l(Jo,{})}),l($e,{path:"*",element:l(Qo,{})})]})}),l(Xo,{})]})})}),Qo=()=>h("div",{className:"text-center py-12",children:[l("div",{className:"text-gray-400 text-6xl mb-4",children:"🔍"}),l("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"页面未找到"}),l("p",{className:"text-gray-600 mb-6",children:"抱歉，您访问的页面不存在"}),l("a",{href:"/",className:"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors",children:"返回首页"})]}),Xo=()=>l("footer",{className:"bg-white border-t mt-16",children:h("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[h("div",{className:"grid md:grid-cols-4 gap-8",children:[h("div",{children:[l("h3",{className:"font-semibold text-gray-900 mb-4",children:"关于我们"}),l("p",{className:"text-gray-600 text-sm",children:"现代化商品展示系统，基于React和SpringBoot构建的高性能电商展示应用。"})]}),h("div",{children:[l("h3",{className:"font-semibold text-gray-900 mb-4",children:"快速链接"}),h("ul",{className:"space-y-2 text-sm",children:[l("li",{children:l("a",{href:"/",className:"text-gray-600 hover:text-primary-600",children:"首页"})}),l("li",{children:l("a",{href:"/products",className:"text-gray-600 hover:text-primary-600",children:"商品"})}),l("li",{children:l("a",{href:"/categories",className:"text-gray-600 hover:text-primary-600",children:"分类"})}),l("li",{children:l("a",{href:"/cart",className:"text-gray-600 hover:text-primary-600",children:"购物车"})})]})]}),h("div",{children:[l("h3",{className:"font-semibold text-gray-900 mb-4",children:"技术栈"}),h("ul",{className:"space-y-2 text-sm text-gray-600",children:[l("li",{children:"React 18 + TypeScript"}),l("li",{children:"Vite + Tailwind CSS"}),l("li",{children:"React Query + Zustand"}),l("li",{children:"SpringBoot + MySQL"})]})]}),h("div",{children:[l("h3",{className:"font-semibold text-gray-900 mb-4",children:"联系我们"}),h("ul",{className:"space-y-2 text-sm text-gray-600",children:[l("li",{children:"邮箱: <EMAIL>"}),l("li",{children:"电话: +86 123-4567-8900"}),l("li",{children:"地址: 北京市朝阳区"})]})]})]}),l("div",{className:"border-t mt-8 pt-8 text-center text-sm text-gray-600",children:l("p",{children:"© 2024 现代化商品展示系统. All rights reserved."})})]})});Kt(document.getElementById("root")).render(l(Qe.StrictMode,{children:l(Ko,{})}));
//# sourceMappingURL=index-f7870cb4.js.map

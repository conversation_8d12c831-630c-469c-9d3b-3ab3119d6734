import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { PaginatedResponse, Product, Category, Review, ProductSearchParams } from '@/types';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

// 商品API
export const productApi = {
  // 获取商品列表
  getProducts: async (params?: ProductSearchParams): Promise<PaginatedResponse<Product>> => {
    const response = await api.get('/products', { params });
    return response.data;
  },

  // 获取商品详情
  getProduct: async (id: number): Promise<Product> => {
    const response = await api.get(`/products/${id}`);
    return response.data;
  },

  // 搜索商品
  searchProducts: async (keyword: string, params?: ProductSearchParams): Promise<PaginatedResponse<Product>> => {
    const response = await api.get('/products/search', { 
      params: { keyword, ...params } 
    });
    return response.data;
  },

  // 获取热门商品
  getPopularProducts: async (limit: number = 10): Promise<Product[]> => {
    const response = await api.get('/products/popular', { params: { limit } });
    return response.data;
  },

  // 获取推荐商品
  getRecommendedProducts: async (productId?: number, limit: number = 10): Promise<Product[]> => {
    const response = await api.get('/products/recommended', { 
      params: { productId, limit } 
    });
    return response.data;
  },
};

// 分类API
export const categoryApi = {
  // 获取所有分类
  getCategories: async (): Promise<Category[]> => {
    const response = await api.get('/products/categories');
    const categoryNames: string[] = response.data;

    // 将字符串数组转换为Category对象数组
    return categoryNames.map((name, index) => ({
      id: index + 1,
      name,
      slug: name.toLowerCase().replace(/\s+/g, '-'),
      description: `${name}相关商品`,
    }));
  },

  // 获取分类详情
  getCategory: async (id: number): Promise<Category> => {
    const categories = await categoryApi.getCategories();
    const category = categories.find(cat => cat.id === id);
    if (!category) {
      throw new Error(`Category with id ${id} not found`);
    }
    return category;
  },

  // 获取分类下的商品
  getCategoryProducts: async (categoryId: number, params?: ProductSearchParams): Promise<PaginatedResponse<Product>> => {
    const categories = await categoryApi.getCategories();
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) {
      throw new Error(`Category with id ${categoryId} not found`);
    }

    const response = await api.get(`/products/category/${encodeURIComponent(category.name)}`, { params });
    return response.data;
  },
};

// 评价API
export const reviewApi = {
  // 获取商品评价
  getProductReviews: async (productId: number, page: number = 0, size: number = 10): Promise<PaginatedResponse<Review>> => {
    const response = await api.get(`/products/${productId}/reviews`, { 
      params: { page, size } 
    });
    return response.data;
  },

  // 添加评价
  addReview: async (productId: number, review: Partial<Review>): Promise<Review> => {
    const response = await api.post(`/products/${productId}/reviews`, review);
    return response.data;
  },
};

// 统计API
export const statsApi = {
  // 获取商品统计信息
  getProductStats: async (productId: number) => {
    const response = await api.get(`/products/${productId}/stats`);
    return response.data;
  },
};

export default api;

import React from 'react';
import { cn } from '@/utils';
import { BaseComponentProps } from '@/types';

interface CardProps extends BaseComponentProps {
  hover?: boolean;
  onClick?: () => void;
}

interface CardComponent extends React.FC<CardProps> {
  Header: React.FC<BaseComponentProps>;
  Content: React.FC<BaseComponentProps>;
  Footer: React.FC<BaseComponentProps>;
}

const Card = ({
  children,
  className,
  hover = false,
  onClick
}: CardProps) => {
  return (
    <div
      className={cn(
        'card',
        hover && 'hover:shadow-md transition-shadow duration-200',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

const CardHeader: React.FC<BaseComponentProps> = ({ children, className }) => {
  return (
    <div className={cn('card-header', className)}>
      {children}
    </div>
  );
};

const CardContent: React.FC<BaseComponentProps> = ({ children, className }) => {
  return (
    <div className={cn('card-content', className)}>
      {children}
    </div>
  );
};

const CardFooter: React.FC<BaseComponentProps> = ({ children, className }) => {
  return (
    <div className={cn('card-footer', className)}>
      {children}
    </div>
  );
};

(Card as CardComponent).Header = CardHeader;
(Card as CardComponent).Content = CardContent;
(Card as CardComponent).Footer = CardFooter;

export default Card;

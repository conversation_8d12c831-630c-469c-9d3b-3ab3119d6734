import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ShoppingCart, Menu, X } from 'lucide-react';
import { useCartStore } from '@/store/cartStore';
import { formatPrice } from '@/utils';
import { HeaderProps } from '@/types';
import SearchBar from './SearchBar';
import Button from './ui/Button';

const Header: React.FC<HeaderProps> = ({ onSearch }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { totalItems, totalPrice } = useCartStore();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">🛍️</span>
              </div>
              <span className="font-bold text-xl text-gray-900 hidden sm:block">
                商品展示
              </span>
            </Link>
          </div>

          {/* 搜索框 - 桌面版 */}
          <div className="hidden md:block flex-1 max-w-lg mx-8">
            <SearchBar onSearch={onSearch} />
          </div>

          {/* 右侧操作 */}
          <div className="flex items-center space-x-4">
            {/* 购物车 */}
            <Link to="/cart" className="relative">
              <Button variant="ghost" size="sm" className="p-2">
                <ShoppingCart className="w-6 h-6" />
                {totalItems > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {totalItems > 99 ? '99+' : totalItems}
                  </span>
                )}
              </Button>
            </Link>

            {/* 移动端菜单按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden p-2"
              onClick={toggleMobileMenu}
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </Button>
          </div>
        </div>

        {/* 移动端搜索框 */}
        <div className="md:hidden pb-4">
          <SearchBar onSearch={onSearch} />
        </div>
      </div>

      {/* 移动端菜单 */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white border-t">
          <div className="px-4 py-4 space-y-4">
            <Link
              to="/"
              className="block text-gray-700 hover:text-primary-600 font-medium"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              首页
            </Link>
            <Link
              to="/categories"
              className="block text-gray-700 hover:text-primary-600 font-medium"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              分类
            </Link>
            <Link
              to="/cart"
              className="flex items-center justify-between text-gray-700 hover:text-primary-600 font-medium"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <span>购物车</span>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">
                  {totalItems} 件商品
                </span>
                <span className="text-sm font-medium text-primary-600">
                  {formatPrice(totalPrice)}
                </span>
              </div>
            </Link>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Header from './components/Header';
import HomePage from './pages/HomePage';
import ProductDetailPage from './pages/ProductDetailPage';

// 创建 React Query 客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
  },
});

const App: React.FC = () => {
  const handleSearch = (keyword: string) => {
    // 这里可以添加搜索逻辑，比如导航到搜索页面
    console.log('搜索关键词:', keyword);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Header onSearch={handleSearch} />
          
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/products/:id" element={<ProductDetailPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </main>
          
          <Footer />
        </div>
      </Router>
    </QueryClientProvider>
  );
};

// 404页面组件
const NotFoundPage: React.FC = () => {
  return (
    <div className="text-center py-12">
      <div className="text-gray-400 text-6xl mb-4">🔍</div>
      <h1 className="text-3xl font-bold text-gray-900 mb-2">页面未找到</h1>
      <p className="text-gray-600 mb-6">抱歉，您访问的页面不存在</p>
      <a
        href="/"
        className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
      >
        返回首页
      </a>
    </div>
  );
};

// 页脚组件
const Footer: React.FC = () => {
  return (
    <footer className="bg-white border-t mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">关于我们</h3>
            <p className="text-gray-600 text-sm">
              现代化商品展示系统，基于React和SpringBoot构建的高性能电商展示应用。
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">快速链接</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="/" className="text-gray-600 hover:text-primary-600">首页</a></li>
              <li><a href="/products" className="text-gray-600 hover:text-primary-600">商品</a></li>
              <li><a href="/categories" className="text-gray-600 hover:text-primary-600">分类</a></li>
              <li><a href="/cart" className="text-gray-600 hover:text-primary-600">购物车</a></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">技术栈</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>React 18 + TypeScript</li>
              <li>Vite + Tailwind CSS</li>
              <li>React Query + Zustand</li>
              <li>SpringBoot + MySQL</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">联系我们</h3>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>邮箱: <EMAIL></li>
              <li>电话: +86 123-4567-8900</li>
              <li>地址: 北京市朝阳区</li>
            </ul>
          </div>
        </div>
        <div className="border-t mt-8 pt-8 text-center text-sm text-gray-600">
          <p>&copy; 2024 现代化商品展示系统. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default App;

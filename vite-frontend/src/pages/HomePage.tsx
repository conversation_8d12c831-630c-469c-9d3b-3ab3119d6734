import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProducts, usePopularProducts } from '@/hooks/useProducts';
import { useCategories } from '@/hooks/useCategories';
import { Product, ProductSearchParams } from '@/types';
import ProductGrid from '@/components/ProductGrid';
import { PageLoading } from '@/components/ui/Loading';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useState<ProductSearchParams>({
    page: 0,
    size: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const { data: productsData, isLoading: productsLoading } = useProducts(searchParams);
  const { data: popularProducts, isLoading: popularLoading } = usePopularProducts(8);
  const { data: categories, isLoading: categoriesLoading } = useCategories();

  const handleProductClick = (product: Product) => {
    navigate(`/products/${product.id}`);
  };

  const handleCategoryClick = (categoryId: number) => {
    navigate(`/categories/${categoryId}`);
  };

  if (productsLoading && popularLoading && categoriesLoading) {
    return <PageLoading />;
  }

  return (
    <div className="space-y-8">
      {/* 英雄区域 */}
      <section className="gradient-bg text-white py-16 rounded-lg">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            现代化商品展示
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90">
            发现优质商品，享受购物乐趣
          </p>
          <Button
            size="lg"
            variant="secondary"
            onClick={() => navigate('/products')}
            className="text-primary-600"
          >
            开始购物
          </Button>
        </div>
      </section>

      {/* 热门分类 */}
      {categories && categories.length > 0 && (
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">热门分类</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {categories.slice(0, 6).map((category) => (
              <Card
                key={category.id}
                hover
                className="cursor-pointer text-center p-4"
                onClick={() => handleCategoryClick(category.id)}
              >
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">📦</span>
                </div>
                <h3 className="font-medium text-gray-900">{category.name}</h3>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* 热门商品 */}
      {popularProducts && popularProducts.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">热门商品</h2>
            <Button
              variant="outline"
              onClick={() => navigate('/products?sort=popular')}
            >
              查看更多
            </Button>
          </div>
          <ProductGrid
            products={popularProducts}
            loading={popularLoading}
            onProductClick={handleProductClick}
          />
        </section>
      )}

      {/* 最新商品 */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">最新商品</h2>
          <Button
            variant="outline"
            onClick={() => navigate('/products')}
          >
            查看全部
          </Button>
        </div>
        <ProductGrid
          products={productsData?.content || []}
          loading={productsLoading}
          onProductClick={handleProductClick}
        />
      </section>

      {/* 特色功能 */}
      <section className="bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 text-center mb-8">
          为什么选择我们
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-3xl">🚀</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">极速加载</h3>
            <p className="text-gray-600">
              基于现代化技术栈，提供极速的页面加载体验
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-3xl">📱</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">响应式设计</h3>
            <p className="text-gray-600">
              完美适配各种设备，随时随地享受购物乐趣
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-3xl">🔒</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">安全可靠</h3>
            <p className="text-gray-600">
              企业级安全保障，让您的购物更加安心
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
